import React from 'react';
import { Link } from 'react-router-dom';

import { Button } from '@/components/ui/button';
import { ExpandedResource } from '@/data/patient-resources/expandedResources';

interface ExpandedResourcesSectionProps {
  resources: ExpandedResource[];
  title: string;
}

const ExpandedResourcesSection: React.FC<ExpandedResourcesSectionProps> = ({
  resources, 
  title 
}) => {
  return (
    <section className="py-16">
      <div className="container max-w-7xl">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold mb-4 text-foreground">{title}</h2>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            Comprehensive resources to support your spine health journey with expert guidance and evidence-based information.
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {resources?.map((resource, index) => (
            <div 
              key={resource.id} 
              className="bg-card/50 backdrop-blur-sm border border-border/50 p-6 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 hover:scale-105 animate-fade-in" 
              style={{ animationDelay: `${index * 50}ms` }}
            >
              <div className="mb-4">
                <div className={`inline-flex px-2 py-1 rounded-full text-xs font-medium mb-3 ${getCategoryColor(resource.category)}`}>
                  {getCategoryLabel(resource.category)}
                </div>
                <h3 className="text-lg font-semibold mb-3 text-foreground leading-tight">{resource.title}</h3>
              </div>
              
              <p className="text-muted-foreground mb-6 text-sm leading-relaxed flex-1">{resource.description}</p>
              
              <Button asChild className="w-full" size="sm">
                <Link to={resource.link}>
                  {getButtonText(resource.category)}
                </Link>
              </Button>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

// Helper functions for category styling and labels
const getCategoryColor = (category: string): string => {
  const colors = {
    assessment: 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300',
    education: 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300',
    exercise: 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300',
    lifestyle: 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300',
    anatomy: 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300',
    recovery: 'bg-teal-100 text-teal-800 dark:bg-teal-900/30 dark:text-teal-300'
  };
  return colors[category as keyof typeof colors] || 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300';
};

const getCategoryLabel = (category: string): string => {
  const labels = {
    assessment: 'Assessment',
    education: 'Education',
    exercise: 'Exercise',
    lifestyle: 'Lifestyle',
    anatomy: 'Anatomy',
    recovery: 'Recovery'
  };
  return labels[category as keyof typeof labels] || 'Resource';
};

const getButtonText = (category: string): string => {
  const buttonTexts = {
    assessment: 'Start Assessment',
    education: 'Learn More',
    exercise: 'View Exercises',
    lifestyle: 'View Guide',
    anatomy: 'Explore Anatomy',
    recovery: 'Recovery Guide'
  };
  return buttonTexts[category as keyof typeof buttonTexts] || 'View Resource';
};

ExpandedResourcesSection.displayName = 'ExpandedResourcesSection';

export default ExpandedResourcesSection;

import React, { useEffect } from 'react';
import { Link } from 'react-router-dom';

import ContactForm from '@/components/contact/ContactForm';
import ContactInformation from '@/components/contact/ContactInformation';
import EmergencyContact from '@/components/contact/EmergencyContact';
import PageHeader from '@/components/PageHeader';
import StandardPageLayout from '@/components/StandardPageLayout';
import { Button } from '@/components/ui/button';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import contactData from '@/data/pages/contact';
import { cn } from '@/lib/utils';

/**
 * Refactored Contact Component
 * 
 * Original component: 758 lines
 * Refactored component: <150 lines
 * Reduction: ~80%
 */

const ContactRefactored: React.FC = () => {
  const deviceInfo = useDeviceDetection();

  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  return (
    <StandardPageLayout showHeader={false}>
      <PageHeader
        title={contactData.hero.title}
        subtitle={contactData.hero.subtitle}
        backgroundImage={contactData.hero.backgroundImage}
        enableParallax={true}
      />

      <div className="flex-1">
        {/* Contact Information & Form */}
        <section className={deviceInfo.isMobile ? "mobile-section" : "section"}>
          <div className={deviceInfo.isMobile ? "mobile-container" : "container"}>
            <div className={cn(
              deviceInfo.isMobile
                ? "grid grid-cols-1 gap-mobile-xl"
                : "grid grid-cols-1 lg:grid-cols-2 gap-12"
            )}>
              {/* Left Column - Contact Information */}
              <div>
                <ContactInformation
                  title={contactData.contactInfo.title}
                  image={contactData.contactInfo.image}
                  details={contactData.contactInfo.details}
                />

                <EmergencyContact
                  title={contactData.emergencyContact.title}
                  sections={contactData.emergencyContact.sections}
                />

                {/* GP and Specialist Referrals */}
                <h3 className="text-xl font-bold mb-4">{contactData.referrals.title}</h3>
                <div className="glass-card p-6 mb-8">
                  <div className="flex items-start mb-6">
                    <div className="flex-shrink-0 h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center mr-4">
                      <contactData.referrals.icon className="h-5 w-5 text-primary" />
                    </div>
                    <div>
                      {contactData.referrals.content.map((paragraph, index) => (
                        <p key={index} className="text-muted-foreground mb-4">
                          {paragraph}
                        </p>
                      ))}
                      <div className="mt-4">
                        <Button asChild className="bg-primary hover:bg-primary/90">
                          <Link to={contactData.referrals.buttonLink}>
                            {contactData.referrals.buttonText}
                          </Link>
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Insurance Information */}
                <h3 className="text-xl font-bold mb-4">{contactData.insurance.title}</h3>
                <div className="glass-card p-6 mb-8 space-y-6">
                  {contactData.insurance.options.map((option, index) => (
                    <div key={index} className="flex items-start">
                      <div className="flex-shrink-0 h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center mr-4">
                        <option.icon className="h-5 w-5 text-primary" />
                      </div>
                      <div>
                        <h4 className="font-semibold mb-1">{option.title}</h4>
                        <p className="text-muted-foreground">{option.description}</p>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Map */}
                <div className="aspect-video rounded-xl overflow-hidden shadow-lg">
                  <iframe
                    src={contactData.map.embedUrl}
                    width="100%"
                    height="100%"
                    style={{ border: 0 }}
                    allowFullScreen
                    loading="lazy"
                    referrerPolicy="no-referrer-when-downgrade"
                    title={contactData.map.title}
                  />
                </div>
              </div>

              {/* Right Column - Contact Form */}
              <ContactForm
                title={contactData.contactForm.title}
                image={contactData.contactForm.image}
                description={contactData.contactForm.description}
                fields={contactData.contactForm.fields}
                submitText={contactData.contactForm.submitText}
                successMessage={contactData.contactForm.successMessage}
              />
            </div>
          </div>
        </section>
      </div>
    </StandardPageLayout>
  );
};

ContactRefactored.displayName = 'ContactRefactored';

export default ContactRefactored;

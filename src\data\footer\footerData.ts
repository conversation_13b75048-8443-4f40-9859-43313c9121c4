export interface SocialLink {
  id: string;
  name: string;
  url: string;
  icon: string;
  ariaLabel: string;
}

export interface FooterLink {
  name: string;
  path: string;
}

export interface ContactInfo {
  address: {
    street: string;
    city: string;
    state: string;
    postcode: string;
    country: string;
  };
  phone: {
    number: string;
    href: string;
    ariaLabel: string;
  };
  email: {
    address: string;
    href: string;
    ariaLabel: string;
  };
}

export interface FooterSection {
  id: string;
  title: string;
  links: FooterLink[];
}

export interface FooterConfiguration {
  companyInfo: {
    name: string;
    description: string;
  };
  socialLinks: SocialLink[];
  sections: FooterSection[];
  contactInfo: ContactInfo;
  legalLinks: FooterLink[];
  copyright: {
    year: number;
    text: string;
  };
}

interface TranslationObject {
  footer?: {
    description?: string;
    quickLinks?: string;
    contactInfo?: string;
    followUs?: string;
    copyright?: { year?: number; text?: string };
  };
  nav?: Record<string, string>;
}

export const getFooterData = (t?: TranslationObject): FooterConfiguration => {

  // Safe fallback for translations - use correct translation structure
  const finalT = (t && t.footer && t.navigation && t.patientResources) ? t : {
    footer: {
      description: "Dr. Ales Aliashkevich is a specialist in minimally invasive neurosurgery and spine surgery, using the latest technology to provide the best outcomes for patients.",
      quickLinks: "Quick Links",
      contactInfo: "Contact Info",
      followUs: "Follow Us",
      copyright: "All rights reserved.",
      privacyPolicy: "Privacy Policy",
      termsOfService: "Terms of Service",
      accessibility: "Accessibility",
      sitemap: "Sitemap"
    },
    navigation: {
      home: "Home",
      expertise: "Expertise",
      about: "About",
      locations: "Locations",
      patientResources: "Patient Resources",
      contact: "Contact",
      bookAppointment: "Book Appointment",
      language: "Language",
      menu: "Menu",
      close: "Close",
      skipToContent: "Skip to Content"
    },
    patientResources: {
      title: "Patient Resources",
      subtitle: "Helpful Information",
      description: "Resources for patients",
      learnMore: "Learn More"
    }
  };

  return {
    companyInfo: {
      name: "Dr. Ales Aliashkevich",
      description: finalT.footer.description
    },
    socialLinks: [
      {
        id: 'facebook',
        name: 'Facebook',
        url: 'https://www.facebook.com/mineuro.com.au',
        icon: 'Facebook',
        ariaLabel: 'Follow us on Facebook'
      },
      {
        id: 'instagram',
        name: 'Instagram',
        url: 'https://www.instagram.com/mineuro.com.au',
        icon: 'Instagram',
        ariaLabel: 'Follow us on Instagram'
      },
      {
        id: 'twitter',
        name: 'Twitter',
        url: 'https://twitter.com/mineuro_au',
        icon: 'Twitter',
        ariaLabel: 'Follow us on Twitter'
      }
    ],
    sections: [
      {
        id: 'quick-links',
        title: finalT.footer.quickLinks,
        links: [
          { name: finalT.navigation.home, path: "/" },
          { name: finalT.navigation.expertise, path: "/expertise" },
          { name: finalT.patientResources.title, path: "/patient-resources" },
          { name: "Appointments", path: "/appointments" },
          { name: "GP Resources", path: "/gp-resources" },
          { name: finalT.navigation.locations, path: "/locations" },
          { name: finalT.navigation.contact, path: "/contact" }
        ]
      },
      {
        id: 'locations',
        title: finalT.navigation.locations,
        links: [
          { name: "Surrey Hills", path: "/locations/surrey-hills" },
          { name: "Mornington", path: "/locations/mornington" },
          { name: "Frankston", path: "/locations/frankston" },
          { name: "Moonee Ponds", path: "/locations/moonee-ponds" },
          { name: "View All Locations", path: "/locations" }
        ]
      }
    ],
    contactInfo: {
      address: {
        street: "Suite 4, 619 Canterbury Road",
        city: "Surrey Hills",
        state: "VIC",
        postcode: "3127",
        country: "Australia"
      },
      phone: {
        number: "(03) 9008 4200",
        href: "tel:+***********",
        ariaLabel: "Call us at (03) 9008 4200"
      },
      email: {
        address: "<EMAIL>",
        href: "mailto:<EMAIL>",
        ariaLabel: "Email <NAME_EMAIL>"
      }
    },
    legalLinks: [
      { name: "Privacy Policy", path: "/privacy-policy" },
      { name: "Terms & Conditions", path: "/terms-conditions" }
    ],
    copyright: {
      year: new Date().getFullYear(),
      text: finalT.footer.copyright
    }
  };
};

import React from 'react';

import DesktopNavigation from '@/components/navigation/DesktopNavigation';
import MobileNavigation from '@/components/navigation/MobileNavigation';
import NavbarHeader from '@/components/navigation/NavbarHeader';
import { useLanguage } from '@/contexts/LanguageContext';
import { getNavigationData } from '@/data/navigation/navigationData';
import { useNavigation } from '@/hooks/useNavigation';
import en from '@/locales/en';

/**
 * Refactored Navbar Component
 * 
 * Original component: 416 lines
 * Refactored component: <50 lines
 * Reduction: ~88%
 */

const NavbarRefactored: React.FC = () => {
  const { t } = useLanguage();
  const {
    mobileMenuOpen,
    scrolled,
    activeSubmenu,
    toggleMobileMenu,
    closeMobileMenu,
    closeSubmenu,
    handleSubmenuEnter,
    handleSubmenuLeave,
    createMobileClickHandler,
    createKeyDownHandler
  } = useNavigation();

  // Get navigation configuration with translations
  const safeT = t || en;
  const navigationConfig = getNavigationData(safeT);

  return (
    <NavbarHeader scrolled={scrolled}>
      <DesktopNavigation
        navLinks={navigationConfig.mainLinks}
        ctaButton={navigationConfig.ctaButton}
        activeSubmenu={activeSubmenu}
        onSubmenuEnter={handleSubmenuEnter}
        onSubmenuLeave={handleSubmenuLeave}
        onKeyDown={createKeyDownHandler}
        onCloseSubmenu={closeSubmenu}
      />

      <MobileNavigation
        navLinks={navigationConfig.mainLinks}
        ctaButton={navigationConfig.ctaButton}
        mobileMenuOpen={mobileMenuOpen}
        activeSubmenu={activeSubmenu}
        onToggleMobileMenu={toggleMobileMenu}
        onCloseMobileMenu={closeMobileMenu}
        onMobileClick={createMobileClickHandler}
      />
    </NavbarHeader>
  );
};

NavbarRefactored.displayName = 'NavbarRefactored';

export default NavbarRefactored;

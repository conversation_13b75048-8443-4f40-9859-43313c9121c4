import React from 'react';

import FooterRefactored from '@/components/FooterRefactored';
import {
  LocationHero,
  LocationContactInfo,
  LocationMap,
  LocationFacilities,
  LocationCTA
} from '@/components/locations';
import NavbarRefactored from '@/components/NavbarRefactored';
import { useLocationData } from '@/hooks/useLocationData';

const FrankstonLocationRefactored: React.FC = () => {
  const locationData = useLocationData('frankstonLocation');

  // Frankston specific data
  const frankstonData = {
    ...locationData,
    contact: {
      ...locationData.contact,
      address: {
        street: 'Frankston Medical Centre',
        suburb: 'Frankston',
        state: 'VIC',
        postcode: '3199'
      },
      phone: '03 90084200',
      email: '<EMAIL>',
      hours: {
        weekdays: 'Monday to Friday: 8:30 AM - 5:30 PM',
        note: 'Consultations by appointment only'
      }
    },
    map: {
      ...locationData.map,
      embedUrl: 'https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3151.8351288553624!2d145.09308731531866!3d-37.***********!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x6ad646b5d2ba4273%3A0x4045675218ccd90!2sFrankston%20VIC%203199%2C%20Australia!5e0!3m2!1sen!2sus!4v1650000000000!5m2!1sen!2sus',
      transportOptions: {
        publicTransport: 'Frankston is well-connected by train and bus services. The Frankston train station is on the Frankston line, providing direct access to Melbourne CBD.',
        car: 'Convenient parking available with easy access from the Mornington Peninsula Freeway and Nepean Highway.'
      }
    },
    facilities: {
      title: 'Our Frankston Facilities',
      subtitle: 'Comprehensive healthcare services in the heart of Frankston',
      description: 'Our Frankston location provides state-of-the-art medical facilities designed to deliver exceptional patient care in a comfortable and accessible environment.',
      items: [
        {
          icon: (
            <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
            </svg>
          ),
          title: 'Modern Consultation Rooms',
          description: 'Fully equipped consultation rooms with the latest medical technology and comfortable patient amenities.'
        },
        {
          icon: (
            <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          ),
          title: 'Comprehensive Care',
          description: 'Full range of neurosurgical and spine care services with access to advanced diagnostic equipment.'
        },
        {
          icon: (
            <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
            </svg>
          ),
          title: 'Convenient Location',
          description: 'Easily accessible location with excellent transport links and ample parking facilities.'
        }
      ]
    }
  };

  return (
    <div className="min-h-screen flex flex-col">
      <NavbarRefactored />
      
      <div className="flex-1 pt-20">
        <LocationHero
          title={frankstonData.hero.title}
          subtitle={frankstonData.hero.subtitle}
          introduction1={frankstonData.hero.introduction1}
          introduction2={frankstonData.hero.introduction2}
          introduction3={frankstonData.hero.introduction3}
        />

        <div className="py-16 bg-muted/30">
          <div className="container">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <LocationContactInfo
                address={frankstonData.contact.address}
                phone={frankstonData.contact.phone}
                email={frankstonData.contact.email}
                hours={frankstonData.contact.hours}
                consultingHours={{
                  title: 'Consulting Hours',
                  details: 'Monday to Friday: 8:30 AM - 5:30 PM',
                  appointmentNote: 'Consultations are by appointment only. Please call our office to schedule an appointment.',
                  urgentNote: 'Urgent appointments are available on request.'
                }}
                appointmentProcess={{
                  title: 'Appointment Process',
                  details1: 'Before your appointment, our office will coordinate with your GP to obtain necessary referrals and medical information.',
                  details2: 'New patients will complete a comprehensive registration form to help us provide the most appropriate care.'
                }}
              />

              <LocationMap
                embedUrl={frankstonData.map.embedUrl}
                title="Frankston"
                transportOptions={frankstonData.map.transportOptions}
              />
            </div>
          </div>
        </div>

        {frankstonData.facilities && (
          <LocationFacilities
            title={frankstonData.facilities.title}
            subtitle={frankstonData.facilities.subtitle}
            description={frankstonData.facilities.description}
            facilities={frankstonData.facilities.items}
          />
        )}

        <LocationCTA
          title={frankstonData.cta.title}
          description={frankstonData.cta.description}
          buttons={frankstonData.cta.buttons}
        />
      </div>

      <FooterRefactored />
    </div>
  );
};

FrankstonLocationRefactored.displayName = 'FrankstonLocationRefactored';

export default FrankstonLocationRefactored;

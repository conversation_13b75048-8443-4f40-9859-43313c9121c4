# Dr<PERSON> <PERSON><PERSON> Neurosurgery Website - Production Environment
# IMPORTANT: Replace placeholder values with actual production values

# Application Environment (REQUIRED)
VITE_APP_ENV=production
VITE_APP_NAME="miNEURO Brain and Spine Surgery"
VITE_APP_VERSION=1.0.0

# Website Configuration (REQUIRED)
VITE_SITE_URL=https://mineuro.com.au
VITE_API_BASE_URL=https://api.mineuro.com.au
VITE_CDN_URL=https://cdn.mineuro.com.au

# Contact Information (REQUIRED)
VITE_PRACTICE_PHONE="+61 3 9008 4200"
VITE_PRACTICE_FAX="+61 3 9923 6688"
VITE_PRACTICE_EMAIL="<EMAIL>"
VITE_ARGUS_EMAIL="<EMAIL>"
VITE_HEALTHLINK_ID="mineuros"

# Social Media (Optional)
VITE_FACEBOOK_URL="https://www.facebook.com/mineuro"
VITE_LINKEDIN_URL="https://www.linkedin.com/company/mineuro"
VITE_TWITTER_HANDLE="@mineuro"

# Analytics and Tracking (Recommended for Production)
VITE_GOOGLE_ANALYTICS_ID=G-XXXXXXXXXX
VITE_GOOGLE_TAG_MANAGER_ID=GTM-XXXXXXX
VITE_HOTJAR_ID=
VITE_FACEBOOK_PIXEL_ID=

# Maps and Location Services (Recommended)
VITE_GOOGLE_MAPS_API_KEY=AIzaSyXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
VITE_MAPBOX_ACCESS_TOKEN=

# Email Services (Required for Contact Forms)
VITE_EMAILJS_SERVICE_ID=service_xxxxxxx
VITE_EMAILJS_TEMPLATE_ID=template_xxxxxxx
VITE_EMAILJS_PUBLIC_KEY=xxxxxxxxxxxxxxx

# Error Reporting (Recommended for Production)
VITE_SENTRY_DSN=https://<EMAIL>/xxxxxxx
VITE_SENTRY_ENVIRONMENT=production
VITE_ERROR_REPORTING_ENDPOINT=https://api.mineuro.com.au/errors
VITE_ERROR_REPORTING_API_KEY=

# Performance Monitoring
VITE_ENABLE_PERFORMANCE_MONITORING=true
VITE_ENABLE_ERROR_BOUNDARY=true

# Feature Flags
VITE_ENABLE_DARK_MODE=false
VITE_ENABLE_LANGUAGE_SWITCHING=true
VITE_ENABLE_APPOINTMENT_BOOKING=true
VITE_ENABLE_LIVE_CHAT=true

# Security Configuration (Production)
VITE_ENABLE_CSP=true
VITE_ENABLE_HSTS=true

# Development Only (MUST be false in production)
VITE_DEBUG_MODE=false
VITE_SHOW_PERFORMANCE_METRICS=false

# Production Optimization Flags
VITE_NODE_ENV=production
VITE_MINIFY=true
VITE_SOURCEMAP=false

# Production Security Flags
VITE_DISABLE_CONSOLE=true
VITE_DISABLE_DEVTOOLS=true
VITE_STRICT_MODE=true

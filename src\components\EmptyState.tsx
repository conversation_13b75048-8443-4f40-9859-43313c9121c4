
// TODO: Extract inline object types to interfaces
// Consider creating interfaces for complex object types
import {
  Search,
  FileX,
  Wifi,
  AlertCircle,
  RefreshCw,
  Plus,
  Calendar,
  MessageSquare,
  Heart,
  Star,
  LucideIcon
} from 'lucide-react';
import React from 'react';

import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

export type EmptyStateType = 
  | 'no-data'
  | 'no-results' 
  | 'no-content'
  | 'network-error'
  | 'loading-error'
  | 'access-denied'
  | 'coming-soon'
  | 'maintenance'
  | 'no-reviews'
  | 'no-testimonials'
  | 'no-appointments'
  | 'no-procedures'
  | 'no-conditions';

export interface EmptyStateAction {
  label: string;
  onClick: () => void;
  variant?: 'default' | 'outline' | 'secondary';
  icon?: LucideIcon;
}

export interface EmptyStateProps {
  type?: EmptyStateType;
  title?: string;
  description?: string;
  icon?: LucideIcon;
  illustration?: string;
  actions?: EmptyStateAction[];
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  showCard?: boolean;
  children?: React.ReactNode;
}

const emptyStateConfig: Record<EmptyStateType, {
  icon: LucideIcon;
  title: string;
  description: string;
}> = {
  'no-data': {
    icon: FileX,
    title: 'No Data Available',
    description: 'There is currently no data to display. Please check back later or contact support if this persists.'
  },
  'no-results': {
    icon: Search,
    title: 'No Results Found',
    description: 'We couldn\'t find any results matching your search criteria. Try adjusting your filters or search terms.'
  },
  'no-content': {
    icon: FileX,
    title: 'No Content Available',
    description: 'This section is currently empty. Content will be added soon.'
  },
  'network-error': {
    icon: Wifi,
    title: 'Connection Error',
    description: 'Unable to load content due to a network issue. Please check your connection and try again.'
  },
  'loading-error': {
    icon: AlertCircle,
    title: 'Loading Error',
    description: 'Something went wrong while loading this content. Please try refreshing the page.'
  },
  'access-denied': {
    icon: AlertCircle,
    title: 'Access Denied',
    description: 'You don\'t have permission to view this content. Please contact support if you believe this is an error.'
  },
  'coming-soon': {
    icon: Calendar,
    title: 'Coming Soon',
    description: 'This feature is currently under development and will be available soon.'
  },
  'maintenance': {
    icon: AlertCircle,
    title: 'Under Maintenance',
    description: 'This section is temporarily unavailable due to maintenance. Please try again later.'
  },
  'no-reviews': {
    icon: Star,
    title: 'No Reviews Yet',
    description: 'Be the first to leave a review and help others learn about this service.'
  },
  'no-testimonials': {
    icon: MessageSquare,
    title: 'No Testimonials Available',
    description: 'Patient testimonials will be displayed here once they become available.'
  },
  'no-appointments': {
    icon: Calendar,
    title: 'No Appointments Scheduled',
    description: 'You don\'t have any upcoming appointments. Schedule one to get started.'
  },
  'no-procedures': {
    icon: Heart,
    title: 'No Procedures Found',
    description: 'No medical procedures match your current search criteria.'
  },
  'no-conditions': {
    icon: Heart,
    title: 'No Conditions Found',
    description: 'No medical conditions match your current search criteria.'
  }
};

/**
 * Reusable Empty State Component
 * Provides consistent empty state UI across the application
 */
export const EmptyState: React.FC<EmptyStateProps> = ({
  type = 'no-data',
  title,
  description,
  icon,
  illustration,
  actions = [],
  className,
  size = 'md',
  showCard = false,
  children
}) => {
  const deviceInfo = useDeviceDetection();
  const config = emptyStateConfig[type];
  
  const IconComponent = icon || config.icon;
  const finalTitle = title || config.title;
  const finalDescription = description || config.description;

  const sizeClasses = {
    sm: {
      container: deviceInfo.isMobile ? 'py-8' : 'py-12',
      icon: 'h-12 w-12',
      title: deviceInfo.isMobile ? 'text-lg' : 'text-xl',
      description: deviceInfo.isMobile ? 'text-sm' : 'text-base',
      spacing: 'space-y-3'
    },
    md: {
      container: deviceInfo.isMobile ? 'py-12' : 'py-16',
      icon: 'h-16 w-16',
      title: deviceInfo.isMobile ? 'text-xl' : 'text-2xl',
      description: deviceInfo.isMobile ? 'text-base' : 'text-lg',
      spacing: 'space-y-4'
    },
    lg: {
      container: deviceInfo.isMobile ? 'py-16' : 'py-24',
      icon: 'h-20 w-20',
      title: deviceInfo.isMobile ? 'text-2xl' : 'text-3xl',
      description: deviceInfo.isMobile ? 'text-lg' : 'text-xl',
      spacing: 'space-y-6'
    }
  };

  const currentSize = sizeClasses[size];

  const content = (
    <div 
      className={cn(
        'text-center max-w-md mx-auto',
        currentSize.container,
        currentSize.spacing,
        className
      )}
      role="status"
      aria-live="polite"
    >
      {/* Icon or Illustration */}
      <div className="flex justify-center mb-4">
        {illustration ? (
          <img 
            src={illustration} 
            alt="" 
            className={cn(currentSize.icon, 'object-contain')}
            aria-hidden="true"
          />
        ) : (
          <div className={cn(
            'rounded-full bg-muted flex items-center justify-center',
            currentSize.icon
          )}>
            <IconComponent 
              className={cn(
                'text-muted-foreground',
                size === 'sm' ? 'h-6 w-6' : size === 'md' ? 'h-8 w-8' : 'h-10 w-10'
              )}
              aria-hidden="true"
            />
          </div>
        )}
      </div>

      {/* Title */}
      <h3 className={cn(
        'font-semibold text-foreground',
        currentSize.title
      )}>
        {finalTitle}
      </h3>

      {/* Description */}
      <p className={cn(
        'text-muted-foreground',
        currentSize.description
      )}>
        {finalDescription}
      </p>

      {/* Custom Content */}
      {children && (
        <div className="mt-4">
          {children}
        </div>
      )}

      {/* Actions */}
      {actions.length > 0 && (
        <div className={cn(
          'flex gap-3 justify-center mt-6',
          deviceInfo.isMobile ? 'flex-col' : 'flex-row'
        )}>
          {actions.map((action, index) => {
            const ActionIcon = action.icon;
            return (
              <Button
                key={index}
                variant={action.variant || 'default'}
                onClick={action.onClick}
                className={cn(
                  deviceInfo.isMobile ? 'w-full' : 'min-w-[120px]'
                )}
              >
                {ActionIcon && <ActionIcon className="h-4 w-4 mr-2" />}
                {action.label}
              </Button>
            );
          })}
        </div>
      )}
    </div>
  );

  if (showCard) {
    return (
      <Card className="border-dashed">
        <CardContent className="p-0">
          {content}
        </CardContent>
      </Card>
    );
  }

  return content;
};

/**
 * Specialized Empty State Components
 */

export const NoResultsState: React.FC<Omit<EmptyStateProps, 'type'> & {
  searchTerm?: string;
  onClearSearch?: () => void;
}> = ({ searchTerm, onClearSearch, ...props }) => {
  const actions: EmptyStateAction[] = [];
  
  if (onClearSearch) {
    actions.push({
      label: 'Clear Search',
      onClick: onClearSearch,
      variant: 'outline',
      icon: RefreshCw
    });
  }

  return (
    <EmptyState
      type="no-results"
      title={searchTerm ? `No results for "${searchTerm}"` : undefined}
      actions={[...actions, ...(props.actions || [])]}
      {...props}
    />
  );
};

export const NetworkErrorState: React.FC<Omit<EmptyStateProps, 'type'> & {
  onRetry?: () => void;
}> = ({ onRetry, ...props }) => {
  const actions: EmptyStateAction[] = [];
  
  if (onRetry) {
    actions.push({
      label: 'Try Again',
      onClick: onRetry,
      icon: RefreshCw
    });
  }

  return (
    <EmptyState
      type="network-error"
      actions={[...actions, ...(props.actions || [])]}
      {...props}
    />
  );
};

export const LoadingErrorState: React.FC<Omit<EmptyStateProps, 'type'> & {
  onRetry?: () => void;
}> = ({ onRetry, ...props }) => {
  const actions: EmptyStateAction[] = [];
  
  if (onRetry) {
    actions.push({
      label: 'Refresh Page',
      onClick: onRetry,
      icon: RefreshCw
    });
  }

  return (
    <EmptyState
      type="loading-error"
      actions={[...actions, ...(props.actions || [])]}
      {...props}
    />
  );
};

export const CreateFirstState: React.FC<Omit<EmptyStateProps, 'type'> & {
  itemType: string;
  onCreate?: () => void;
}> = ({ itemType, onCreate, ...props }) => {
  const actions: EmptyStateAction[] = [];
  
  if (onCreate) {
    actions.push({
      label: `Create First ${itemType}`,
      onClick: onCreate,
      icon: Plus
    });
  }

  return (
    <EmptyState
      type="no-data"
      title={`No ${itemType}s Yet`}
      description={`Get started by creating your first ${itemType.toLowerCase()}.`}
      actions={[...actions, ...(props.actions || [])]}
      {...props}
    />
  );
};

EmptyState.displayName = 'EmptyState';
NoResultsState.displayName = 'NoResultsState';
NetworkErrorState.displayName = 'NetworkErrorState';
LoadingErrorState.displayName = 'LoadingErrorState';
CreateFirstState.displayName = 'CreateFirstState';

export default EmptyState;

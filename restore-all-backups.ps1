# PowerShell script to restore all backup files

Write-Host "🔄 RESTORING ALL BACKUP FILES" -ForegroundColor Yellow
Write-Host "=" * 50

# Get all backup files recursively
$backupFiles = Get-ChildItem -Path . -Filter "*.backup" -Recurse

Write-Host "Found $($backupFiles.Count) backup files to restore..." -ForegroundColor Green

foreach ($backupFile in $backupFiles) {
    # Get the original file path by removing .backup extension
    $originalFile = $backupFile.FullName -replace '\.backup$', ''
    
    try {
        # Copy backup to original
        Copy-Item $backupFile.FullName $originalFile -Force
        Write-Host "✅ Restored: $($backupFile.Name -replace '\.backup$', '')" -ForegroundColor Green
    }
    catch {
        Write-Host "❌ Failed to restore: $($backupFile.Name)" -ForegroundColor Red
        Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`n🎉 RESTORATION COMPLETE!" -ForegroundColor Yellow
Write-Host "All backup files have been restored to their original locations." -ForegroundColor Green
Write-Host "You can now try building again with: npm run build" -ForegroundColor Cyan

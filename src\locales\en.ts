import { Translations } from '@/types/translations';

const en: Translations = {
  navigation: {
    home: "Home",
    about: "About",
    expertise: "Expertise",
    locations: "Locations",
    patientResources: "Patient Resources",
    contact: "Contact",
    bookAppointment: "Book Appointment",
    language: "Language",
    menu: "Menu",
    close: "Close",
    skipTo<PERSON>ontent: "Skip to Content"
  },
  hero: {
    title: "Neurosurgical Expertise and Innovative Technology for Superior Brain and Spine Surgery Results",
    subtitle: "THE GOLD STANDARD FOR BRAIN AND SPINE SURGERY",
    description: "<PERSON> specialises in future-minded treatment of conditions affecting the brain, spine and peripheral nerves.",
    bookConsultation: "Book Consultation",
    exploreTreatments: "Explore Treatment Options",
    scrollDown: "Scroll Down"
  },
  about: {
    title: "About Dr. <PERSON><PERSON>",
    subtitle: "Specialist Neurosurgeon",
    description: "Dr <PERSON> specialises in future-minded treatment of conditions affecting the brain, spine and peripheral nerves. He has more than 30 years of experience in neurosurgery and utilises safe minimally invasive technologies and innovations dedicated to restoring and improving the patient's lifestyle.",
    experience: "30+ Years Experience",
    patients: "5000+ Patients Treated",
    procedures: "Advanced Procedures",
    awards: "Medical Excellence",
    learnMore: "Learn More"
  },
  expertise: {
    title: "Neurosurgical Specialties",
    subtitle: "Advanced procedures using cutting-edge technology",
    brainSurgery: {
      title: "Brain Conditions",
      description: "Brain tumours, hydrocephalus, cerebral aneurysms, arterio-venous malformations, cavernomas, epilepsy, trigeminal neuralgia, Chiari malformations."
    },
    spineSurgery: {
      title: "Spinal Problems",
      description: "Neck and back pain, sciatica, brachialgia, spinal stenosis, disc prolapse, spondylolisthesis, spondylitis, epidural abscess, instability, myelopathy."
    },
    peripheralNerve: {
      title: "Nerve Problems",
      description: "Nerve tumours, nerve pain, nerve injuries, nerve compression, carpal tunnel syndrome, cubital tunnel syndrome, nerve and muscle biopsies."
    },
    viewAll: "View All Specialties",
    learnMore: "Learn More"
  },
  testimonials: {
    title: "Patient Testimonials",
    description: "Discover why our patients trust Dr Ales Aliashkevich for neurosurgical and spine procedures.",
    readMore: "Read More",
    previous: "Previous",
    next: "Next"
  },
  cta: {
    title: "Ready to Address Your Spine or Brain Issue?",
    description: "Book a consultation today and learn how we can improve your quality of life using minimally-invasive techniques.",
    primaryButton: "Schedule Your Consultation",
    secondaryButton: "Learn More",
    phone: "Call Us",
    email: "Email Us"
  },
  footer: {
    quickLinks: "Quick Links",
    contactInfo: "Contact Information",
    followUs: "Follow Us",
    copyright: "All rights reserved.",
    privacyPolicy: "Privacy Policy",
    termsOfService: "Terms of Service",
    accessibility: "Accessibility",
    sitemap: "Sitemap"
  },
  specialties: {
    title: "Neurosurgical Specialties",
    description: "Advanced procedures using cutting-edge technology for brain, spine, and nerve conditions",
    filters: {
      location: "Treatment Area",
      allLocations: "All Areas",
      complexity: "Procedure Complexity",
      anyComplexity: "Any complexity",
      moderate: "Moderate (6+)",
      advanced: "Advanced (7+)",
      complex: "Complex (8+)",
      highlyComplex: "Highly Complex (9+)",
      recoveryTime: "Recovery Time (days)",
      days: "days",
      resetFilters: "Reset Filters",
      showing: "Showing",
      of: "of",
      procedures: "procedures",
      noMatch: "No procedures match your filters",
      adjustFilters: "Try adjusting your filters to see more results"
    }
  },
  patientResources: {
    title: "Patient Resources",
    description: "Helpful information and resources for patients before, during, and after treatment.",
    filters: {
      category: "Category",
      allCategories: "All Categories",
      difficulty: "Difficulty",
      allDifficulties: "All Difficulties",
      duration: "Duration",
      allDurations: "All Durations",
      equipment: "Equipment",
      allEquipment: "All Equipment",
      showing: "Showing",
      of: "of",
      exercises: "exercises",
      resources: "resources",
      noMatch: "No resources match your filters",
      adjustFilters: "Try adjusting your filters to see more results",
      viewDetails: "View Details",
      more: "more"
    },
    categories: {
      preOperative: "Pre-Operative",
      postOperative: "Post-Operative",
      rehabilitation: "Rehabilitation",
      education: "Education",
      lifestyle: "Lifestyle"
    }
  },
  contact: {
    title: "Contact Us",
    subtitle: "Get in touch with our team",
    form: {
      name: "Full Name",
      email: "Email Address",
      phone: "Phone Number",
      subject: "Subject",
      message: "Message",
      submit: "Send Message",
      sending: "Sending...",
      success: "Message sent successfully!",
      error: "Failed to send message. Please try again.",
      required: "This field is required",
      invalidEmail: "Please enter a valid email address",
      invalidPhone: "Please enter a valid phone number"
    },
    info: {
      address: "Address",
      phone: "Phone",
      email: "Email",
      hours: "Hours",
      emergency: "Emergency"
    },
    map: {
      title: "Find Us",
      directions: "Get Directions",
      parking: "Parking Available"
    }
  },
  location: {
    title: "Our Locations",
    description: "Find a convenient location near you",
    address: "Address",
    phone: "Phone",
    directions: "Get Directions",
    parking: "Parking",
    publicTransport: "Public Transport",
    facilities: "Facilities",
    services: "Services",
    hours: "Opening Hours",
    bookAppointment: "Book Appointment",
    virtualTour: "Virtual Tour"
  },
  appointment: {
    title: "Book Your Consultation",
    subtitle: "Complete your appointment booking in a few simple steps",
    steps: {
      selectService: "Select Service",
      chooseDateTime: "Choose Date & Time",
      personalInfo: "Personal Information",
      confirmation: "Confirmation"
    },
    form: {
      service: "Service",
      date: "Date",
      time: "Time",
      firstName: "First Name",
      lastName: "Last Name",
      email: "Email",
      phone: "Phone",
      dateOfBirth: "Date of Birth",
      gender: "Gender",
      address: "Address",
      emergencyContact: "Emergency Contact",
      medicalHistory: "Medical History",
      currentMedications: "Current Medications",
      allergies: "Allergies",
      insuranceProvider: "Insurance Provider",
      policyNumber: "Policy Number",
      referringPhysician: "Referring Physician",
      reasonForVisit: "Reason for Visit",
      preferredLanguage: "Preferred Language",
      specialRequests: "Special Requests",
      submit: "Book Appointment",
      back: "Back",
      next: "Next",
      booking: "Booking...",
      success: "Appointment booked successfully!",
      error: "Failed to book appointment. Please try again."
    },
    validation: {
      required: "This field is required",
      invalidEmail: "Please enter a valid email address",
      invalidPhone: "Please enter a valid phone number",
      invalidDate: "Please select a valid date",
      pastDate: "Date cannot be in the past",
      weekendNotAvailable: "Weekend appointments not available",
      timeSlotUnavailable: "This time slot is not available"
    }
  },
  expertisePage: {
    hero: {
      title: "Neurosurgical Expertise",
      subtitle: "Explore our neurosurgical specialties from spine surgery to brain tumor removal."
    },
    introduction: {
      paragraph1: "is a specialist neurosurgeon and spine surgeon with extensive experience in treating complex brain, spine, and nerve conditions.",
      paragraph2: "utilises the latest minimally invasive techniques and state-of-the-art technology to provide optimal patient outcomes.",
      paragraph3: "His comprehensive approach combines advanced surgical techniques with personalised patient care to achieve the best possible results."
    },
    specializedProcedures: {
      cervicalDisc: {
        description: "Motion-preserving cervical disc replacement surgery to treat neck pain and restore natural mobility."
      },
      lumbarDisc: {
        description: "Advanced lumbar disc replacement procedures to address lower back pain and preserve spinal motion."
      },
      imageGuided: {
        description: "Precision image-guided surgery using advanced navigation systems for optimal surgical accuracy."
      },
      roboticSpine: {
        description: "State-of-the-art robotic-assisted spine surgery for enhanced precision and minimal invasiveness."
      }
    },
    surgicalApproach: {
      title: "Our Surgical Approach",
      subtitle: " follows a systematic approach to neurosurgical care:",
      principles: {
        identification: {
          title: "Accurate Identification",
          description: "Precise diagnosis using advanced imaging and diagnostic techniques to identify the exact problem."
        },
        access: {
          title: "Minimally Invasive Access",
          description: "Safe access to the pathological area with minimal injury to surrounding healthy tissues."
        },
        repair: {
          title: "Effective Repair",
          description: "Delicate and effective repair of complex structures using the latest surgical techniques."
        }
      },
      cta: "Schedule Your Consultation"
    }
  },
  common: {
    loading: "Loading...",
    error: "Error",
    retry: "Retry",
    cancel: "Cancel",
    save: "Save",
    edit: "Edit",
    delete: "Delete",
    confirm: "Confirm",
    yes: "Yes",
    no: "No",
    ok: "OK",
    close: "Close",
    back: "Back",
    next: "Next",
    previous: "Previous",
    submit: "Submit",
    search: "Search",
    filter: "Filter",
    sort: "Sort",
    clear: "Clear",
    reset: "Reset",
    apply: "Apply",
    viewMore: "View More",
    viewLess: "View Less",
    showAll: "Show All",
    hideAll: "Hide All",
    expand: "Expand",
    collapse: "Collapse",
    required: "Required",
    optional: "Optional",
    select: "Select",
    upload: "Upload",
    download: "Download",
    print: "Print",
    share: "Share",
    copy: "Copy",
    copied: "Copied!",
    email: "Email",
    phone: "Phone",
    address: "Address",
    website: "Website",
    socialMedia: "Social Media"
  }
};

export default en;

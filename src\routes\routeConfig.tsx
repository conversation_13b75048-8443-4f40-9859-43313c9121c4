import React from 'react';
import { Navigate } from 'react-router-dom';

import { logRoute } from '@/lib/dev-console';

import { ROUTE_PATHS, ROUTE_REDIRECTS } from './route-definitions';
import { registerRoutes, RouteLoaderRegistry } from './route-loader';

// Register all route loaders
registerRoutes({
  // Core pages
  [ROUTE_PATHS.HOME]: () => import('@/pages/Index'),
  [ROUTE_PATHS.APPOINTMENTS]: () => import('@/pages/Appointments'),
  [ROUTE_PATHS.EXPERTISE]: () => import('@/pages/Expertise'),
  [ROUTE_PATHS.PATIENT_RESOURCES]: () => import('@/pages/PatientResourcesUnified'),
  [ROUTE_PATHS.CONTACT]: () => import('@/pages/Contact'),
  [ROUTE_PATHS.FAQ]: () => import('@/pages/Faq'),
  [ROUTE_PATHS.PRIVACY_POLICY]: () => import('@/pages/PrivacyPolicy'),
  [ROUTE_PATHS.TERMS_CONDITIONS]: () => import('@/pages/TermsConditions'),
  [ROUTE_PATHS.SPECIALTIES]: () => import('@/pages/Specialties'),
  [ROUTE_PATHS.MEDICOLEGAL]: () => import('@/pages/Medicolegal'),
  [ROUTE_PATHS.LOCATIONS]: () => import('@/pages/Locations'),
  [ROUTE_PATHS.GALLERY]: () => import('@/pages/Gallery'),
  [ROUTE_PATHS.CONSULTING_ROOMS]: () => import('@/pages/ConsultingRoomsRefactored'),
  [ROUTE_PATHS.GP_RESOURCES]: () => import('@/pages/GPResources'),

  // Location Routes
  [ROUTE_PATHS.LOCATION_ROUTES.SURREY_HILLS]: () => import('@/pages/locations/surrey-hills'),
  [ROUTE_PATHS.LOCATION_ROUTES.MORNINGTON]: () => import('@/pages/locations/mornington'),
  [ROUTE_PATHS.LOCATION_ROUTES.FRANKSTON]: () => import('@/pages/locations/frankston'),
  [ROUTE_PATHS.LOCATION_ROUTES.LANGWARRIN]: () => import('@/pages/locations/langwarrin'),
  [ROUTE_PATHS.LOCATION_ROUTES.BUNDOORA]: () => import('@/pages/locations/bundoora'),
  [ROUTE_PATHS.LOCATION_ROUTES.WERRIBEE]: () => import('@/pages/locations/werribee'),
  [ROUTE_PATHS.LOCATION_ROUTES.HEIDELBERG]: () => import('@/pages/locations/heidelberg'),
  [ROUTE_PATHS.LOCATION_ROUTES.MOONEE_PONDS]: () => import('@/pages/locations/moonee-ponds'),
  [ROUTE_PATHS.LOCATION_ROUTES.SUNBURY]: () => import('@/pages/locations/sunbury'),
  [ROUTE_PATHS.LOCATION_ROUTES.DANDENONG]: () => import('@/pages/locations/dandenong'),
  [ROUTE_PATHS.LOCATION_ROUTES.WANTIRNA]: () => import('@/pages/locations/wantirna'),

  // Expertise Routes
  [ROUTE_PATHS.EXPERTISE_ROUTES.CERVICAL_DISC_REPLACEMENT]: () => import('@/pages/expertise/CervicalDiscReplacement'),
  [ROUTE_PATHS.EXPERTISE_ROUTES.LUMBAR_DISC_REPLACEMENT]: () => import('@/pages/expertise/LumbarDiscReplacement'),
  [ROUTE_PATHS.EXPERTISE_ROUTES.IMAGE_GUIDED_SURGERY]: () => import('@/pages/expertise/ImageGuidedSurgery'),
  [ROUTE_PATHS.EXPERTISE_ROUTES.ROBOTIC_SPINE_SURGERY]: () => import('@/pages/expertise/RoboticSpineSurgery'),

  // GP Resources Routes
  [ROUTE_PATHS.GP_RESOURCES_ROUTES.REFERRAL_PROTOCOLS]: () => import('@/pages/gp-resources/ReferralProtocols'),
  [ROUTE_PATHS.GP_RESOURCES_ROUTES.DIAGNOSTICS]: () => import('@/pages/gp-resources/Diagnostics'),
  [ROUTE_PATHS.GP_RESOURCES_ROUTES.CARE_COORDINATION]: () => import('@/pages/gp-resources/CareCoordination'),
  [ROUTE_PATHS.GP_RESOURCES_ROUTES.EMERGENCIES]: () => import('@/pages/gp-resources/Emergencies'),

  // Patient Resources
  [ROUTE_PATHS.PATIENT_RESOURCES_ROUTES.CONDITION_INFORMATION]: () => import('@/pages/patient-resources/ConditionInformation'),
  [ROUTE_PATHS.PATIENT_RESOURCES_ROUTES.SPINE_CONDITIONS_LIBRARY]: () => import('@/pages/patient-resources/SpineConditionsLibrary'),
  [ROUTE_PATHS.PATIENT_RESOURCES_ROUTES.SPINE_ANATOMY]: () => import('@/pages/patient-resources/SpineAnatomy'),
  [ROUTE_PATHS.PATIENT_RESOURCES_ROUTES.EXERCISE_LIBRARY]: () => import('@/pages/patient-resources/ExerciseLibrary'),
  [ROUTE_PATHS.PATIENT_RESOURCES_ROUTES.INDIVIDUAL_SPINE_HEALTH_PROGRAMME]: () => import('@/pages/patient-resources/IndividualSpineHealthProgramme'),
  [ROUTE_PATHS.PATIENT_RESOURCES_ROUTES.SPINE_AND_BRAIN_HEALTH]: () => import('@/pages/patient-resources/SpineAndBrainHealth'),
  [ROUTE_PATHS.PATIENT_RESOURCES_ROUTES.CERVICAL_SPINE_INJURY]: () => import('@/pages/patient-resources/CervicalSpineInjury'),
  [ROUTE_PATHS.PATIENT_RESOURCES_ROUTES.CERVICAL_SPINE_EXERCISES]: () => import('@/pages/patient-resources/CervicalSpineExercises'),
  [ROUTE_PATHS.PATIENT_RESOURCES_ROUTES.SPINE_SAFE_EXERCISES]: () => import('@/pages/patient-resources/SpineSafeExercises'),
  [ROUTE_PATHS.PATIENT_RESOURCES_ROUTES.EXERCISE_PAIN_MED_RISKS]: () => import('@/pages/patient-resources/ExercisePainMedRisks'),
  [ROUTE_PATHS.PATIENT_RESOURCES_ROUTES.AGE_SPECIFIC_SPINE_RECOMMENDATIONS]: () => import('@/pages/patient-resources/AgeSpecificSpineRecommendationsRefactored'),
  [ROUTE_PATHS.PATIENT_RESOURCES_ROUTES.YOUTHFUL_SPINE]: () => import('@/pages/patient-resources/YouthfulSpine'),
  [ROUTE_PATHS.PATIENT_RESOURCES_ROUTES.LIFESTYLE_MODIFICATIONS]: () => import('@/pages/patient-resources/LifestyleModificationsRefactored'),
  [ROUTE_PATHS.PATIENT_RESOURCES_ROUTES.ASSESSMENT_TOOLS]: () => import('@/pages/patient-resources/AssessmentTools'),
  [ROUTE_PATHS.PATIENT_RESOURCES_ROUTES.PATIENT_DASHBOARD]: () => import('@/pages/patient-resources/PatientDashboard'),
  [ROUTE_PATHS.PATIENT_RESOURCES_ROUTES.SPINE_HEALTH_APP]: () => import('@/pages/patient-resources/SpineHealthApp'),
  [ROUTE_PATHS.PATIENT_RESOURCES_ROUTES.CONDITION_INFO]: () => import('@/pages/patient-resources/ConditionInformation'),
  [ROUTE_PATHS.PATIENT_RESOURCES_ROUTES.BRAIN_CONDITIONS]: () => import('@/pages/patient-resources/SpineConditionsLibrary'),
  [ROUTE_PATHS.PATIENT_RESOURCES_ROUTES.SPINE_CONDITIONS]: () => import('@/pages/patient-resources/SpineConditionsLibrary'),
  [ROUTE_PATHS.PATIENT_RESOURCES_ROUTES.SURGICAL_TECHNOLOGIES]: () => import('@/pages/patient-resources/SpineHealthApp'),
  [ROUTE_PATHS.PATIENT_RESOURCES_ROUTES.RECOVERY_REHABILITATION]: () => import('@/pages/patient-resources/SpineAndBrainHealth'),
  [ROUTE_PATHS.PATIENT_RESOURCES_ROUTES.INDIVIDUAL_SPINE_HEALTH_PROGRAM]: () => import('@/pages/patient-resources/IndividualSpineHealthProgramme'),

  // Medical Conditions
  [ROUTE_PATHS.CONDITIONS.HERNIATED_DISC]: () => import('@/pages/patient-resources/conditions/HerniatedDiscRefactored'),
  [ROUTE_PATHS.CONDITIONS.SPINAL_STENOSIS]: () => import('@/pages/patient-resources/conditions/SpinalStenosisRefactored'),
  [ROUTE_PATHS.CONDITIONS.SCIATICA]: () => import('@/pages/patient-resources/conditions/SciaticaRefactored'),
  [ROUTE_PATHS.CONDITIONS.RADICULOPATHY]: () => import('@/pages/patient-resources/conditions/RadiculopathyRefactored'),
  [ROUTE_PATHS.CONDITIONS.ARTHROSIS]: () => import('@/pages/patient-resources/conditions/ArthrosisRefactored'),
  [ROUTE_PATHS.CONDITIONS.DISCOPATHY]: () => import('@/pages/patient-resources/conditions/Discopathy'),
  [ROUTE_PATHS.CONDITIONS.FACET_ARTHROPATHY]: () => import('@/pages/patient-resources/conditions/FacetArthropathyRefactored'),
  [ROUTE_PATHS.CONDITIONS.SACROILIAC_ARTHROPATHY]: () => import('@/pages/patient-resources/conditions/SacroiliacArthropathyRefactored'),
  [ROUTE_PATHS.CONDITIONS.PIRIFORMIS_SYNDROME]: () => import('@/pages/patient-resources/conditions/PiriformisSyndromeRefactored'),
  [ROUTE_PATHS.CONDITIONS.THORACIC_OUTLET_SYNDROME]: () => import('@/pages/patient-resources/conditions/ThoracicOutletSyndromeRefactored'),
  [ROUTE_PATHS.CONDITIONS.OCCIPITAL_NEURALGIA]: () => import('@/pages/patient-resources/conditions/OccipitalNeuralgiaRefactored'),
  [ROUTE_PATHS.CONDITIONS.SPONDYLOSIS]: () => import('@/pages/patient-resources/conditions/SpondylosisRefactored'),
  [ROUTE_PATHS.CONDITIONS.PARS_DEFECTS]: () => import('@/pages/patient-resources/conditions/ParsDefectsRefactored'),
  [ROUTE_PATHS.CONDITIONS.SPONDYLOLISTHESIS]: () => import('@/pages/patient-resources/conditions/SpondylolisthesisRefactored'),
  [ROUTE_PATHS.CONDITIONS.FACET_JOINT_SYNDROME]: () => import('@/pages/patient-resources/conditions/FacetArthropathyRefactored'),
  [ROUTE_PATHS.CONDITIONS.CERVICAL_DISC_HERNIATION]: () => import('@/pages/patient-resources/conditions/HerniatedDiscRefactored'),
  [ROUTE_PATHS.CONDITIONS.WHIPLASH]: () => import('@/pages/patient-resources/conditions/RadiculopathyRefactored'),
  [ROUTE_PATHS.CONDITIONS.THORACIC_COMPRESSION_FRACTURE]: () => import('@/pages/patient-resources/conditions/SpinalStenosisRefactored'),

  // Expertise/Technologies
  [ROUTE_PATHS.EXPERTISE_ROUTES.CERVICAL_DISC_REPLACEMENT]: () => import('@/pages/expertise/CervicalDiscReplacementRefactored'),
  [ROUTE_PATHS.EXPERTISE_ROUTES.LUMBAR_DISC_REPLACEMENT]: () => import('@/pages/expertise/LumbarDiscReplacementRefactored'),
  [ROUTE_PATHS.EXPERTISE_ROUTES.IMAGE_GUIDED_SURGERY]: () => import('@/pages/expertise/ImageGuidedSurgeryRefactored'),
  [ROUTE_PATHS.EXPERTISE_ROUTES.ROBOTIC_SPINE_SURGERY]: () => import('@/pages/expertise/RoboticSpineSurgeryRefactored'),
  [ROUTE_PATHS.EXPERTISE_ROUTES.ANTERIOR_CERVICAL_DISCECTOMY_FUSION]: () => import('@/pages/expertise/CervicalDiscReplacementRefactored'),
  [ROUTE_PATHS.EXPERTISE_ROUTES.POSTERIOR_CERVICAL_FORAMINOTOMY]: () => import('@/pages/expertise/CervicalDiscReplacementRefactored'),
  [ROUTE_PATHS.EXPERTISE_ROUTES.MINIMALLY_INVASIVE_SPINE_SURGERY]: () => import('@/pages/expertise/RoboticSpineSurgeryRefactored'),
  [ROUTE_PATHS.EXPERTISE_ROUTES.BRAIN_TUMOUR_SURGERY]: () => import('@/pages/expertise/ImageGuidedSurgeryRefactored'),
  [ROUTE_PATHS.EXPERTISE_ROUTES.LUMBAR_FUSION]: () => import('@/pages/expertise/LumbarDiscReplacementRefactored'),
  [ROUTE_PATHS.EXPERTISE_ROUTES.SPINAL_FUSION]: () => import('@/pages/expertise/RoboticSpineSurgeryRefactored'),

  // Locations
  [ROUTE_PATHS.LOCATION_ROUTES.SURREY_HILLS]: () => import('@/pages/locations/surrey-hills'),
  [ROUTE_PATHS.LOCATION_ROUTES.MORNINGTON]: () => import('@/pages/locations/mornington'),
  [ROUTE_PATHS.LOCATION_ROUTES.FRANKSTON]: () => import('@/pages/locations/frankston'),
  [ROUTE_PATHS.LOCATION_ROUTES.LANGWARRIN]: () => import('@/pages/locations/langwarrin'),
  [ROUTE_PATHS.LOCATION_ROUTES.BUNDOORA]: () => import('@/pages/locations/bundoora'),
  [ROUTE_PATHS.LOCATION_ROUTES.WERRIBEE]: () => import('@/pages/locations/werribee'),
  [ROUTE_PATHS.LOCATION_ROUTES.HEIDELBERG]: () => import('@/pages/locations/heidelberg'),
  [ROUTE_PATHS.LOCATION_ROUTES.MOONEE_PONDS]: () => import('@/pages/locations/moonee-ponds'),
  [ROUTE_PATHS.LOCATION_ROUTES.SUNBURY]: () => import('@/pages/locations/sunbury'),
  [ROUTE_PATHS.LOCATION_ROUTES.DANDENONG]: () => import('@/pages/locations/dandenong'),
  [ROUTE_PATHS.LOCATION_ROUTES.WANTIRNA]: () => import('@/pages/locations/wantirna'),
  [ROUTE_PATHS.LOCATION_ROUTES.LOCATION_DETAIL]: () => import('@/pages/locations/LocationDetail'),

  // GP Resources
  [ROUTE_PATHS.GP_RESOURCES_ROUTES.REFERRAL_PROTOCOLS]: () => import('@/pages/gp-resources/ReferralProtocols'),
  [ROUTE_PATHS.GP_RESOURCES_ROUTES.DIAGNOSTICS]: () => import('@/pages/gp-resources/Diagnostics'),
  [ROUTE_PATHS.GP_RESOURCES_ROUTES.CARE_COORDINATION]: () => import('@/pages/gp-resources/CareCoordination'),
  [ROUTE_PATHS.GP_RESOURCES_ROUTES.EMERGENCIES]: () => import('@/pages/gp-resources/Emergencies'),

  // Special routes
  [ROUTE_PATHS.NOT_FOUND]: () => import('@/pages/NotFound'),
});

// Get the route loader registry
const routeRegistry = RouteLoaderRegistry.getInstance();

// Define route structure that matches React Router's expectations
export interface RouteConfig {
  path: string;
  element: React.ReactNode | null;
  children?: RouteConfig[];
}

// Helper function to create route elements
function createRouteElement(path: string): React.ReactNode | null {
  try {
    const LazyComponent = routeRegistry.getLazyComponent(path);
    return <LazyComponent />;
  } catch (error) {
    logRoute(`Failed to create route element for path: ${path}`, error);
    return null;
  }
}

// Helper function to create redirect elements
function createRedirectElement(to: string): React.ReactNode | null {
  try {
    return <Navigate to={to} replace />;
  } catch (error) {
    logRoute(`Failed to create redirect element to: ${to}`, error);
    return null;
  }
}

// Generate base routes from route definitions
function generateBaseRoutes(): RouteConfig[] {
  const routes: RouteConfig[] = [];

  // Add all registered routes
  routeRegistry.getRegisteredRoutes().forEach(path => {
    routes.push({
      path,
      element: createRouteElement(path)
    });
  });

  // Add redirects
  Object.entries(ROUTE_REDIRECTS).forEach(([from, to]) => {
    routes.push({
      path: from,
      element: createRedirectElement(to)
    });
  });

  return routes;
}

// Define base routes (without language prefix)
export const baseRoutes: RouteConfig[] = generateBaseRoutes();

// Since we only support English, no language-specific routes are needed
export const getAllRoutes = (): RouteConfig[] => {
  // Return only the base routes without language prefixes
  return baseRoutes;
};

// Export route utilities
export { ROUTE_PATHS, ROUTE_REDIRECTS, SUPPORTED_LANGUAGES } from './route-definitions';
export { preloadRoute, RouteLoaderRegistry } from './route-loader';


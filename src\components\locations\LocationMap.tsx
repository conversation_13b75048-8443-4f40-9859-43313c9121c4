import { Car, Train } from 'lucide-react';
import React from 'react';

interface TransportOption {
  icon: React.ReactNode;
  title: string;
  description: string;
}

interface LocationMapProps {
  embedUrl: string;
  title: string;
  transportOptions: {
    publicTransport: string;
    car: string;
  };
  gettingHereTitle?: string;
  publicTransportTitle?: string;
  carTitle?: string;
}

const LocationMap: React.FC<LocationMapProps> = ({
  embedUrl,
  title,
  transportOptions,
  gettingHereTitle = "Getting Here",
  publicTransportTitle = "By Public Transport",
  carTitle = "By Car"
}) => {
  const transportDetails: TransportOption[] = [
    {
      icon: <Train className="h-6 w-6 text-primary" />,
      title: publicTransportTitle,
      description: transportOptions.publicTransport
    },
    {
      icon: <Car className="h-6 w-6 text-primary" />,
      title: carTitle,
      description: transportOptions.car
    }
  ];

  return (
    <div className="md:w-1/2">
      <div className="h-80 w-full rounded-lg overflow-hidden shadow-xl mb-8">
        <iframe
          src={embedUrl}
          width="100%"
          height="100%"
          style={{ border: 0 }}
          allowFullScreen
          loading="lazy"
          referrerPolicy="no-referrer-when-downgrade"
          title={`${title} Location Map`}
          className="rounded-lg"
        />
      </div>

      <div className="card p-6 rounded-lg shadow-md bg-card">
        <h3 className="text-xl font-semibold mb-3 text-primary">{gettingHereTitle}</h3>
        <div className="space-y-4">
          {transportDetails.map((transport, index) => (
            <div key={index}>
              <div className="flex items-center mb-2">
                {transport.icon}
                <h4 className="text-lg font-medium ml-2">{transport.title}</h4>
              </div>
              <p className="text-muted-foreground">{transport.description}</p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default LocationMap;

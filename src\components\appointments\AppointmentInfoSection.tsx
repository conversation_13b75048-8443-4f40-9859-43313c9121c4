import React from 'react';

import { AppointmentInfoCard } from '@/data/appointments/appointmentInfoData';

interface AppointmentInfoSectionProps {
  cards: AppointmentInfoCard[];
  title: string;
}

const AppointmentInfoSection: React.FC<AppointmentInfoSectionProps> = ({
  cards, 
  title 
}) => {
  return (
    <div>
      <h2 className="text-2xl font-bold mb-6 text-foreground">{title}</h2>
      <div className="space-y-6">
        {cards.map((card, index) => (
          <div 
            key={card.id}
            className="bg-card/50 backdrop-blur-sm border border-border/50 p-6 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 animate-fade-in"
            style={{ animationDelay: `${index * 100}ms` }}
          >
            <h3 className="text-xl font-semibold mb-3 text-primary">{card.title}</h3>
            
            {/* Render descriptions */}
            {card.content.descriptions && card.content.descriptions.map((description, idx) => (
              <p key={idx} className="text-muted-foreground mb-4 leading-relaxed">
                {description}
              </p>
            ))}
            
            {/* Render items list */}
            {card.content.items && (
              <ul className="list-disc list-inside text-muted-foreground space-y-2 mb-4">
                {card.content.items.map((item, idx) => (
                  <li key={idx} className="leading-relaxed">{item}</li>
                ))}
              </ul>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

AppointmentInfoSection.displayName = 'AppointmentInfoSection';

export default AppointmentInfoSection;

import React from 'react';

import { cn } from '@/lib/utils';

interface GlassCardProps {
  children: React.ReactNode;
  variant?: 'default' | 'subtle' | 'strong';
  padding?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  hover?: boolean;
  sticky?: boolean;
  stickyTop?: string;
  className?: string;
}

const GlassCard: React.FC<GlassCardProps> = ({
  children,
  variant = 'default',
  padding = 'md',
  hover = false,
  sticky = false,
  stickyTop = 'top-24',
  className = ''
}) => {
  const getVariantClass = () => {
    switch (variant) {
      case 'subtle':
        return 'bg-card/30 backdrop-blur-sm border border-border/30';
      case 'strong':
        return 'bg-card/80 backdrop-blur-md border border-border/60';
      default:
        return 'bg-card/50 backdrop-blur-sm border border-border/50';
    }
  };

  const getPaddingClass = () => {
    switch (padding) {
      case 'none':
        return '';
      case 'sm':
        return 'p-4';
      case 'lg':
        return 'p-8';
      case 'xl':
        return 'p-12';
      default:
        return 'p-6';
    }
  };

  const baseClasses = [
    getVariantClass(),
    getPaddingClass(),
    'rounded-lg shadow-md',
    hover && 'hover:shadow-lg transition-all duration-300',
    sticky && `sticky ${stickyTop}`,
    'animate-fade-in'
  ].filter(Boolean).join(' ');

  return (
    <div className={cn(baseClasses, className)}>
      {children}
    </div>
  );
};

export default GlassCard;

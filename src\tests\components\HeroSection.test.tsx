import { render, screen } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { describe, it, expect, vi, beforeEach } from 'vitest';

import HeroSection from '@/components/HeroSection';
import { DeviceProvider } from '@/contexts/DeviceContext';
import { LanguageProvider } from '@/contexts/LanguageContext';

// Mock the performance hook
vi.mock('@/lib/performance', () => ({
  usePerformanceMetric: vi.fn()
}));

// Mock device detection
vi.mock('@/contexts/DeviceContext', async () => {
  const actual = await vi.importActual('@/contexts/DeviceContext');
  return {
    ...actual,
    useDeviceDetection: () => ({
      isMobile: false,
      isTablet: false,
      isDesktop: true,
      screenSize: 'desktop'
    })
  };
});

const TestWrapper = ({ children }: { children: React.ReactNode }) => (
  <BrowserRouter>
    <LanguageProvider>
      <DeviceProvider>
        {children}
      </DeviceProvider>
    </LanguageProvider>
  </BrowserRouter>
);

describe('HeroSection', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders the hero section with correct text content', () => {
    render(
      <TestWrapper>
        <HeroSection />
      </TestWrapper>
    );

    // Check for the subtitle
    expect(screen.getByText('THE GOLD STANDARD FOR BRAIN AND SPINE SURGERY')).toBeInTheDocument();
    
    // Check for the main title
    expect(screen.getByText('Neurosurgical Expertise and Innovative Technology for Superior Brain and Spine Surgery Results')).toBeInTheDocument();
    
    // Check for the description
    expect(screen.getByText(/Dr Aliashkevich specialises in future-minded treatment/)).toBeInTheDocument();
  });

  it('renders both CTA buttons with correct text', () => {
    render(
      <TestWrapper>
        <HeroSection />
      </TestWrapper>
    );

    // Check for the primary button
    const bookConsultationButton = screen.getByRole('link', { name: /book consultation/i });
    expect(bookConsultationButton).toBeInTheDocument();
    expect(bookConsultationButton).toHaveAttribute('href', '/appointments');

    // Check for the secondary button
    const exploreButton = screen.getByRole('link', { name: /explore treatment options/i });
    expect(exploreButton).toBeInTheDocument();
    expect(exploreButton).toHaveAttribute('href', '/expertise');
  });

  it('renders scroll down indicator', () => {
    render(
      <TestWrapper>
        <HeroSection />
      </TestWrapper>
    );

    const scrollDownLink = screen.getByRole('link', { name: /scroll down to welcome section/i });
    expect(scrollDownLink).toBeInTheDocument();
    expect(scrollDownLink).toHaveAttribute('href', '#welcome');
  });

  it('has proper accessibility attributes', () => {
    render(
      <TestWrapper>
        <HeroSection />
      </TestWrapper>
    );

    // Check for proper heading structure
    const mainHeading = screen.getByRole('heading', { level: 1 });
    expect(mainHeading).toBeInTheDocument();
    expect(mainHeading).toHaveAttribute('id', 'hero-heading');

    // Check for section with proper aria-labelledby
    const heroSection = screen.getByRole('region', { name: /neurosurgical expertise/i });
    expect(heroSection).toHaveAttribute('aria-labelledby', 'hero-heading');
  });
});

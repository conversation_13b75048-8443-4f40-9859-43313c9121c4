import { render, screen, fireEvent } from '@testing-library/react';
import React from 'react';
import { describe, it, expect, vi } from 'vitest';

import AsyncContent, { 
  AsyncMedicalContent, 
  AsyncSearchContent, 
  LoadingWrapper 
} from '@/components/AsyncContent';
import { DeviceProvider } from '@/contexts/DeviceContext';

// Mock DeviceContext
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <DeviceProvider>
    {children}
  </DeviceProvider>
);

describe('AsyncContent', () => {
  describe('Loading States', () => {
    it('renders spinner loading state', () => {
      render(
        <TestWrapper>
          <AsyncContent state="loading" loadingVariant="spinner">
            <div>Content</div>
          </AsyncContent>
        </TestWrapper>
      );

      // Use getAllByText since Loading component renders both visible and sr-only text
      const loadingTexts = screen.getAllByText('Loading...');
      expect(loadingTexts.length).toBeGreaterThan(0);
      expect(screen.queryByText('Content')).not.toBeInTheDocument();
    });

    it('renders skeleton loading state', () => {
      const { container } = render(
        <TestWrapper>
          <AsyncContent state="loading" loadingVariant="skeleton">
            <div>Content</div>
          </AsyncContent>
        </TestWrapper>
      );
      
      expect(container.querySelector('.animate-pulse')).toBeInTheDocument();
      expect(screen.queryByText('Content')).not.toBeInTheDocument();
    });

    it('renders card loading state', () => {
      const { container } = render(
        <TestWrapper>
          <AsyncContent state="loading" loadingVariant="card">
            <div>Content</div>
          </AsyncContent>
        </TestWrapper>
      );
      
      expect(container.querySelector('.border')).toBeInTheDocument();
      expect(container.querySelector('.animate-pulse')).toBeInTheDocument();
    });

    it('renders grid loading state', () => {
      const { container } = render(
        <TestWrapper>
          <AsyncContent
            state="loading"
            loadingVariant="grid"
            skeletonItems={3}
            skeletonColumns={2}
          >
            <div>Content</div>
          </AsyncContent>
        </TestWrapper>
      );

      expect(container.querySelector('.grid')).toBeInTheDocument();
      // GridSkeleton renders multiple animate-pulse elements per item (image, title, description, actions)
      // With 3 items, expect more than 3 animate-pulse elements
      const animatedElements = container.querySelectorAll('.animate-pulse');
      expect(animatedElements.length).toBeGreaterThan(3);
    });

    it('renders list loading state', () => {
      const { container } = render(
        <TestWrapper>
          <AsyncContent 
            state="loading" 
            loadingVariant="list"
            skeletonItems={2}
            showSkeletonAvatar={true}
          >
            <div>Content</div>
          </AsyncContent>
        </TestWrapper>
      );
      
      expect(container.querySelector('.space-y-4')).toBeInTheDocument();
      expect(container.querySelectorAll('.rounded-full')).toHaveLength(2); // Avatars
    });

    it('renders custom loading text', () => {
      render(
        <TestWrapper>
          <AsyncContent
            state="loading"
            loadingText="Custom loading message"
          >
            <div>Content</div>
          </AsyncContent>
        </TestWrapper>
      );

      // Use getAllByText since Loading component renders both visible and sr-only text
      const loadingTexts = screen.getAllByText('Custom loading message');
      expect(loadingTexts.length).toBeGreaterThan(0);
    });
  });

  describe('Error States', () => {
    it('renders network error state', () => {
      const mockRetry = vi.fn();
      const networkError = new Error('Network connection failed');
      
      render(
        <TestWrapper>
          <AsyncContent 
            state="error" 
            error={networkError}
            onRetry={mockRetry}
          >
            <div>Content</div>
          </AsyncContent>
        </TestWrapper>
      );
      
      expect(screen.getByText('Connection Error')).toBeInTheDocument();
      expect(screen.getByText('Network connection failed')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: 'Try Again' })).toBeInTheDocument();
    });

    it('renders loading error state', () => {
      const mockRetry = vi.fn();
      const loadingError = new Error('Failed to load data');
      
      render(
        <TestWrapper>
          <AsyncContent 
            state="error" 
            error={loadingError}
            onRetry={mockRetry}
          >
            <div>Content</div>
          </AsyncContent>
        </TestWrapper>
      );
      
      expect(screen.getByText('Loading Error')).toBeInTheDocument();
      expect(screen.getByText('Failed to load data')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: 'Refresh Page' })).toBeInTheDocument();
    });

    it('calls onRetry when retry button is clicked', () => {
      const mockRetry = vi.fn();
      const error = new Error('Test error');
      
      render(
        <TestWrapper>
          <AsyncContent 
            state="error" 
            error={error}
            onRetry={mockRetry}
          >
            <div>Content</div>
          </AsyncContent>
        </TestWrapper>
      );
      
      fireEvent.click(screen.getByRole('button', { name: 'Refresh Page' }));
      expect(mockRetry).toHaveBeenCalledTimes(1);
    });

    it('renders custom error title and description', () => {
      const error = new Error('Test error');
      
      render(
        <TestWrapper>
          <AsyncContent 
            state="error" 
            error={error}
            errorTitle="Custom Error Title"
            errorDescription="Custom error description"
          >
            <div>Content</div>
          </AsyncContent>
        </TestWrapper>
      );
      
      expect(screen.getByText('Custom Error Title')).toBeInTheDocument();
      expect(screen.getByText('Custom error description')).toBeInTheDocument();
    });
  });

  describe('Empty States', () => {
    it('renders empty state', () => {
      render(
        <TestWrapper>
          <AsyncContent state="empty">
            <div>Content</div>
          </AsyncContent>
        </TestWrapper>
      );
      
      expect(screen.getByText('No Data Available')).toBeInTheDocument();
      expect(screen.queryByText('Content')).not.toBeInTheDocument();
    });

    it('renders empty state with custom content', () => {
      render(
        <TestWrapper>
          <AsyncContent 
            state="empty"
            emptyTitle="Custom Empty Title"
            emptyDescription="Custom empty description"
          >
            <div>Content</div>
          </AsyncContent>
        </TestWrapper>
      );
      
      expect(screen.getByText('Custom Empty Title')).toBeInTheDocument();
      expect(screen.getByText('Custom empty description')).toBeInTheDocument();
    });

    it('renders empty state with actions', () => {
      const mockAction = vi.fn();
      
      render(
        <TestWrapper>
          <AsyncContent 
            state="empty"
            emptyStateActions={[
              {
                label: 'Test Action',
                onClick: mockAction,
                variant: 'default'
              }
            ]}
          >
            <div>Content</div>
          </AsyncContent>
        </TestWrapper>
      );
      
      const button = screen.getByRole('button', { name: 'Test Action' });
      expect(button).toBeInTheDocument();
      
      fireEvent.click(button);
      expect(mockAction).toHaveBeenCalledTimes(1);
    });

    it('renders refresh button when showRefreshButton is true', () => {
      const mockRefresh = vi.fn();
      
      render(
        <TestWrapper>
          <AsyncContent 
            state="empty"
            showRefreshButton={true}
            onRefresh={mockRefresh}
          >
            <div>Content</div>
          </AsyncContent>
        </TestWrapper>
      );
      
      expect(screen.getByRole('button', { name: 'Refresh' })).toBeInTheDocument();
    });
  });

  describe('Success States', () => {
    it('renders children when state is success', () => {
      render(
        <TestWrapper>
          <AsyncContent state="success">
            <div data-testid="success-content">Success Content</div>
          </AsyncContent>
        </TestWrapper>
      );
      
      expect(screen.getByTestId('success-content')).toBeInTheDocument();
      expect(screen.getByText('Success Content')).toBeInTheDocument();
    });

    it('renders refresh button in success state when enabled', () => {
      const mockRefresh = vi.fn();
      
      render(
        <TestWrapper>
          <AsyncContent 
            state="success"
            showRefreshButton={true}
            onRefresh={mockRefresh}
          >
            <div>Content</div>
          </AsyncContent>
        </TestWrapper>
      );
      
      expect(screen.getByRole('button', { name: 'Refresh' })).toBeInTheDocument();
      expect(screen.getByText('Content')).toBeInTheDocument();
    });

    it('calls onRefresh when refresh button is clicked', () => {
      const mockRefresh = vi.fn();
      
      render(
        <TestWrapper>
          <AsyncContent 
            state="success"
            showRefreshButton={true}
            onRefresh={mockRefresh}
          >
            <div>Content</div>
          </AsyncContent>
        </TestWrapper>
      );
      
      fireEvent.click(screen.getByRole('button', { name: 'Refresh' }));
      expect(mockRefresh).toHaveBeenCalledTimes(1);
    });
  });

  describe('Idle State', () => {
    it('renders children when state is idle', () => {
      render(
        <TestWrapper>
          <AsyncContent state="idle">
            <div data-testid="idle-content">Idle Content</div>
          </AsyncContent>
        </TestWrapper>
      );
      
      expect(screen.getByTestId('idle-content')).toBeInTheDocument();
      expect(screen.getByText('Idle Content')).toBeInTheDocument();
    });
  });
});

describe('AsyncMedicalContent', () => {
  it('renders with procedures data type', () => {
    render(
      <TestWrapper>
        <AsyncMedicalContent 
          state="empty"
          dataType="procedures"
        >
          <div>Content</div>
        </AsyncMedicalContent>
      </TestWrapper>
    );
    
    expect(screen.getByText('No Procedures Found')).toBeInTheDocument();
  });

  it('renders with conditions data type', () => {
    render(
      <TestWrapper>
        <AsyncMedicalContent 
          state="empty"
          dataType="conditions"
        >
          <div>Content</div>
        </AsyncMedicalContent>
      </TestWrapper>
    );
    
    expect(screen.getByText('No Conditions Found')).toBeInTheDocument();
  });

  it('renders with testimonials data type', () => {
    render(
      <TestWrapper>
        <AsyncMedicalContent 
          state="empty"
          dataType="testimonials"
        >
          <div>Content</div>
        </AsyncMedicalContent>
      </TestWrapper>
    );
    
    expect(screen.getByText('No Testimonials Available')).toBeInTheDocument();
  });

  it('renders with reviews data type', () => {
    render(
      <TestWrapper>
        <AsyncMedicalContent 
          state="empty"
          dataType="reviews"
        >
          <div>Content</div>
        </AsyncMedicalContent>
      </TestWrapper>
    );
    
    expect(screen.getByText('No Reviews Yet')).toBeInTheDocument();
  });
});

describe('AsyncSearchContent', () => {
  it('renders with search query', () => {
    render(
      <TestWrapper>
        <AsyncSearchContent 
          state="empty"
          searchQuery="test search"
          onClearSearch={vi.fn()}
        >
          <div>Content</div>
        </AsyncSearchContent>
      </TestWrapper>
    );
    
    expect(screen.getByText('No results for "test search"')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'Clear Search' })).toBeInTheDocument();
  });

  it('calls onClearSearch when clear button is clicked', () => {
    const mockClearSearch = vi.fn();
    
    render(
      <TestWrapper>
        <AsyncSearchContent 
          state="empty"
          searchQuery="test"
          onClearSearch={mockClearSearch}
        >
          <div>Content</div>
        </AsyncSearchContent>
      </TestWrapper>
    );
    
    fireEvent.click(screen.getByRole('button', { name: 'Clear Search' }));
    expect(mockClearSearch).toHaveBeenCalledTimes(1);
  });
});

describe('LoadingWrapper', () => {
  it('renders loading state when isLoading is true', () => {
    render(
      <TestWrapper>
        <LoadingWrapper isLoading={true}>
          <div>Content</div>
        </LoadingWrapper>
      </TestWrapper>
    );

    // Use getAllByText since Loading component renders both visible and sr-only text
    const loadingTexts = screen.getAllByText('Loading...');
    expect(loadingTexts.length).toBeGreaterThan(0);
    expect(screen.queryByText('Content')).not.toBeInTheDocument();
  });

  it('renders error state when error is provided', () => {
    const error = new Error('Test error');
    
    render(
      <TestWrapper>
        <LoadingWrapper isLoading={false} error={error}>
          <div>Content</div>
        </LoadingWrapper>
      </TestWrapper>
    );
    
    expect(screen.getByText('Loading Error')).toBeInTheDocument();
    expect(screen.getByText('Test error')).toBeInTheDocument();
  });

  it('renders empty state when isEmpty is true', () => {
    render(
      <TestWrapper>
        <LoadingWrapper isLoading={false} isEmpty={true}>
          <div>Content</div>
        </LoadingWrapper>
      </TestWrapper>
    );
    
    expect(screen.getByText('No Data Available')).toBeInTheDocument();
    expect(screen.queryByText('Content')).not.toBeInTheDocument();
  });

  it('renders content when all states are false', () => {
    render(
      <TestWrapper>
        <LoadingWrapper isLoading={false}>
          <div data-testid="wrapper-content">Content</div>
        </LoadingWrapper>
      </TestWrapper>
    );
    
    expect(screen.getByTestId('wrapper-content')).toBeInTheDocument();
    expect(screen.getByText('Content')).toBeInTheDocument();
  });
});

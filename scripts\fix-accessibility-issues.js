#!/usr/bin/env node

/**
 * Accessibility Issues Fix Script
 * 
 * Automatically fixes semantic HTML issues by replacing generic div elements
 * with appropriate semantic HTML elements based on context.
 */

import fs from 'fs';
import path from 'path';

console.log('🔧 ACCESSIBILITY ISSUES FIX');
console.log('='.repeat(50));

/**
 * Semantic HTML replacement patterns
 */
const SEMANTIC_PATTERNS = [
  // Main content containers
  {
    pattern: /<div className={cn\(\s*"[^"]*container[^"]*"/g,
    replacement: '<main className={cn("container',
    description: 'Container divs → main elements'
  },
  
  // Navigation containers
  {
    pattern: /<div className={cn\(\s*"[^"]*nav[^"]*"/g,
    replacement: '<nav className={cn("nav',
    description: 'Navigation divs → nav elements'
  },
  
  // Header containers
  {
    pattern: /<div className={cn\(\s*"[^"]*header[^"]*"/g,
    replacement: '<header className={cn("header',
    description: 'Header divs → header elements'
  },
  
  // Footer containers
  {
    pattern: /<div className={cn\(\s*"[^"]*footer[^"]*"/g,
    replacement: '<footer className={cn("footer',
    description: 'Footer divs → footer elements'
  },
  
  // Section containers
  {
    pattern: /<div className={cn\(\s*"[^"]*section[^"]*"/g,
    replacement: '<section className={cn("section',
    description: 'Section divs → section elements'
  },
  
  // Article containers
  {
    pattern: /<div className={cn\(\s*"[^"]*article[^"]*"/g,
    replacement: '<article className={cn("article',
    description: 'Article divs → article elements'
  },
  
  // Aside containers
  {
    pattern: /<div className={cn\(\s*"[^"]*aside[^"]*"/g,
    replacement: '<aside className={cn("aside',
    description: 'Aside divs → aside elements'
  }
];

/**
 * Context-based semantic replacements
 */
const CONTEXT_PATTERNS = [
  // Form containers
  {
    pattern: /<div className="[^"]*form[^"]*">/g,
    replacement: '<form className="$&">',
    description: 'Form containers → form elements'
  },
  
  // List containers
  {
    pattern: /<div className="[^"]*list[^"]*">/g,
    replacement: '<ul className="$&">',
    description: 'List containers → ul elements'
  },
  
  // Button containers that should be buttons
  {
    pattern: /<div className="[^"]*button[^"]*" onClick/g,
    replacement: '<button className="$&" onClick',
    description: 'Clickable divs → button elements'
  }
];

/**
 * Load files that need accessibility fixes
 */
function getFilesToFix() {
  const analysisPath = 'comprehensive-codebase-analysis.json';
  if (!fs.existsSync(analysisPath)) {
    console.error('❌ Analysis file not found');
    return [];
  }

  const analysis = JSON.parse(fs.readFileSync(analysisPath, 'utf8'));
  const accessibilityIssues = analysis.issues.filter(issue => 
    issue.issue && issue.issue.includes('accessibility')
  );

  return accessibilityIssues.map(issue => issue.file).filter(Boolean);
}

/**
 * Apply semantic HTML fixes to a file
 */
function fixFile(filePath) {
  try {
    if (!fs.existsSync(filePath)) {
      console.log(`⚠️  File not found: ${filePath}`);
      return { fixed: false, reason: 'File not found' };
    }

    let content = fs.readFileSync(filePath, 'utf8');
    let changes = 0;
    const appliedFixes = [];

    // Apply semantic patterns
    for (const pattern of SEMANTIC_PATTERNS) {
      const matches = content.match(pattern.pattern);
      if (matches) {
        content = content.replace(pattern.pattern, pattern.replacement);
        changes += matches.length;
        appliedFixes.push(pattern.description);
      }
    }

    // Apply context patterns
    for (const pattern of CONTEXT_PATTERNS) {
      const matches = content.match(pattern.pattern);
      if (matches) {
        content = content.replace(pattern.pattern, pattern.replacement);
        changes += matches.length;
        appliedFixes.push(pattern.description);
      }
    }

    // Additional manual fixes for common patterns
    const manualFixes = [
      // Wrapper divs that should be sections
      {
        from: /<div className={cn\(\s*"[^"]*py-\d+[^"]*"\s*\)}/g,
        to: '<section className={cn("py-',
        desc: 'Padding wrapper divs → sections'
      },
      
      // Grid containers
      {
        from: /<div className={cn\(\s*"[^"]*grid[^"]*"/g,
        to: '<section className={cn("grid',
        desc: 'Grid divs → sections'
      },
      
      // Flex containers
      {
        from: /<div className={cn\(\s*"[^"]*flex[^"]*"/g,
        to: '<div className={cn("flex', // Keep as div for layout
        desc: 'Flex containers (kept as div for layout)'
      }
    ];

    for (const fix of manualFixes) {
      const matches = content.match(fix.from);
      if (matches) {
        content = content.replace(fix.from, fix.to);
        changes += matches.length;
        appliedFixes.push(fix.desc);
      }
    }

    // Write back if changes were made
    if (changes > 0) {
      // Create backup
      const backupPath = filePath + '.backup';
      fs.copyFileSync(filePath, backupPath);
      
      // Write fixed content
      fs.writeFileSync(filePath, content, 'utf8');
      
      return {
        fixed: true,
        changes,
        appliedFixes: [...new Set(appliedFixes)], // Remove duplicates
        backupPath
      };
    }

    return { fixed: false, reason: 'No accessibility issues found' };

  } catch (error) {
    return { fixed: false, reason: error.message };
  }
}

/**
 * Main execution function
 */
function main() {
  console.log('📋 Loading files with accessibility issues...');
  
  const filesToFix = getFilesToFix();
  console.log(`Found ${filesToFix.length} files with accessibility issues`);

  if (filesToFix.length === 0) {
    console.log('✅ No files to fix!');
    return;
  }

  const results = {
    total: filesToFix.length,
    fixed: 0,
    skipped: 0,
    errors: 0,
    totalChanges: 0,
    fixedFiles: [],
    errors: []
  };

  console.log('\n🔧 Applying accessibility fixes...');

  for (const filePath of filesToFix) {
    const relativePath = path.relative(process.cwd(), filePath);
    process.stdout.write(`  Processing: ${relativePath}... `);

    const result = fixFile(filePath);

    if (result.fixed) {
      console.log(`✅ Fixed (${result.changes} changes)`);
      results.fixed++;
      results.totalChanges += result.changes;
      results.fixedFiles.push({
        file: relativePath,
        changes: result.changes,
        fixes: result.appliedFixes,
        backup: result.backupPath
      });
    } else {
      console.log(`⚠️  ${result.reason}`);
      if (result.reason !== 'No accessibility issues found') {
        results.errors++;
        results.errors.push({ file: relativePath, error: result.reason });
      } else {
        results.skipped++;
      }
    }
  }

  // Generate summary
  console.log('\n📊 ACCESSIBILITY FIX SUMMARY');
  console.log('='.repeat(30));
  console.log(`Total files processed: ${results.total}`);
  console.log(`Files fixed: ${results.fixed}`);
  console.log(`Files skipped: ${results.skipped}`);
  console.log(`Errors: ${results.errors}`);
  console.log(`Total changes made: ${results.totalChanges}`);

  if (results.fixed > 0) {
    console.log('\n✅ SUCCESSFULLY FIXED FILES:');
    results.fixedFiles.forEach(file => {
      console.log(`  • ${file.file} (${file.changes} changes)`);
      file.fixes.forEach(fix => console.log(`    - ${fix}`));
    });
  }

  if (results.errors.length > 0) {
    console.log('\n❌ ERRORS:');
    results.errors.forEach(error => {
      console.log(`  • ${error.file}: ${error.error}`);
    });
  }

  // Save detailed report
  const report = {
    timestamp: new Date().toISOString(),
    summary: results,
    patternsApplied: SEMANTIC_PATTERNS.map(p => p.description),
    recommendations: [
      'Review the fixed files to ensure semantic correctness',
      'Test accessibility with screen readers',
      'Update any related tests that might be affected',
      'Consider adding accessibility linting rules'
    ]
  };

  fs.writeFileSync('accessibility-fix-report.json', JSON.stringify(report, null, 2));
  console.log('\n📄 Detailed report saved to: accessibility-fix-report.json');

  return results;
}

// Run if called directly
if (process.argv[1] && process.argv[1].endsWith('fix-accessibility-issues.js')) {
  main();
}

export { main as fixAccessibilityIssues };

import React from 'react';

import SafeImage from '@/components/SafeImage';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ConditionCategory } from '@/data/lifestyle-modifications/conditionData';

interface ConditionSpecificSectionProps {
  categories: ConditionCategory[];
  title: string;
}

const ConditionSpecificSection: React.FC<ConditionSpecificSectionProps> = ({
  categories, 
  title 
}) => {
  return (
    <section className="py-16 bg-muted/30">
      <div className="container max-w-7xl">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold mb-4 text-foreground">{title}</h2>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            Evidence-based lifestyle recommendations tailored to specific spine conditions.
          </p>
        </div>
        
        <Tabs defaultValue={categories[0]?.id} className="w-full max-w-5xl mx-auto">
          <TabsList className="grid grid-cols-2 mb-8 bg-background/50 backdrop-blur-sm">
            {categories.map((category) => (
              <TabsTrigger 
                key={category.id} 
                value={category.id} 
                className="text-center py-3 text-sm"
              >
                {category.title}
              </TabsTrigger>
            ))}
          </TabsList>

          {categories.map((category) => (
            <TabsContent 
              key={category.id} 
              value={category.id} 
              className="bg-card/50 backdrop-blur-sm border border-border/50 p-6 rounded-lg shadow-sm"
            >
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
                <div className="lg:col-span-2 space-y-4">
                  <h3 className="text-2xl font-bold text-foreground">{category.title}</h3>
                  <p className="text-muted-foreground leading-relaxed">
                    {category.description}
                  </p>
                </div>
                <div className="relative rounded-lg overflow-hidden shadow-md">
                  <SafeImage
                    src={category.imageSrc}
                    alt={category.imageAlt}
                    className="w-full h-full object-cover"
                    fallbackSrc="/images/patient-resources/spine-health.jpg"
                  />
                </div>
              </div>

              <Accordion type="single" collapsible className="w-full space-y-4">
                {category.conditions.map((condition) => (
                  <AccordionItem 
                    key={condition.id} 
                    value={condition.id}
                    className="bg-background/50 backdrop-blur-sm border border-border/30 rounded-lg px-4"
                  >
                    <AccordionTrigger className="text-lg font-semibold text-foreground hover:no-underline">
                      {condition.title}
                    </AccordionTrigger>
                    <AccordionContent className="text-muted-foreground space-y-4 pt-2">
                      <p className="leading-relaxed">
                        {condition.description}
                      </p>
                      <div>
                        <h4 className="text-base font-semibold mb-3 text-foreground">
                          Recommended Lifestyle Modifications:
                        </h4>
                        <ul className="space-y-2">
                          {condition.modifications.map((modification, index) => (
                            <li key={index} className="flex items-start">
                              <span className="text-primary mr-2 mt-0.5 font-bold">•</span>
                              <span className="text-sm leading-relaxed">{modification}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    </AccordionContent>
                  </AccordionItem>
                ))}
              </Accordion>
            </TabsContent>
          ))}
        </Tabs>
      </div>
    </section>
  );
};

ConditionSpecificSection.displayName = 'ConditionSpecificSection';

export default ConditionSpecificSection;

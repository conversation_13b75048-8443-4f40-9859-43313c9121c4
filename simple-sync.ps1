# Simple Translation Synchronization Script

Write-Host "Translation Synchronization Fix" -ForegroundColor Cyan
Write-Host "================================" -ForegroundColor Cyan

$enPath = "src/locales/en.ts"
$zhPath = "src/locales/zh.ts"
$backupPath = "src/locales/zh.ts.backup"

# Check if files exist
if (-not (Test-Path $enPath)) {
    Write-Host "Error: English translation file not found: $enPath" -ForegroundColor Red
    exit 1
}

if (-not (Test-Path $zhPath)) {
    Write-Host "Error: Chinese translation file not found: $zhPath" -ForegroundColor Red
    exit 1
}

Write-Host "Loading translation files..." -ForegroundColor Yellow

# Create backup
Write-Host "Creating backup of Chinese translations..." -ForegroundColor Yellow
Copy-Item $zhPath $backupPath -Force

# Read files
$enContent = Get-Content $enPath -Raw -Encoding UTF8
$zhContent = Get-Content $zhPath -Raw -Encoding UTF8

# Simple key extraction using regex
function Get-TranslationKeys {
    param([string]$Content)
    
    $keys = @()
    $matches = [regex]::Matches($Content, '(\w+)\s*:\s*["{]')
    
    foreach ($match in $matches) {
        $keys += $match.Groups[1].Value
    }
    
    return $keys | Sort-Object | Get-Unique
}

Write-Host "Extracting keys..." -ForegroundColor Yellow

$enKeys = Get-TranslationKeys $enContent
$zhKeys = Get-TranslationKeys $zhContent

Write-Host "Analysis Results:" -ForegroundColor Green
Write-Host "  English keys: $($enKeys.Count)" -ForegroundColor Gray
Write-Host "  Chinese keys: $($zhKeys.Count)" -ForegroundColor Gray

$enKeySet = [System.Collections.Generic.HashSet[string]]::new($enKeys)
$zhKeySet = [System.Collections.Generic.HashSet[string]]::new($zhKeys)

$missingInZh = @()
$extraInZh = @()

foreach ($key in $enKeys) {
    if (-not $zhKeySet.Contains($key)) {
        $missingInZh += $key
    }
}

foreach ($key in $zhKeys) {
    if (-not $enKeySet.Contains($key)) {
        $extraInZh += $key
    }
}

$commonKeys = $enKeys | Where-Object { $zhKeySet.Contains($_) }

Write-Host "  Common keys: $($commonKeys.Count)" -ForegroundColor Gray
Write-Host "  Missing in Chinese: $($missingInZh.Count)" -ForegroundColor Red
Write-Host "  Extra in Chinese: $($extraInZh.Count)" -ForegroundColor Yellow

# Create report
$report = @{
    timestamp = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
    summary = @{
        originalEnglishKeys = $enKeys.Count
        originalChineseKeys = $zhKeys.Count
        commonKeys = $commonKeys.Count
        missingInChinese = $missingInZh.Count
        extraInChinese = $extraInZh.Count
        backupCreated = $backupPath
    }
    missingKeys = $missingInZh
    extraKeys = $extraInZh
    recommendations = @(
        "Review and translate the missing keys"
        "Consider removing extra keys that do not exist in English"
        "Implement automated translation key validation"
        "Use translation management tools for better synchronization"
    )
}

$report | ConvertTo-Json -Depth 10 | Out-File "translation-sync-report.json" -Encoding UTF8

Write-Host "Translation synchronization analysis completed!" -ForegroundColor Green
Write-Host "Report saved to: translation-sync-report.json" -ForegroundColor Green
Write-Host "Backup saved to: $backupPath" -ForegroundColor Green

if ($missingInZh.Count -gt 0) {
    Write-Host "`nMissing keys (first 10):" -ForegroundColor Yellow
    $missingInZh | Select-Object -First 10 | ForEach-Object { 
        Write-Host "  - $_" -ForegroundColor Yellow 
    }
}

if ($extraInZh.Count -gt 0) {
    Write-Host "`nExtra keys in Chinese (first 10):" -ForegroundColor Yellow
    $extraInZh | Select-Object -First 10 | ForEach-Object { 
        Write-Host "  + $_" -ForegroundColor Yellow 
    }
}

Write-Host "`nNext Steps:" -ForegroundColor Cyan
Write-Host "1. Review the generated report" -ForegroundColor Gray
Write-Host "2. Manually add missing translations to the Chinese file" -ForegroundColor Gray
Write-Host "3. Remove or translate extra keys" -ForegroundColor Gray
Write-Host "4. Test the application with updated translations" -ForegroundColor Gray

Write-Host "`nSummary:" -ForegroundColor Cyan
Write-Host "  [OK] Backup created" -ForegroundColor Green
Write-Host "  [OK] Analysis completed" -ForegroundColor Green
Write-Host "  [OK] Report generated" -ForegroundColor Green

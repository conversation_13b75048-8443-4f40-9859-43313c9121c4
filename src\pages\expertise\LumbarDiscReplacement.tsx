import React, { useEffect } from 'react';
import { Link } from 'react-router-dom';

import BenefitsGrid from '@/components/expertise/BenefitsGrid';
import ExpertiseCallToAction from '@/components/expertise/ExpertiseCallToAction';
import ExpertiseHero from '@/components/expertise/ExpertiseHero';
import ExpertiseSection from '@/components/expertise/ExpertiseSection';
import ExpertiseSidebar from '@/components/expertise/ExpertiseSidebar';
import SafeImage from '@/components/SafeImage';
import StandardPageLayout from '@/components/StandardPageLayout';
import { Button } from '@/components/ui/button';
import { lumbarDiscReplacementData } from '@/data/expertise/lumbarDiscReplacementData';

/**
 * Lumbar Disc Replacement Component
 * Unified implementation combining comprehensive content with modular architecture
 * Includes all sections from both original and refactored versions plus documentation content
 */

const LumbarDiscReplacement: React.FC = () => {
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  const data = lumbarDiscReplacementData;

  // Sub-pages navigation data
  const subPages = [
    {
      title: 'Technology & Implants',
      description: 'Explore the advanced technology behind artificial lumbar discs, materials, and device examples.',
      link: '/expertise/lumbar-disc-replacement/technology-implants',
      highlights: ['Implant materials & designs', 'Biomechanical engineering', 'Device examples (ProDisc-L, M6-L)', 'Design goals & features']
    },
    {
      title: 'Surgery & Recovery',
      description: 'Comprehensive guide to the surgical procedure, patient selection, and recovery process.',
      link: '/expertise/lumbar-disc-replacement/surgery-recovery',
      highlights: ['Patient selection criteria', 'Anterior surgical approach', 'Recovery timeline', 'Postoperative care']
    },
    {
      title: 'Risks & Comparison',
      description: 'Understanding risks, complications, and how disc replacement compares to fusion surgery.',
      link: '/expertise/lumbar-disc-replacement/risks-comparison',
      highlights: ['Risk assessment', 'Safety statistics', 'Comparison with ALIF', 'Clinical outcomes']
    }
  ];

  return (
    <StandardPageLayout title="Lumbar Disc Replacement" showHeader={false}>
      <ExpertiseHero
        title={data.hero.title}
        subtitle={data.hero.subtitle}
        backgroundImage={data.hero.backgroundImage}
      />

      <div className="flex-1">
        {/* Main Content */}
        <section className="py-16">
          <div className="container">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
              <div className="lg:col-span-2">
                {/* Main Sections */}
                {data.sections.map((section) => (
                  <ExpertiseSection key={section.id} section={section} />
                ))}

                {/* Sub-pages Navigation */}
                <div className="mb-8">
                  <h2 className="text-2xl font-bold mb-6">Explore Detailed Information</h2>
                  <p className="text-muted-foreground mb-6">
                    Dive deeper into specific aspects of lumbar disc replacement with our comprehensive sub-pages:
                  </p>

                  <div className="grid grid-cols-1 md:grid-cols-1 gap-6">
                    {subPages.map((subPage, index) => (
                      <div key={index} className="border rounded-lg p-6 bg-card hover:shadow-lg transition-shadow">
                        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                          <div className="flex-1 mb-4 lg:mb-0">
                            <h3 className="text-xl font-semibold mb-3 text-primary">{subPage.title}</h3>
                            <p className="text-muted-foreground mb-4">{subPage.description}</p>
                            <div className="grid grid-cols-2 gap-2">
                              {subPage.highlights.map((highlight, highlightIndex) => (
                                <div key={highlightIndex} className="flex items-center text-sm text-muted-foreground">
                                  <span className="w-2 h-2 bg-primary rounded-full mr-2"></span>
                                  {highlight}
                                </div>
                              ))}
                            </div>
                          </div>
                          <div className="lg:ml-6">
                            <Button asChild size="lg">
                              <Link to={subPage.link}>
                                Learn More →
                              </Link>
                            </Button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Recommendations */}
                <div className="mb-8">
                  <h2 className="text-2xl font-bold mb-6">{data.recommendations.title}</h2>
                  <p className="text-muted-foreground mb-6">
                    {data.recommendations.description}
                  </p>
                  <ul className="list-disc pl-6 space-y-2 text-muted-foreground">
                    {data.recommendations.conditions.map((condition, index) => (
                      <li key={index}>{condition}</li>
                    ))}
                  </ul>
                </div>

                {/* Benefits Grid */}
                <BenefitsGrid benefits={data.benefits} />

                {/* Quick Overview of Key Benefits */}
                <div className="mb-8">
                  <h2 className="text-2xl font-bold mb-6">Why Choose Lumbar Disc Replacement?</h2>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div className="text-center p-6 bg-primary/5 rounded-lg">
                      <div className="text-3xl font-bold text-primary mb-2">85-90%</div>
                      <div className="text-sm text-muted-foreground">Patient satisfaction in ideal candidates</div>
                    </div>
                    <div className="text-center p-6 bg-primary/5 rounded-lg">
                      <div className="text-3xl font-bold text-primary mb-2">Motion</div>
                      <div className="text-sm text-muted-foreground">Preserved at treated level</div>
                    </div>
                    <div className="text-center p-6 bg-primary/5 rounded-lg">
                      <div className="text-3xl font-bold text-primary mb-2">&lt;1%</div>
                      <div className="text-sm text-muted-foreground">Risk of permanent disability</div>
                    </div>
                  </div>
                </div>

                {/* Dr. Aliashkevich's Experience */}
                <div className="mb-8">
                  <h2 className="text-2xl font-bold mb-6">Dr. Aliashkevich's Experience</h2>
                  <div className="bg-primary/10 p-6 rounded-lg">
                    <p className="text-muted-foreground">
                      Dr Ales Aliashkevich is a strong advocate for motion-preserving spinal surgery. He has been using lumbar disc replacement as an alternative to spinal fusion to maintain segmental mobility in hundreds of patients with chronic back pain and radiculopathy since 2012. The validity of this approach was confirmed by the excellent surgery results with very low complication rates. Over the years, he has gained extensive experience in single and multilevel arthroplasty and hybrid procedures.
                    </p>
                  </div>
                </div>

                {/* Goals of Treatment */}
                <div className="mb-8">
                  <h2 className="text-2xl font-bold mb-6">Goals of Lumbar Disc Replacement</h2>
                  <div className="bg-primary/5 p-6 rounded-lg">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <ul className="list-disc pl-5 space-y-2 text-muted-foreground">
                        <li>Reduction of back and radicular leg pain</li>
                        <li>Medication reduction</li>
                        <li>Prevention of disc and facet joint degeneration</li>
                        <li>Improved posture, lower back and leg function</li>
                      </ul>
                      <ul className="list-disc pl-5 space-y-2 text-muted-foreground">
                        <li>Improved work and recreational capacity</li>
                        <li>Improved life quality</li>
                        <li>Return to normal activities</li>
                        <li>Preservation of spinal motion</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>

              {/* Sidebar */}
              <div className="lg:col-span-1">
                <ExpertiseSidebar sections={data.sidebar} />
              </div>
            </div>
          </div>
        </section>

        {/* Call to Action */}
        <ExpertiseCallToAction
          title={data.callToAction.title}
          description={data.callToAction.description}
          primaryButton={data.callToAction.primaryButton}
          secondaryButton={data.callToAction.secondaryButton}
        />
      </div>
    </StandardPageLayout>
  );
};

LumbarDiscReplacement.displayName = 'LumbarDiscReplacement';

export default LumbarDiscReplacement;
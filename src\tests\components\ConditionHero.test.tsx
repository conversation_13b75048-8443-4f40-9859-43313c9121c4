import { render, screen } from '@testing-library/react';
import { axe, toHaveNoViolations } from 'jest-axe';
import React from 'react';
import { <PERSON>rowserRouter } from 'react-router-dom';
import { beforeEach, describe, expect, it, vi } from 'vitest';

import ConditionHero from '@/components/medical-conditions/ConditionHero';
import { DeviceProvider, useDeviceDetection } from '@/contexts/DeviceContext';
import { useLanguage } from '@/contexts/LanguageContext';
import { mockUtils } from '@/lib/test-utils';

// Extend Jest matchers
expect.extend(toHaveNoViolations);

// Mock dependencies
vi.mock('@/contexts/DeviceContext', () => ({
  DeviceProvider: ({ children }: { children: React.ReactNode }) => children,
  useDeviceDetection: vi.fn(() => ({
    isMobile: false,
    isTablet: false,
    isDesktop: true,
    deviceType: 'desktop',
  })),
}));

vi.mock('@/contexts/LanguageContext', () => ({
  LanguageProvider: ({ children }: { children: React.ReactNode }) => children,
  useLanguage: vi.fn(() => ({
    t: (key: string) => key,
    language: 'en',
    setLanguage: vi.fn(),
  })),
}));

// Import the mocked functions

// Get the mocked functions for use in tests
const mockUseDeviceDetection = vi.mocked(useDeviceDetection);
const mockUseLanguage = vi.mocked(useLanguage);

// Test wrapper component
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <BrowserRouter>
      <DeviceProvider>
        <LanguageProvider>
          {children}
        </LanguageProvider>
      </DeviceProvider>
    </BrowserRouter>
  );
};

// Mock data
const mockHeroData = {
  title: 'Herniated Disc Treatment',
  subtitle: 'Comprehensive care for disc herniation and related spinal conditions',
  backgroundImage: '/images/medical-conditions/herniated-disc-hero.jpg',
  breadcrumbs: [
    { label: 'Home', href: '/' },
    { label: 'Patient Resources', href: '/patient-resources' },
    { label: 'Medical Conditions', href: '/patient-resources/conditions' },
    { label: 'Herniated Disc', href: '/patient-resources/conditions/herniated-disc' },
  ],
};

describe('ConditionHero', () => {
  beforeEach(() => {
    // Use standardized mock reset
    mockUtils.resetAllMocks();
  });

  describe('Basic Rendering', () => {
    it('renders hero title correctly', () => {
      render(
        <TestWrapper>
          <ConditionHero data={mockHeroData} />
        </TestWrapper>
      );

      expect(screen.getByText('Herniated Disc Treatment')).toBeInTheDocument();
    });

    it('renders hero subtitle correctly', () => {
      render(
        <TestWrapper>
          <ConditionHero data={mockHeroData} />
        </TestWrapper>
      );

      expect(screen.getByText('Comprehensive care for disc herniation and related spinal conditions')).toBeInTheDocument();
    });

    it('renders without subtitle when not provided', () => {
      const dataWithoutSubtitle = {
        ...mockHeroData,
        subtitle: undefined,
      };

      render(
        <TestWrapper>
          <ConditionHero data={dataWithoutSubtitle} />
        </TestWrapper>
      );

      expect(screen.getByText('Herniated Disc Treatment')).toBeInTheDocument();
      expect(screen.queryByText('Comprehensive care for disc herniation and related spinal conditions')).not.toBeInTheDocument();
    });
  });

  describe('Background Image', () => {
    it('applies background image correctly', () => {
      const { container } = render(
        <TestWrapper>
          <ConditionHero data={mockHeroData} />
        </TestWrapper>
      );

      const heroSection = container.querySelector('[data-testid="condition-hero"]');
      expect(heroSection).toHaveStyle({
        backgroundImage: 'url(/images/medical-conditions/herniated-disc-hero.jpg)',
      });
    });

    it('handles missing background image gracefully', () => {
      const dataWithoutImage = {
        ...mockHeroData,
        backgroundImage: ''
      };

      render(
        <TestWrapper>
          <ConditionHero data={dataWithoutImage} />
        </TestWrapper>
      );

      expect(screen.getByText('Herniated Disc Treatment')).toBeInTheDocument();
    });
  });

  describe('Breadcrumb Navigation', () => {
    it('renders breadcrumbs when provided', () => {
      render(
        <TestWrapper>
          <ConditionHero data={mockHeroData} />
        </TestWrapper>
      );

      // Check for breadcrumb navigation
      expect(screen.getByRole('navigation', { name: /breadcrumb/i })).toBeInTheDocument();
      expect(screen.getByText('Home')).toBeInTheDocument();
      expect(screen.getByText('Patient Resources')).toBeInTheDocument();
      expect(screen.getByText('Medical Conditions')).toBeInTheDocument();
      expect(screen.getByText('Herniated Disc')).toBeInTheDocument();
    });

    it('renders without breadcrumbs when not provided', () => {
      const dataWithoutBreadcrumbs = {
        ...mockHeroData,
        breadcrumbs: undefined
      };

      render(
        <TestWrapper>
          <ConditionHero data={dataWithoutBreadcrumbs} />
        </TestWrapper>
      );

      expect(screen.queryByRole('navigation', { name: /breadcrumb/i })).not.toBeInTheDocument();
    });

    it('creates proper breadcrumb links', () => {
      render(
        <TestWrapper>
          <ConditionHero data={mockHeroData} />
        </TestWrapper>
      );

      const homeLink = screen.getByRole('link', { name: 'Home' });
      expect(homeLink).toHaveAttribute('href', '/');

      const patientResourcesLink = screen.getByRole('link', { name: 'Patient Resources' });
      expect(patientResourcesLink).toHaveAttribute('href', '/patient-resources');
    });
  });

  describe('Accessibility', () => {
    it('should not have accessibility violations', async () => {
      const { container } = render(
        <TestWrapper>
          <ConditionHero data={mockHeroData} />
        </TestWrapper>
      );

      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });

    it('has proper heading hierarchy', () => {
      render(
        <TestWrapper>
          <ConditionHero data={mockHeroData} />
        </TestWrapper>
      );

      const heading = screen.getByRole('heading', { level: 1 });
      expect(heading).toHaveTextContent('Herniated Disc Treatment');
    });

    it('has proper semantic structure', () => {
      render(
        <TestWrapper>
          <ConditionHero data={mockHeroData} />
        </TestWrapper>
      );

      // Check for semantic elements
      expect(screen.getByRole('banner')).toBeInTheDocument();
      expect(screen.getByRole('navigation')).toBeInTheDocument();
    });

    it('provides proper ARIA labels', () => {
      render(
        <TestWrapper>
          <ConditionHero data={mockHeroData} />
        </TestWrapper>
      );

      const heroSection = screen.getByRole('banner');
      expect(heroSection).toHaveAttribute('aria-label', expect.stringContaining('Herniated Disc Treatment'));
    });
  });

  describe('Responsive Design', () => {
    it('adapts to mobile viewport', () => {
      // Mock mobile device detection
      mockUseDeviceDetection.mockReturnValue({
        isMobile: true,
        isTablet: false,
        isDesktop: false,
        deviceType: 'mobile',
      });

      render(
        <TestWrapper>
          <ConditionHero data={mockHeroData} />
        </TestWrapper>
      );

      const heroSection = screen.getByRole('banner');
      expect(heroSection).toHaveClass('mobile-optimized');
    });

    it('adapts to tablet viewport', () => {
      // Mock tablet device detection
      mockUseDeviceDetection.mockReturnValue({
        isMobile: false,
        isTablet: true,
        isDesktop: false,
        deviceType: 'tablet',
      });

      render(
        <TestWrapper>
          <ConditionHero data={mockHeroData} />
        </TestWrapper>
      );

      const heroSection = screen.getByRole('banner');
      expect(heroSection).toHaveClass('tablet-optimized');
    });
  });

  describe('Performance', () => {
    it('renders efficiently', () => {
      const { rerender } = render(
        <TestWrapper>
          <ConditionHero data={mockHeroData} />
        </TestWrapper>
      );

      // Re-render with same props
      rerender(
        <TestWrapper>
          <ConditionHero data={mockHeroData} />
        </TestWrapper>
      );

      expect(screen.getByText('Herniated Disc Treatment')).toBeInTheDocument();
    });

    it('handles data updates correctly', () => {
      const { rerender } = render(
        <TestWrapper>
          <ConditionHero data={mockHeroData} />
        </TestWrapper>
      );

      const updatedData = {
        ...mockHeroData,
        title: 'Updated Condition Title'
      };

      rerender(
        <TestWrapper>
          <ConditionHero data={updatedData} />
        </TestWrapper>
      );

      expect(screen.getByText('Updated Condition Title')).toBeInTheDocument();
      expect(screen.queryByText('Herniated Disc Treatment')).not.toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    it('handles missing data gracefully', () => {
      const minimalData = {
        title: 'Minimal Title',
        subtitle: '',
        backgroundImage: ''
      };

      render(
        <TestWrapper>
          <ConditionHero data={minimalData} />
        </TestWrapper>
      );

      expect(screen.getByText('Minimal Title')).toBeInTheDocument();
    });

    it('handles empty breadcrumbs array', () => {
      const dataWithEmptyBreadcrumbs = {
        ...mockHeroData,
        breadcrumbs: []
      };

      render(
        <TestWrapper>
          <ConditionHero data={dataWithEmptyBreadcrumbs} />
        </TestWrapper>
      );

      expect(screen.queryByRole('navigation', { name: /breadcrumb/i })).not.toBeInTheDocument();
    });
  });

  describe('Integration', () => {
    it('integrates with language context', () => {
      mockUseLanguage.mockReturnValue({
        t: (key: string) => `translated_${key}`,
        language: 'en',
        setLanguage: vi.fn(),
      });

      render(
        <TestWrapper>
          <ConditionHero data={mockHeroData} />
        </TestWrapper>
      );

      // Check that translation function is used
      expect(mockUseLanguage).toHaveBeenCalled();
    });

    it('integrates with device context', () => {
      render(
        <TestWrapper>
          <ConditionHero data={mockHeroData} />
        </TestWrapper>
      );

      expect(mockUseDeviceDetection).toHaveBeenCalled();
    });
  });
});

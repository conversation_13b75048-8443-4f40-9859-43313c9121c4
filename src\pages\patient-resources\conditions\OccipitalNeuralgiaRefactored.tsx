import React, { useEffect } from 'react';

import { ConditionCauses } from '@/components/medical-conditions/shared/ConditionCauses';
import { ConditionHero } from '@/components/medical-conditions/shared/ConditionHero';
import ConditionOverviewSection from '@/components/medical-conditions/shared/ConditionOverviewSection';
import ConditionQuickFacts from '@/components/medical-conditions/shared/ConditionQuickFacts';
import { ConditionSymptoms } from '@/components/medical-conditions/shared/ConditionSymptoms';
import { ConditionTreatment } from '@/components/medical-conditions/shared/ConditionTreatment';
import StandardPageLayout from '@/components/StandardPageLayout';
import { occipitalNeuralgiaData } from '@/data/conditions/occipitalneuralgia';

/**
 * Refactored Occipital Neuralgia Component
 * 
 * Original component: 1,502 lines
 * Refactored component: <100 lines
 * Reduction: ~95%
 */

const OccipitalNeuralgiaRefactored: React.FC = () => {
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  return (
    <StandardPageLayout 
      title="Occipital Neuralgia - Comprehensive Guide" 
      showHeader={false}
    >
      <main className="flex-1 pt-20">
        <ConditionHero
          title={occipitalNeuralgiaData.hero.title}
          subtitle={occipitalNeuralgiaData.hero.subtitle}
          backgroundImage={occipitalNeuralgiaData.hero.backgroundImage}
          badge={occipitalNeuralgiaData.hero.badge}
        />

        <ConditionQuickFacts facts={occipitalNeuralgiaData.quickFacts} />

        <ConditionOverviewSection
          title={occipitalNeuralgiaData.overview.title}
          description={occipitalNeuralgiaData.overview.description}
          keyPoints={occipitalNeuralgiaData.overview.keyPoints}
          imageSrc={occipitalNeuralgiaData.overview.imageSrc}
          imageAlt={occipitalNeuralgiaData.overview.imageAlt}
          imageCaption={occipitalNeuralgiaData.overview.imageCaption}
        />

        <ConditionCauses
          causes={occipitalNeuralgiaData.causes}
          riskFactors={occipitalNeuralgiaData.riskFactors}
        />

        <ConditionSymptoms
          symptomCategories={occipitalNeuralgiaData.symptoms}
          warningSigns={occipitalNeuralgiaData.warningSigns}
        />

        <ConditionTreatment
          conservativeOptions={occipitalNeuralgiaData.conservativeTreatments}
          surgicalOptions={occipitalNeuralgiaData.surgicalTreatments}
        />
      </main>
    </StandardPageLayout>
  );
};

OccipitalNeuralgiaRefactored.displayName = 'OccipitalNeuralgiaRefactored';

export default OccipitalNeuralgiaRefactored;

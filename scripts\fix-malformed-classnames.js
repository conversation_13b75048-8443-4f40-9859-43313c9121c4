#!/usr/bin/env node

/**
 * Malformed ClassName Fix Script
 * 
 * Finds and fixes malformed className attributes that cause syntax errors
 * in React/JSX files. Specifically targets patterns like:
 * - className={cn("flex,
 * - className={cn("container,
 * And other unclosed quote patterns.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🔧 MALFORMED CLASSNAME FIX');
console.log('='.repeat(50));

/**
 * Patterns to detect and fix malformed className attributes
 */
const MALFORMED_PATTERNS = [
  {
    // Pattern: className={cn("flex,
    pattern: /className=\{cn\(\s*"([^"]*),\s*\n/g,
    fix: (match, classes) => `className={cn(\n        "${classes}",\n`,
    description: 'Fixed unclosed className with cn() function'
  },
  {
    // Pattern: className={cn("container,
    pattern: /className=\{cn\(\s*"([^"]*),\s*\n/g,
    fix: (match, classes) => `className={cn(\n        "${classes}",\n`,
    description: 'Fixed unclosed className with cn() function'
  },
  {
    // Pattern: className="some-class,
    pattern: /className=\s*"([^"]*),\s*\n/g,
    fix: (match, classes) => `className="${classes}"\n`,
    description: 'Fixed unclosed className string'
  },
  {
    // Pattern: Missing closing brace in cn()
    pattern: /className=\{cn\(\s*"([^"]+)"\s*,\s*\n\s*([^}]+)\s*\)\s*\}/g,
    fix: (match, classes, condition) => `className={cn("${classes}", ${condition.trim()})}`,
    description: 'Fixed malformed cn() function call'
  }
];

/**
 * Scan directory for React/JSX files
 */
function scanForReactFiles(dir, files = []) {
  const items = fs.readdirSync(dir);
  
  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory()) {
      if (!item.startsWith('.') && item !== 'node_modules' && item !== 'dist') {
        scanForReactFiles(fullPath, files);
      }
    } else if (item.match(/\.(tsx|jsx)$/)) {
      files.push(fullPath);
    }
  }
  
  return files;
}

/**
 * Check if file contains malformed className patterns
 */
function hasMalformedClassNames(content) {
  // Look for specific problematic patterns
  const problematicPatterns = [
    /className=\{cn\(\s*"[^"]*,\s*\n/,  // className={cn("flex,
    /className=\s*"[^"]*,\s*\n/,        // className="flex,
    /<\w+\s+className=\{cn\(\s*"[^"]*,\s*$/m, // Incomplete cn() calls
  ];
  
  return problematicPatterns.some(pattern => pattern.test(content));
}

/**
 * Fix malformed className patterns in content
 */
function fixMalformedClassNames(content, filePath) {
  let fixed = content;
  let changes = 0;
  const appliedFixes = [];

  // Fix specific known patterns
  const specificFixes = [
    {
      // Fix: className={cn("flex, -> className={cn("flex",
      from: /className=\{cn\(\s*"([^"]*),\s*\n\s*([^}]+)\s*\)\s*\}/g,
      to: (match, classes, condition) => {
        const cleanCondition = condition.trim().replace(/\s+/g, ' ');
        return `className={cn("${classes}", ${cleanCondition})}`;
      },
      desc: 'Fixed malformed cn() className'
    },
    {
      // Fix: className={cn("container, -> className={cn("container",
      from: /className=\{cn\(\s*"([^"]*),\s*\n/g,
      to: 'className={cn("$1",\n',
      desc: 'Fixed unclosed className quote in cn()'
    },
    {
      // Fix: <main className={cn("container, -> <div className={cn("container",
      from: /<(main|section|div)\s+className=\{cn\(\s*"([^"]*),\s*\n/g,
      to: '<$1 className={cn("$2",\n',
      desc: 'Fixed unclosed className in HTML elements'
    },
    {
      // Fix missing closing quotes in simple className
      from: /className=\s*"([^"]*),\s*\n/g,
      to: 'className="$1"\n',
      desc: 'Fixed unclosed className string'
    }
  ];

  for (const fix of specificFixes) {
    const beforeContent = fixed;
    if (typeof fix.to === 'function') {
      fixed = fixed.replace(fix.from, fix.to);
    } else {
      fixed = fixed.replace(fix.from, fix.to);
    }
    
    if (fixed !== beforeContent) {
      changes++;
      appliedFixes.push(fix.desc);
    }
  }

  return { content: fixed, changes, appliedFixes };
}

/**
 * Process a single file
 */
function processFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const relativePath = path.relative(process.cwd(), filePath);
    
    if (!hasMalformedClassNames(content)) {
      return { processed: false, reason: 'No malformed classNames found' };
    }

    console.log(`🔧 Processing: ${relativePath}`);
    
    const result = fixMalformedClassNames(content, filePath);
    
    if (result.changes > 0) {
      // Create backup
      const backupPath = filePath + '.backup';
      fs.copyFileSync(filePath, backupPath);
      
      // Write fixed content
      fs.writeFileSync(filePath, result.content, 'utf8');
      
      console.log(`  ✅ Fixed ${result.changes} issues`);
      result.appliedFixes.forEach(fix => {
        console.log(`    - ${fix}`);
      });
      
      return {
        processed: true,
        changes: result.changes,
        appliedFixes: result.appliedFixes,
        backupPath
      };
    }
    
    return { processed: false, reason: 'No changes needed' };
    
  } catch (error) {
    console.log(`  ❌ Error: ${error.message}`);
    return { processed: false, reason: error.message };
  }
}

/**
 * Main execution function
 */
function main() {
  console.log('📋 Scanning for React/JSX files with malformed classNames...');
  
  const reactFiles = scanForReactFiles('src');
  console.log(`Found ${reactFiles.length} React/JSX files`);

  const results = {
    total: reactFiles.length,
    processed: 0,
    skipped: 0,
    errors: 0,
    totalChanges: 0,
    processedFiles: []
  };

  console.log('\n🔧 Processing files...');

  for (const filePath of reactFiles) {
    const result = processFile(filePath);
    
    if (result.processed) {
      results.processed++;
      results.totalChanges += result.changes;
      results.processedFiles.push({
        file: path.relative(process.cwd(), filePath),
        changes: result.changes,
        fixes: result.appliedFixes,
        backup: result.backupPath
      });
    } else {
      if (result.reason.includes('Error')) {
        results.errors++;
      } else {
        results.skipped++;
      }
    }
  }

  // Generate summary
  console.log('\n📊 MALFORMED CLASSNAME FIX SUMMARY');
  console.log('='.repeat(40));
  console.log(`Total files scanned: ${results.total}`);
  console.log(`Files processed: ${results.processed}`);
  console.log(`Files skipped: ${results.skipped}`);
  console.log(`Errors: ${results.errors}`);
  console.log(`Total changes made: ${results.totalChanges}`);

  if (results.processed > 0) {
    console.log('\n✅ SUCCESSFULLY PROCESSED FILES:');
    results.processedFiles.forEach(file => {
      console.log(`  • ${file.file} (${file.changes} changes)`);
      file.fixes.forEach(fix => console.log(`    - ${fix}`));
    });
  }

  // Save detailed report
  const report = {
    timestamp: new Date().toISOString(),
    summary: results,
    patternsFixed: [
      'Unclosed className quotes in cn() functions',
      'Malformed className attributes',
      'Missing closing braces in className expressions',
      'Incomplete cn() function calls'
    ],
    recommendations: [
      'Test all fixed components to ensure they render correctly',
      'Review className logic for any unintended changes',
      'Consider using ESLint rules to prevent similar issues',
      'Update development workflow to catch syntax errors early'
    ]
  };

  fs.writeFileSync('malformed-classname-fix-report.json', JSON.stringify(report, null, 2));
  console.log('\n📄 Detailed report saved to: malformed-classname-fix-report.json');

  return results;
}

// Run if called directly
if (process.argv[1] && process.argv[1].endsWith('fix-malformed-classnames.js')) {
  main();
}

export { main as fixMalformedClassNames };

import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { toHaveNoViolations } from 'jest-axe';
import React from 'react';
import { vi, describe, it, expect, beforeEach } from 'vitest';

import AsyncContent from '@/components/AsyncContent';
import ErrorBoundary from '@/components/ErrorBoundary';
import { mockUtils, ThrowError, TestWrapper } from '@/lib/test-utils';
import { setupAllStandardMocks } from '@/tests/utils/standard-mocks';

// Add jest-axe matchers
expect.extend(toHaveNoViolations);

// Setup standardized mocks
setupAllStandardMocks();

// Mock network-dependent components
const MockNetworkComponent: React.FC<{ shouldFail?: boolean; errorType?: string }> = ({ 
  shouldFail = false, 
  errorType = 'network' 
}) => {
  const [loading, setLoading] = React.useState(false);
  const [error, setError] = React.useState<string | null>(null);
  const [data, setData] = React.useState<Record<string, unknown> | null>(null);

  const fetchData = React.useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      if (shouldFail) {
        throw new Error(
          errorType === 'network' ? 'Network Error: Failed to fetch' :
          errorType === 'timeout' ? 'Request Timeout' :
          errorType === 'server' ? 'Server Error: 500' :
          'Unknown Error'
        );
      }

      // Simulate successful response
      await new Promise(resolve => setTimeout(resolve, 100));
      setData({ message: 'Data loaded successfully' });
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  }, [shouldFail, errorType]);

  React.useEffect(() => {
    // Immediate execution to avoid async issues in tests
    if (shouldFail) {
      setError(
        errorType === 'network' ? 'Network Error: Failed to fetch' :
        errorType === 'timeout' ? 'Request Timeout' :
        errorType === 'server' ? 'Server Error: 500' :
        'Unknown Error'
      );
    } else {
      setData({ message: 'Data loaded successfully' });
    }
  }, [shouldFail, errorType]);

  if (loading) {
    return <div data-testid="loading-state">Loading...</div>;
  }

  if (error) {
    return (
      <div data-testid="error-state">
        <p data-testid="error-message">{error}</p>
        <button onClick={fetchData} data-testid="retry-button">
          Retry
        </button>
      </div>
    );
  }

  return (
    <div data-testid="success-state">
      <p data-testid="success-message">{data?.message}</p>
    </div>
  );
};

const MockFormComponent: React.FC<{ shouldFail?: boolean }> = ({ shouldFail = false }) => {
  const [submitting, setSubmitting] = React.useState(false);
  const [error, setError] = React.useState<string | null>(null);
  const [success, setSuccess] = React.useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitting(true);
    setError(null);

    // Immediate execution to avoid async issues in tests
    setTimeout(() => {
      if (shouldFail) {
        setError('Network Error: Failed to submit form');
      } else {
        setSuccess(true);
      }
      setSubmitting(false);
    }, 10); // Very short delay to simulate async behavior
  };

  if (success) {
    return <div data-testid="form-success">Form submitted successfully!</div>;
  }

  return (
    <form onSubmit={handleSubmit} data-testid="network-form">
      <input 
        type="text" 
        placeholder="Name" 
        data-testid="form-input"
        required 
      />
      {error && (
        <div data-testid="form-error" role="alert">
          {error}
        </div>
      )}
      <button 
        type="submit" 
        disabled={submitting}
        data-testid="submit-button"
      >
        {submitting ? 'Submitting...' : 'Submit'}
      </button>
    </form>
  );
};

// Mock dependencies
vi.mock('@/contexts/LanguageContext', () => ({
  useLanguage: vi.fn(() => ({
    language: 'en',
    setLanguage: vi.fn(),
    t: vi.fn((key: string) => {
      const translations: Record<string, string> = {
        'error.network': 'Network connection failed',
        'error.timeout': 'Request timed out',
        'error.server': 'Server error occurred',
        'error.retry': 'Retry',
        'error.generic': 'An error occurred'
      };
      return translations[key] || key;
    }),
    isRTL: false
  }))
}));

vi.mock('@/contexts/DeviceContext', () => ({
  useDevice: vi.fn(() => ({
    isMobile: false,
    isTablet: false,
    isDesktop: true,
    screenSize: 'desktop'
  })),
  useDeviceDetection: vi.fn(() => ({
    isMobile: false,
    isTablet: false,
    isDesktop: true,
    screenSize: 'desktop'
  }))
}));

describe('Network Errors Integration Tests', () => {
  beforeEach(() => {
    mockUtils.resetAllMocks();
    mockUtils.suppressConsoleErrors();
  });

  describe('Network Error Handling', () => {
    it('handles network connection failures gracefully', () => {
      render(
        <TestWrapper>
          <MockNetworkComponent shouldFail={true} errorType="network" />
        </TestWrapper>
      );

      // Since the component renders immediately, no need for waitFor
      expect(screen.getByTestId('error-state')).toBeInTheDocument();
      expect(screen.getByTestId('error-message')).toHaveTextContent('Network Error: Failed to fetch');
      expect(screen.getByTestId('retry-button')).toBeInTheDocument();
    });

    it('handles request timeout errors', () => {
      render(
        <TestWrapper>
          <MockNetworkComponent shouldFail={true} errorType="timeout" />
        </TestWrapper>
      );

      expect(screen.getByTestId('error-state')).toBeInTheDocument();
      expect(screen.getByTestId('error-message')).toHaveTextContent('Request Timeout');
    });

    it('handles server errors (5xx)', () => {
      render(
        <TestWrapper>
          <MockNetworkComponent shouldFail={true} errorType="server" />
        </TestWrapper>
      );

      expect(screen.getByTestId('error-state')).toBeInTheDocument();
      expect(screen.getByTestId('error-message')).toHaveTextContent('Server Error: 500');
    });

    it('allows retry after network error', () => {
      const { rerender } = render(
        <TestWrapper>
          <MockNetworkComponent shouldFail={true} errorType="network" />
        </TestWrapper>
      );

      // Verify error state
      expect(screen.getByTestId('error-state')).toBeInTheDocument();

      // Click retry button
      fireEvent.click(screen.getByTestId('retry-button'));

      // Simulate successful retry
      rerender(
        <TestWrapper>
          <MockNetworkComponent shouldFail={false} />
        </TestWrapper>
      );

      // Verify success state
      expect(screen.getByTestId('success-state')).toBeInTheDocument();
      expect(screen.getByTestId('success-message')).toHaveTextContent('Data loaded successfully');
    });
  });

  describe('Form Submission Error Handling', () => {
    it('handles form submission network errors', async () => {
      render(
        <TestWrapper>
          <MockFormComponent shouldFail={true} />
        </TestWrapper>
      );

      // Fill and submit form
      fireEvent.change(screen.getByTestId('form-input'), {
        target: { value: 'Test User' }
      });
      fireEvent.click(screen.getByTestId('submit-button'));

      // Wait for error state (short timeout since we use 10ms delay)
      await waitFor(() => {
        expect(screen.getByTestId('form-error')).toBeInTheDocument();
        expect(screen.getByTestId('form-error')).toHaveTextContent(
          'Network Error: Failed to submit form'
        );
      }, { timeout: 1000 });

      // Verify form is still accessible for retry
      expect(screen.getByTestId('submit-button')).toBeInTheDocument();
      expect(screen.getByTestId('submit-button')).not.toBeDisabled();
    });

    it('allows form resubmission after error', async () => {
      const { rerender } = render(
        <TestWrapper>
          <MockFormComponent shouldFail={true} />
        </TestWrapper>
      );

      // Submit form and get error
      fireEvent.change(screen.getByTestId('form-input'), {
        target: { value: 'Test User' }
      });
      fireEvent.click(screen.getByTestId('submit-button'));

      await waitFor(() => {
        expect(screen.getByTestId('form-error')).toBeInTheDocument();
      }, { timeout: 1000 });

      // Simulate successful retry
      rerender(
        <TestWrapper>
          <MockFormComponent shouldFail={false} />
        </TestWrapper>
      );

      fireEvent.change(screen.getByTestId('form-input'), {
        target: { value: 'Test User Retry' }
      });
      fireEvent.click(screen.getByTestId('submit-button'));

      await waitFor(() => {
        expect(screen.getByTestId('form-success')).toBeInTheDocument();
      }, { timeout: 1000 });
    });
  });

  describe('Error Boundary Integration', () => {
    it('catches and handles component errors with error boundary', async () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      render(
        <TestWrapper>
          <ErrorBoundary>
            <ThrowError shouldThrow={true} />
          </ErrorBoundary>
        </TestWrapper>
      );

      // Wait for error boundary to catch error
      await waitFor(() => {
        const errorElement = screen.queryByText(/something went wrong/i) ||
                           screen.queryByText(/error/i) ||
                           screen.queryByTestId('error-boundary');
        expect(errorElement).toBeInTheDocument();
      }, { timeout: 10000 });

      consoleSpy.mockRestore();
    }, 15000);

    it('provides error recovery options', async () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      render(
        <TestWrapper>
          <ErrorBoundary>
            <ThrowError shouldThrow={true} />
          </ErrorBoundary>
        </TestWrapper>
      );

      await waitFor(() => {
        // Look for retry or refresh button
        const retryButton = screen.queryByRole('button', { name: /retry/i }) ||
                           screen.queryByRole('button', { name: /refresh/i }) ||
                           screen.queryByTestId('retry-button');

        if (retryButton) {
          expect(retryButton).toBeInTheDocument();
        }
      }, { timeout: 10000 });

      consoleSpy.mockRestore();
    }, 15000);
  });

  describe('AsyncContent Error Integration', () => {
    it('integrates with AsyncContent error states', async () => {
      render(
        <TestWrapper>
          <AsyncContent 
            state="error" 
            error="Network connection failed"
            onRetry={() => {}}
          >
            <div>Content</div>
          </AsyncContent>
        </TestWrapper>
      );

      // Verify error state is displayed
      const errorElement = screen.queryByText(/network connection failed/i) ||
                          screen.queryByText(/error/i);
      expect(errorElement).toBeInTheDocument();

      // Verify retry button is available
      const retryButton = screen.queryByRole('button', { name: /retry/i });
      if (retryButton) {
        expect(retryButton).toBeInTheDocument();
      }
    });

    it('handles loading error state transitions', async () => {
      const { rerender } = render(
        <TestWrapper>
          <AsyncContent state="loading">
            <div>Content</div>
          </AsyncContent>
        </TestWrapper>
      );

      // Verify loading state (handle multiple loading elements)
      const loadingElements = screen.queryAllByText(/loading/i);
      if (loadingElements.length > 0) {
        expect(loadingElements[0]).toBeInTheDocument();
      }

      // Transition to error state
      rerender(
        <TestWrapper>
          <AsyncContent
            state="error"
            error="Failed to load data"
            onRetry={() => {}}
          >
            <div>Content</div>
          </AsyncContent>
        </TestWrapper>
      );

      // Verify error state
      await waitFor(() => {
        const errorElement = screen.queryByText(/failed to load data/i) ||
                            screen.queryByText(/error/i);
        expect(errorElement).toBeInTheDocument();
      }, { timeout: 10000 });
    }, 15000);
  });

  describe('Mobile Error Handling', () => {
    beforeEach(() => {
      // Mobile device context is already mocked by setupAllStandardMocks()
      // No additional setup needed
    });

    it('handles network errors on mobile devices', async () => {
      render(
        <TestWrapper>
          <MockNetworkComponent shouldFail={true} errorType="network" />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('error-state')).toBeInTheDocument();
        expect(screen.getByTestId('retry-button')).toBeInTheDocument();
      }, { timeout: 10000 });

      // Test mobile-friendly retry interaction
      fireEvent.click(screen.getByTestId('retry-button'));
      expect(screen.getByTestId('retry-button')).toBeInTheDocument();
    }, 15000);
  });

  describe('Error Recovery Patterns', () => {
    it('implements progressive error recovery', async () => {
      let attemptCount = 0;
      
      const ProgressiveRetryComponent: React.FC = () => {
        const [error, setError] = React.useState<string | null>(null);
        const [loading, setLoading] = React.useState(false);
        const [success, setSuccess] = React.useState(false);

        const handleRetry = async () => {
          attemptCount++;
          setLoading(true);
          setError(null);

          try {
            // Fail first two attempts, succeed on third
            if (attemptCount < 3) {
              throw new Error(`Attempt ${attemptCount} failed`);
            }
            
            await new Promise(resolve => setTimeout(resolve, 100));
            setSuccess(true);
          } catch (err) {
            setError(err instanceof Error ? err.message : 'Unknown error');
          } finally {
            setLoading(false);
          }
        };

        React.useEffect(() => {
          handleRetry();
        }, []);

        if (success) {
          return <div data-testid="progressive-success">Success after retries!</div>;
        }

        if (loading) {
          return <div data-testid="progressive-loading">Retrying...</div>;
        }

        if (error) {
          return (
            <div data-testid="progressive-error">
              <p>{error}</p>
              <button onClick={handleRetry} data-testid="progressive-retry">
                Retry (Attempt {attemptCount + 1})
              </button>
            </div>
          );
        }

        return null;
      };

      render(
        <TestWrapper>
          <ProgressiveRetryComponent />
        </TestWrapper>
      );

      // First attempt fails
      await waitFor(() => {
        expect(screen.getByTestId('progressive-error')).toBeInTheDocument();
      }, { timeout: 10000 });

      // Second attempt
      fireEvent.click(screen.getByTestId('progressive-retry'));
      await waitFor(() => {
        expect(screen.getByTestId('progressive-error')).toBeInTheDocument();
      }, { timeout: 10000 });

      // Third attempt succeeds
      fireEvent.click(screen.getByTestId('progressive-retry'));
      await waitFor(() => {
        expect(screen.getByTestId('progressive-success')).toBeInTheDocument();
      }, { timeout: 10000 });
    }, 25000);

    it('provides graceful degradation options', async () => {
      const GracefulDegradationComponent: React.FC = () => {
        const [primaryFailed, setPrimaryFailed] = React.useState(false);
        const [fallbackFailed, setFallbackFailed] = React.useState(false);

        const tryPrimary = () => {
          setPrimaryFailed(true); // Simulate primary failure
        };

        const tryFallback = () => {
          setFallbackFailed(true); // Simulate fallback failure
        };

        React.useEffect(() => {
          tryPrimary();
        }, []);

        if (!primaryFailed) {
          return <div data-testid="primary-content">Primary Content</div>;
        }

        if (!fallbackFailed) {
          return (
            <div data-testid="fallback-content">
              <p>Primary service unavailable. Using fallback.</p>
              <button onClick={tryFallback} data-testid="test-fallback">
                Test Fallback
              </button>
            </div>
          );
        }

        return (
          <div data-testid="offline-content">
            <p>All services unavailable. Showing cached content.</p>
          </div>
        );
      };

      render(
        <TestWrapper>
          <GracefulDegradationComponent />
        </TestWrapper>
      );

      // Should show fallback content
      await waitFor(() => {
        expect(screen.getByTestId('fallback-content')).toBeInTheDocument();
      });

      // Test fallback failure
      fireEvent.click(screen.getByTestId('test-fallback'));

      await waitFor(() => {
        expect(screen.getByTestId('offline-content')).toBeInTheDocument();
      });
    });
  });

  describe('Error Accessibility', () => {
    it('provides accessible error messages', async () => {
      render(
        <TestWrapper>
          <MockFormComponent shouldFail={true} />
        </TestWrapper>
      );

      fireEvent.change(screen.getByTestId('form-input'), {
        target: { value: 'Test' }
      });
      fireEvent.click(screen.getByTestId('submit-button'));

      await waitFor(() => {
        const errorElement = screen.getByTestId('form-error');
        expect(errorElement).toBeInTheDocument();
        expect(errorElement).toHaveAttribute('role', 'alert');
      });
    });

    it('maintains focus management during error states', async () => {
      render(
        <TestWrapper>
          <MockNetworkComponent shouldFail={true} />
        </TestWrapper>
      );

      await waitFor(() => {
        const retryButton = screen.getByTestId('retry-button');
        expect(retryButton).toBeInTheDocument();
        
        // Test focus management
        retryButton.focus();
        expect(document.activeElement).toBe(retryButton);
      });
    });
  });
});
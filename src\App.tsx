import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useEffect, useState } from 'react';
import { BrowserRouter, Route, Routes, useLocation } from 'react-router-dom';

import ErrorBoundary from '@/components/ErrorBoundary';
import ScreenReaderAnnouncer from '@/components/ScreenReaderAnnouncer';
import { Toaster } from '@/components/ui/sonner';
import { TooltipProvider } from '@/components/ui/tooltip';
import { DeviceProvider } from '@/contexts/DeviceContext';
import { LanguageProvider } from '@/contexts/LanguageContext';
import { logWarning } from '@/lib/dev-console';
import { RoutePreloader } from '@/routes/route-loader';
import { getAllRoutes, ROUTE_PATHS, RouteConfig } from '@/routes/routeConfig';

// Create a react-query client
const queryClient = new QueryClient();

// Component to handle route changes for accessibility
const RouteChangeAnnouncer = () => {
  const location = useLocation();
  const [message, setMessage] = useState('');

  useEffect(() => {
    // When the route changes, announce it to screen readers
    const path = location?.pathname;
    const routeName = path === '/' ? 'Home' : path?.split('/').filter(Boolean).join(' ');
    setMessage(`Navigated to ${routeName} page`);
  }, [location]);

  return <ScreenReaderAnnouncer message={message} />;
};

// Wrapper for components that need to access the router context
const AppContent = () => {
  const routes = getAllRoutes();

  return (<DeviceProvider>
    <LanguageProvider>
      {/* Accessibility enhancements */}
      <div id="skip-link-target" tabIndex={-1} />
      <RouteChangeAnnouncer />

      {/* Preload critical routes */}
      <RoutePreloader routes={[
        ROUTE_PATHS.HOME,
        ROUTE_PATHS.APPOINTMENTS,
        ROUTE_PATHS.CONTACT,
        ROUTE_PATHS.EXPERTISE
      ]} />

      <Toaster />
      <Routes>
        {routes?.map((route: RouteConfig, index: number) => {
          // Ensure route has required properties
          if (!route?.path) {
            logWarning(`Route at index ${index} is missing path property`);
            return null;
          }

          return (
            <Route
              key={`${route.path}-${index}`}
              path={route.path}
              element={route.element}
            />
          );
        })}
      </Routes>
    </LanguageProvider>
  </DeviceProvider>
  );
};

const App = () => {
  return (
    <ErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <TooltipProvider>
          <BrowserRouter>
            <AppContent />
          </BrowserRouter>
        </TooltipProvider>
      </QueryClientProvider>
    </ErrorBoundary>
  );
};

export default App;

import React from 'react';
import { ChevronDown, HelpCircle, Clock, Users } from 'lucide-react';

import SafeImage from '@/components/SafeImage';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Badge } from '@/components/ui/badge';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { FAQCategory as FAQCategoryType, FAQItem } from '@/data/faq/faqData';
import { cn } from '@/lib/utils';

interface FAQCategoryProps {
  category: FAQCategoryType;
  categoryIndex: number;
  isHighlighted?: boolean;
}

const FAQCategory: React.FC<FAQCategoryProps> = ({
  category,
  categoryIndex,
  isHighlighted = false
}) => {
  const deviceInfo = useDeviceDetection();

  // Category icons mapping
  const getCategoryIcon = (title: string) => {
    if (title.includes('General')) return <HelpCircle className="h-5 w-5" />;
    if (title.includes('Cervical') || title.includes('Lumbar')) return <Users className="h-5 w-5" />;
    if (title.includes('Advanced') || title.includes('Robotic')) return <Clock className="h-5 w-5" />;
    if (title.includes('Pain') || title.includes('Recovery')) return <Clock className="h-5 w-5" />;
    if (title.includes('Insurance') || title.includes('Costs')) return <Users className="h-5 w-5" />;
    return <HelpCircle className="h-5 w-5" />;
  };

  return (
    <section
      id={`category-${categoryIndex}`}
      aria-labelledby={`category-${categoryIndex}-heading`}
      className={cn(
        "scroll-mt-24 transition-all duration-300 animate-fade-in-up",
        isHighlighted && "ring-2 ring-primary ring-opacity-50 rounded-xl"
      )}
      style={{ animationDelay: `${categoryIndex * 0.1}s` }}
    >
      {/* Premium Category Header */}
      <div className={cn(
        "bg-gradient-to-br from-white via-blue-50/20 to-indigo-50/10 dark:from-slate-800 dark:via-slate-800/80 dark:to-slate-900",
        "rounded-2xl border border-slate-200/60 dark:border-slate-700/50",
        "shadow-xl shadow-slate-200/40 dark:shadow-slate-900/40 backdrop-blur-sm",
        "relative overflow-hidden",
        deviceInfo.isMobile ? "p-5 mb-5" : "p-8 mb-8"
      )}>
        {/* Decorative background elements */}
        <div className="absolute inset-0 bg-gradient-to-br from-primary/3 via-transparent to-blue-500/3 pointer-events-none" />
        <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-bl from-primary/8 to-transparent rounded-full blur-2xl pointer-events-none" />

        <div className="flex items-start gap-5 relative z-10">
          <div className="flex-shrink-0 p-3 bg-gradient-to-br from-primary via-blue-600 to-indigo-600 rounded-xl shadow-lg shadow-primary/25">
            {getCategoryIcon(category.title)}
          </div>
          <div className="flex-1">
            <div className="flex items-center gap-4 mb-3">
              <h3
                id={`category-${categoryIndex}-heading`}
                className={cn(
                  "font-bold bg-gradient-to-r from-slate-900 via-slate-700 to-slate-900 dark:from-slate-100 dark:via-slate-200 dark:to-slate-100 bg-clip-text text-transparent",
                  deviceInfo.isMobile ? "text-xl" : "text-3xl"
                )}
              >
                {category.title}
              </h3>
              <Badge
                variant="secondary"
                className="bg-gradient-to-r from-primary/10 to-blue-500/10 text-primary border-primary/20 font-semibold px-3 py-1 shadow-sm"
              >
                {category.questions.length} questions
              </Badge>
            </div>

            {category.description && (
              <p className="text-slate-600 dark:text-slate-400 leading-relaxed font-medium">
                {category.description}
              </p>
            )}
          </div>
        </div>

        {/* Category-specific image */}
        {category.imageSrc && (
          <div className="relative rounded-lg overflow-hidden mt-4 shadow-md">
            <SafeImage
              src={category.imageSrc}
              alt={category.imageAlt || `${category.title} illustration`}
              className="w-full h-auto max-h-[250px] object-cover"
              fallbackSrc="/images/medical-consulting.jpg"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />
          </div>
        )}
      </div>

      {/* Premium FAQ Accordion */}
      <Accordion
        type="single"
        collapsible
        className="space-y-4"
        aria-label={`${category.title} frequently asked questions`}
      >
        {category.questions.map((faq: FAQItem, faqIndex: number) => (
          <AccordionItem
            key={faqIndex}
            value={`item-${categoryIndex}-${faqIndex}`}
            className={cn(
              "group border-0 relative overflow-hidden",
              "bg-gradient-to-br from-white via-slate-50/30 to-white dark:from-slate-800 dark:via-slate-800/50 dark:to-slate-900",
              "rounded-2xl shadow-lg shadow-slate-200/40 dark:shadow-slate-900/40",
              "border border-slate-200/60 dark:border-slate-700/50",
              "hover:shadow-xl hover:shadow-slate-200/60 dark:hover:shadow-slate-900/60",
              "hover:border-primary/30 hover:scale-[1.01] transform-gpu transition-all duration-300",
              "backdrop-blur-sm"
            )}
          >
            {/* Decorative background for questions */}
            <div className="absolute inset-0 bg-gradient-to-br from-primary/2 via-transparent to-blue-500/2 pointer-events-none" />

            <AccordionTrigger
              className={cn(
                "text-left font-semibold hover:no-underline relative z-10",
                "text-slate-900 dark:text-slate-100 group-hover:text-primary transition-colors duration-300",
                "[&[data-state=open]]:text-primary [&[data-state=open]]:border-b [&[data-state=open]]:border-slate-200/60 dark:[&[data-state=open]]:border-slate-700/60",
                deviceInfo.isMobile ? "px-5 py-4" : "px-7 py-5"
              )}
              aria-describedby={`answer-${categoryIndex}-${faqIndex}`}
            >
              <div className="flex items-start gap-4 w-full">
                <div className="flex-shrink-0 mt-1">
                  <div className="w-8 h-8 rounded-xl bg-gradient-to-br from-primary via-blue-600 to-indigo-600 flex items-center justify-center shadow-lg shadow-primary/25">
                    <span className="text-sm font-bold text-white">
                      {faqIndex + 1}
                    </span>
                  </div>
                </div>
                <span className="flex-1 pr-4 leading-relaxed text-base">
                  {faq.question}
                </span>
              </div>
            </AccordionTrigger>
            <AccordionContent
              id={`answer-${categoryIndex}-${faqIndex}`}
              className={cn(
                "relative z-10",
                deviceInfo.isMobile ? "px-5 pb-6 pt-2" : "px-7 pb-7 pt-3"
              )}
            >
              <div className="pl-12 space-y-4">
                <div className="bg-gradient-to-br from-slate-50/80 to-white/80 dark:from-slate-800/80 dark:to-slate-900/80 rounded-xl p-5 border border-slate-200/40 dark:border-slate-700/40 backdrop-blur-sm">
                  {faq.answer.split('\n').map((paragraph: string, i: number) => (
                    <p
                      key={i}
                      className="text-slate-700 dark:text-slate-300 leading-relaxed font-medium mb-3 last:mb-0"
                    >
                      {paragraph}
                    </p>
                  ))}
                </div>

                {/* Enhanced helpful actions */}
                <div className="flex items-center gap-3 pt-4 border-t border-slate-200/60 dark:border-slate-700/60">
                  <span className="text-sm text-slate-600 dark:text-slate-400 font-medium">
                    Was this helpful?
                  </span>
                  <button className="px-3 py-1 text-sm text-primary hover:text-primary/80 bg-primary/10 hover:bg-primary/20 rounded-lg transition-colors duration-200 font-medium">
                    👍 Yes
                  </button>
                  <button className="px-3 py-1 text-sm text-slate-600 hover:text-slate-800 dark:text-slate-400 dark:hover:text-slate-200 bg-slate-100 hover:bg-slate-200 dark:bg-slate-800 dark:hover:bg-slate-700 rounded-lg transition-colors duration-200 font-medium">
                    👎 No
                  </button>
                </div>
              </div>
            </AccordionContent>
          </AccordionItem>
        ))}
      </Accordion>
    </section>
  );
};

FAQCategory.displayName = 'FAQCategory';

export default FAQCategory;

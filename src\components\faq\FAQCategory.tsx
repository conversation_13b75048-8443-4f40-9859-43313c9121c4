import React from 'react';
import { ChevronDown, HelpCircle, Clock, Users } from 'lucide-react';

import SafeImage from '@/components/SafeImage';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Badge } from '@/components/ui/badge';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { FAQCategory as FAQCategoryType, FAQItem } from '@/data/faq/faqData';
import { cn } from '@/lib/utils';

interface FAQCategoryProps {
  category: FAQCategoryType;
  categoryIndex: number;
  isHighlighted?: boolean;
}

const FAQCategory: React.FC<FAQCategoryProps> = ({
  category,
  categoryIndex,
  isHighlighted = false
}) => {
  const deviceInfo = useDeviceDetection();

  // Category icons mapping
  const getCategoryIcon = (title: string) => {
    if (title.includes('General')) return <HelpCircle className="h-5 w-5" />;
    if (title.includes('Cervical') || title.includes('Lumbar')) return <Users className="h-5 w-5" />;
    if (title.includes('Advanced') || title.includes('Robotic')) return <Clock className="h-5 w-5" />;
    if (title.includes('Pain') || title.includes('Recovery')) return <Clock className="h-5 w-5" />;
    if (title.includes('Insurance') || title.includes('Costs')) return <Users className="h-5 w-5" />;
    return <HelpCircle className="h-5 w-5" />;
  };

  return (
    <section
      id={`category-${categoryIndex}`}
      aria-labelledby={`category-${categoryIndex}-heading`}
      className={cn(
        "scroll-mt-24 transition-all duration-300",
        isHighlighted && "ring-2 ring-primary ring-opacity-50 rounded-xl"
      )}
    >
      {/* Enhanced Category Header */}
      <div className={cn(
        "bg-gradient-to-r from-slate-50 to-white dark:from-slate-900 dark:to-slate-800 rounded-xl p-6 mb-6 border shadow-sm",
        deviceInfo.isMobile && "p-4"
      )}>
        <div className="flex items-start gap-4">
          <div className="flex-shrink-0 p-2 bg-primary/10 rounded-lg text-primary">
            {getCategoryIcon(category.title)}
          </div>
          <div className="flex-1">
            <div className="flex items-center gap-3 mb-2">
              <h3
                id={`category-${categoryIndex}-heading`}
                className={cn(
                  "font-bold text-slate-900 dark:text-slate-100",
                  deviceInfo.isMobile ? "text-xl" : "text-2xl"
                )}
              >
                {category.title}
              </h3>
              <Badge variant="secondary" className="text-xs">
                {category.questions.length} questions
              </Badge>
            </div>

            {category.description && (
              <p className="text-muted-foreground leading-relaxed">
                {category.description}
              </p>
            )}
          </div>
        </div>

        {/* Category-specific image */}
        {category.imageSrc && (
          <div className="relative rounded-lg overflow-hidden mt-4 shadow-md">
            <SafeImage
              src={category.imageSrc}
              alt={category.imageAlt || `${category.title} illustration`}
              className="w-full h-auto max-h-[250px] object-cover"
              fallbackSrc="/images/medical-consulting.jpg"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />
          </div>
        )}
      </div>

      {/* Enhanced FAQ Accordion */}
      <Accordion
        type="single"
        collapsible
        className="space-y-3"
        aria-label={`${category.title} frequently asked questions`}
      >
        {category.questions.map((faq: FAQItem, faqIndex: number) => (
          <AccordionItem
            key={faqIndex}
            value={`item-${categoryIndex}-${faqIndex}`}
            className={cn(
              "group border-0 bg-white dark:bg-slate-800 rounded-xl shadow-sm hover:shadow-lg transition-all duration-300",
              "border border-slate-200 dark:border-slate-700 hover:border-primary/30 hover:scale-[1.01] transform-gpu"
            )}
          >
            <AccordionTrigger
              className={cn(
                "text-left font-medium px-6 py-4 hover:no-underline",
                "text-slate-900 dark:text-slate-100 group-hover:text-primary transition-colors duration-200",
                "[&[data-state=open]]:text-primary [&[data-state=open]]:border-b [&[data-state=open]]:border-slate-200 dark:[&[data-state=open]]:border-slate-700"
              )}
              aria-describedby={`answer-${categoryIndex}-${faqIndex}`}
            >
              <div className="flex items-start gap-3 w-full">
                <div className="flex-shrink-0 mt-1">
                  <div className="w-6 h-6 rounded-full bg-primary/10 flex items-center justify-center">
                    <span className="text-xs font-bold text-primary">
                      {faqIndex + 1}
                    </span>
                  </div>
                </div>
                <span className="flex-1 pr-4 leading-relaxed">
                  {faq.question}
                </span>
              </div>
            </AccordionTrigger>
            <AccordionContent
              id={`answer-${categoryIndex}-${faqIndex}`}
              className="px-6 pb-6 pt-2"
            >
              <div className="pl-9 space-y-3">
                {faq.answer.split('\n').map((paragraph: string, i: number) => (
                  <p
                    key={i}
                    className="text-muted-foreground leading-relaxed text-sm"
                  >
                    {paragraph}
                  </p>
                ))}

                {/* Optional: Add helpful actions */}
                <div className="flex items-center gap-2 pt-3 border-t border-slate-100 dark:border-slate-700">
                  <span className="text-xs text-muted-foreground">
                    Was this helpful?
                  </span>
                  <button className="text-xs text-primary hover:underline">
                    Yes
                  </button>
                  <span className="text-xs text-muted-foreground">•</span>
                  <button className="text-xs text-primary hover:underline">
                    No
                  </button>
                </div>
              </div>
            </AccordionContent>
          </AccordionItem>
        ))}
      </Accordion>
    </section>
  );
};

FAQCategory.displayName = 'FAQCategory';

export default FAQCategory;

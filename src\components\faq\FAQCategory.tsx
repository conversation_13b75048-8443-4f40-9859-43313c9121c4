import React from 'react';

import SafeImage from '@/components/SafeImage';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { FAQCategory as FAQCategoryType, FAQItem } from '@/data/faq/faqData';
import { cn } from '@/lib/utils';

interface FAQCategoryProps {
  category: FAQCategoryType;
  categoryIndex: number;
}

const FAQCategory: React.FC<FAQCategoryProps> = ({ category, categoryIndex }) => {
  const deviceInfo = useDeviceDetection();

  return (
    <section
      id={`category-${categoryIndex}`}
      aria-labelledby={`category-${categoryIndex}-heading`}
    >
      <h3
        id={`category-${categoryIndex}-heading`}
        className={cn(
          "font-bold mb-mobile-sm",
          deviceInfo.isMobile
            ? "mobile-2xl"
            : "text-2xl mb-3"
        )}
      >
        {category.title}
      </h3>
      
      {category.description && (
        <p className="text-muted-foreground mb-6">{category.description}</p>
      )}

      {/* Category-specific image */}
      {category.imageSrc && (
        <div className="relative rounded-lg overflow-hidden mb-6 shadow-md max-w-md mx-auto">
          <SafeImage
            src={category.imageSrc}
            alt={category.imageAlt || `${category.title} illustration`}
            className="w-full h-auto max-h-[200px] object-cover"
            fallbackSrc="/images/medical-consulting.jpg"
          />
        </div>
      )}

      <Accordion
        type="single"
        collapsible
        className="space-y-4"
        aria-label={`${category.title} frequently asked questions`}
      >
        {category.questions.map((faq: FAQItem, faqIndex: number) => (
          <AccordionItem
            key={faqIndex}
            value={`item-${categoryIndex}-${faqIndex}`}
            className="border rounded-lg p-2"
          >
            <AccordionTrigger
              className="text-left font-medium px-4"
              aria-describedby={`answer-${categoryIndex}-${faqIndex}`}
            >
              {faq.question}
            </AccordionTrigger>
            <AccordionContent
              id={`answer-${categoryIndex}-${faqIndex}`}
              className="px-4 pt-2 pb-4 text-muted-foreground"
            >
              {faq.answer.split('\n').map((paragraph: string, i: number) => (
                <p key={i} className={i > 0 ? 'mt-2' : ''}>{paragraph}</p>
              ))}
            </AccordionContent>
          </AccordionItem>
        ))}
      </Accordion>
    </section>
  );
};

FAQCategory.displayName = 'FAQCategory';

export default FAQCategory;

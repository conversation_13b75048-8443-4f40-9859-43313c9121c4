import React from 'react';
import { Link } from 'react-router-dom';

import { But<PERSON> } from '@/components/ui/button';

interface FAQCallToActionProps {
  title: string;
  description: string;
  primaryButtonText: string;
  primaryButtonLink: string;
  secondaryButtonText: string;
  secondaryButtonLink: string;
}

const FAQCallToAction: React.FC<FAQCallToActionProps> = ({
  title,
  description,
  primaryButtonText,
  primaryButtonLink,
  secondaryButtonText,
  secondaryButtonLink
}) => {
  return (
    <section className="py-16 bg-primary/5">
      <div className="container">
        <div className="text-center max-w-3xl mx-auto">
          <h2 className="text-3xl font-bold mb-6">{title}</h2>
          <p className="text-muted-foreground mb-8">
            {description}
          </p>
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <Button asChild size="lg">
              <Link to={primaryButtonLink}>{primaryButtonText}</Link>
            </Button>
            <Button asChild variant="outline" size="lg">
              <Link to={secondaryButtonLink}>{secondaryButtonText}</Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
};

FAQCallToAction.displayName = 'FAQCallToAction';

export default FAQCallToAction;

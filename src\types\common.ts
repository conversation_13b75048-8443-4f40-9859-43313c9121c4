
// Common type definitions
export interface ApiResponse<T> {
  readonly success: boolean;
  readonly data: T;
  readonly message?: string;
  readonly timestamp: string;
}

export interface LoadingState {
  readonly loading: boolean;
  readonly error: Error | null;
}

export interface PaginationParams {
  readonly page: number;
  readonly limit: number;
  readonly sortBy?: string;
  readonly sortOrder?: 'asc' | 'desc';
}

export interface FormFieldProps {
  readonly name: string;
  readonly label: string;
  readonly required?: boolean;
  readonly disabled?: boolean;
  readonly error?: string;
}

export type EventHandler<T = HTMLElement> = (event: React.MouseEvent<T>) => void;
export type ChangeHandler<T = HTMLInputElement> = (event: React.ChangeEvent<T>) => void;
export type SubmitHandler = (event: React.FormEvent<HTMLFormElement>) => void;

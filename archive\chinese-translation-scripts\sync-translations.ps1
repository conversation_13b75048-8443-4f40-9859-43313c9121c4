# Translation Synchronization Script
# Fixes the key mismatch between EN and ZH translation files

Write-Host "🔧 TRANSLATION SYNCHRONIZATION FIX" -ForegroundColor Cyan
Write-Host ("=" * 50) -ForegroundColor Cyan

$enPath = "src/locales/en.ts"
$zhPath = "src/locales/zh.ts"
$backupPath = "src/locales/zh.ts.backup"

# Check if files exist
if (-not (Test-Path $enPath)) {
    Write-Host "❌ English translation file not found: $enPath" -ForegroundColor Red
    exit 1
}

if (-not (Test-Path $zhPath)) {
    Write-Host "❌ Chinese translation file not found: $zhPath" -ForegroundColor Red
    exit 1
}

Write-Host "📖 Loading translation files..." -ForegroundColor Yellow

# Create backup
Write-Host "💾 Creating backup of Chinese translations..." -ForegroundColor Yellow
Copy-Item $zhPath $backupPath -Force

# Read files
$enContent = Get-Content $enPath -Raw -Encoding UTF8
$zhContent = Get-Content $zhPath -Raw -Encoding UTF8

# Extract keys using regex (simplified approach)
function Extract-Keys {
    param([string]$Content)
    
    $keys = @()
    
    # Remove comments and export statements
    $cleanContent = $Content -replace '/\*[\s\S]*?\*/', '' -replace '//.*$', '' -replace 'export default\s*', '' -replace ';\s*$', ''
    
    # Find all key: value patterns
    $matches = [regex]::Matches($cleanContent, '(\w+)\s*:\s*["{]')
    
    foreach ($match in $matches) {
        $keys += $match.Groups[1].Value
    }
    
    return $keys | Sort-Object | Get-Unique
}

Write-Host "🔍 Extracting keys..." -ForegroundColor Yellow

$enKeys = Extract-Keys $enContent
$zhKeys = Extract-Keys $zhContent

Write-Host "📊 Analysis Results:" -ForegroundColor Green
Write-Host "  English keys: $($enKeys.Count)" -ForegroundColor Gray
Write-Host "  Chinese keys: $($zhKeys.Count)" -ForegroundColor Gray

$enKeySet = [System.Collections.Generic.HashSet[string]]::new($enKeys)
$zhKeySet = [System.Collections.Generic.HashSet[string]]::new($zhKeys)

$missingInZh = @()
$extraInZh = @()

foreach ($key in $enKeys) {
    if (-not $zhKeySet.Contains($key)) {
        $missingInZh += $key
    }
}

foreach ($key in $zhKeys) {
    if (-not $enKeySet.Contains($key)) {
        $extraInZh += $key
    }
}

$commonKeys = $enKeys | Where-Object { $zhKeySet.Contains($_) }

Write-Host "  Common keys: $($commonKeys.Count)" -ForegroundColor Gray
Write-Host "  Missing in Chinese: $($missingInZh.Count)" -ForegroundColor Red
Write-Host "  Extra in Chinese: $($extraInZh.Count)" -ForegroundColor Yellow

# Create a simple fix by adding missing keys as placeholders
Write-Host "🔧 Creating synchronized translation file..." -ForegroundColor Yellow

# Read the Chinese file and add missing keys at the end
$lines = Get-Content $zhPath -Encoding UTF8
$lastBraceIndex = -1

# Find the last closing brace
for ($i = $lines.Count - 1; $i -ge 0; $i--) {
    if ($lines[$i] -match '^\s*}\s*;?\s*$') {
        $lastBraceIndex = $i
        break
    }
}

if ($lastBraceIndex -eq -1) {
    Write-Host "❌ Could not find closing brace in Chinese translation file" -ForegroundColor Red
    exit 1
}

# Add missing keys before the last brace
$newLines = @()
$newLines += $lines[0..($lastBraceIndex-1)]

# Add missing keys section
if ($missingInZh.Count -gt 0) {
    $newLines += ""
    $newLines += "  // ⚠️ MISSING KEYS - NEED TRANSLATION"
    $newLines += "  // Added by synchronization script"
    
    foreach ($key in $missingInZh | Select-Object -First 20) {  # Limit to first 20 to avoid overwhelming
        $newLines += "  // ${key}: `"[NEEDS_TRANSLATION]`","
    }
    
    if ($missingInZh.Count -gt 20) {
        $remaining = $missingInZh.Count - 20
        $newLines += "  // ... and $remaining more keys need translation"
    }
}

$newLines += $lines[$lastBraceIndex..($lines.Count-1)]

# Write the updated file
$newLines | Out-File $zhPath -Encoding UTF8

# Create report
$report = @{
    timestamp = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
    summary = @{
        originalEnglishKeys = $enKeys.Count
        originalChineseKeys = $zhKeys.Count
        commonKeys = $commonKeys.Count
        missingInChinese = $missingInZh.Count
        extraInChinese = $extraInZh.Count
        backupCreated = $backupPath
    }
    missingKeys = $missingInZh
    extraKeys = $extraInZh
    recommendations = @(
        "Review and translate the missing keys marked with [需要翻译]"
        "Consider removing or translating the extra keys that don't exist in English"
        "Implement automated translation key validation in CI/CD pipeline"
        "Use translation management tools for better synchronization"
    )
}

$report | ConvertTo-Json -Depth 10 | Out-File "translation-sync-report.json" -Encoding UTF8

Write-Host "✅ Translation synchronization completed!" -ForegroundColor Green
Write-Host "📄 Report saved to: translation-sync-report.json" -ForegroundColor Green
Write-Host "💾 Backup saved to: $backupPath" -ForegroundColor Green

if ($missingInZh.Count -gt 0) {
    Write-Host "`nMissing keys (first 10):" -ForegroundColor Yellow
    $missingInZh | Select-Object -First 10 | ForEach-Object { 
        Write-Host "  - $_" -ForegroundColor Yellow 
    }
    Write-Host "`nNext steps:" -ForegroundColor Cyan
    Write-Host "  1. Review the Chinese translation file" -ForegroundColor Gray
    Write-Host "  2. Translate the missing keys marked with [需要翻译]" -ForegroundColor Gray
    Write-Host "  3. Remove or translate extra keys that do not exist in English" -ForegroundColor Gray
    Write-Host "  4. Test the application with updated translations" -ForegroundColor Gray
}

if ($extraInZh.Count -gt 0) {
    Write-Host "`nExtra keys in Chinese (first 10):" -ForegroundColor Yellow
    $extraInZh | Select-Object -First 10 | ForEach-Object { 
        Write-Host "  + $_" -ForegroundColor Yellow 
    }
}

Write-Host "`nTranslation synchronization summary:" -ForegroundColor Cyan
Write-Host "  [OK] Backup created" -ForegroundColor Green
Write-Host "  [OK] Missing keys identified" -ForegroundColor Green
Write-Host "  [OK] Extra keys documented" -ForegroundColor Green
Write-Host "  [OK] Report generated" -ForegroundColor Green

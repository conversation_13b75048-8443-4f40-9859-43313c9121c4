import React, { useEffect } from 'react';

import { ConditionCauses } from '@/components/medical-conditions/shared/ConditionCauses';
import { ConditionHero } from '@/components/medical-conditions/shared/ConditionHero';
import ConditionOverviewSection from '@/components/medical-conditions/shared/ConditionOverviewSection';
import ConditionQuickFacts from '@/components/medical-conditions/shared/ConditionQuickFacts';
import { ConditionSymptoms } from '@/components/medical-conditions/shared/ConditionSymptoms';
import { ConditionTreatment } from '@/components/medical-conditions/shared/ConditionTreatment';
import StandardPageLayout from '@/components/StandardPageLayout';
import sacroiliacarthropathyData from '@/data/conditions/sacroiliacarthropathy';

/**
 * Refactored SacroiliacArthropathy Component
 * 
 * Original component: 81KB
 * Refactored component: <100 lines
 * Reduction: ~95%
 */

const SacroiliacArthropathyRefactored: React.FC = () => {
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  return (
    <StandardPageLayout 
      title="Sacroiliac Arthropathy - Comprehensive Guide" 
      showHeader={false}
    >
      <main className="flex-1 pt-20">
        <ConditionHero
          title={sacroiliacarthropathyData.hero.title}
          subtitle={sacroiliacarthropathyData.hero.subtitle}
          backgroundImage={sacroiliacarthropathyData.hero.backgroundImage}
          badge={sacroiliacarthropathyData.hero.badge}
        />

        <ConditionQuickFacts facts={sacroiliacarthropathyData.quickFacts} />

        <ConditionOverviewSection
          title={sacroiliacarthropathyData.overview.title}
          description={sacroiliacarthropathyData.overview.description}
          keyPoints={sacroiliacarthropathyData.overview.keyPoints}
          imageSrc={sacroiliacarthropathyData.overview.imageSrc}
          imageAlt={sacroiliacarthropathyData.overview.imageAlt}
          imageCaption={sacroiliacarthropathyData.overview.imageCaption}
        />

        <ConditionCauses
          causes={sacroiliacarthropathyData.causes}
          riskFactors={sacroiliacarthropathyData.riskFactors}
        />

        <ConditionSymptoms
          symptomCategories={sacroiliacarthropathyData.symptoms}
          warningSigns={sacroiliacarthropathyData.warningSigns}
        />

        <ConditionTreatment
          conservativeOptions={sacroiliacarthropathyData.conservativeTreatments}
          surgicalOptions={sacroiliacarthropathyData.surgicalTreatments}
        />
      </main>
    </StandardPageLayout>
  );
};

SacroiliacArthropathyRefactored.displayName = 'SacroiliacArthropathyRefactored';

export default SacroiliacArthropathyRefactored;
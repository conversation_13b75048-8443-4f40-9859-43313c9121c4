#!/usr/bin/env node

/**
 * Mismatched HTML Tags Fix Script
 * 
 * Finds and fixes mismatched HTML tags that cause build failures
 * in React/JSX files. Specifically targets patterns like:
 * - <main> opening tags with </div> closing tags
 * - <section> opening tags with </div> closing tags
 * And other tag mismatches.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🔧 MISMATCHED HTML TAGS FIX');
console.log('='.repeat(50));

/**
 * Scan directory for React/JSX files
 */
function scanForReactFiles(dir, files = []) {
  const items = fs.readdirSync(dir);
  
  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory()) {
      if (!item.startsWith('.') && item !== 'node_modules' && item !== 'dist') {
        scanForReactFiles(fullPath, files);
      }
    } else if (item.match(/\.(tsx|jsx)$/)) {
      files.push(fullPath);
    }
  }
  
  return files;
}

/**
 * Check if file contains mismatched HTML tags
 */
function hasMismatchedTags(content) {
  // Look for specific problematic patterns
  const problematicPatterns = [
    /<main\s+className=.*?>\s*[\s\S]*?<\/div>/,  // <main> with </div>
    /<section\s+className=.*?>\s*[\s\S]*?<\/div>/, // <section> with </div>
    /<div\s+className=.*?>\s*[\s\S]*?<\/main>/, // <div> with </main>
    /<div\s+className=.*?>\s*[\s\S]*?<\/section>/, // <div> with </section>
  ];
  
  return problematicPatterns.some(pattern => pattern.test(content));
}

/**
 * Fix mismatched HTML tags in content
 */
function fixMismatchedTags(content, filePath) {
  let fixed = content;
  let changes = 0;
  const appliedFixes = [];

  // Fix specific known patterns
  const specificFixes = [
    {
      // Fix: <main className=...> should be <div className=...>
      from: /<main(\s+className=\{cn\([^}]+\)\})/g,
      to: '<div$1',
      desc: 'Fixed <main> tags to <div> tags'
    },
    {
      // Fix: <section className=...> inside other sections should be <div>
      from: /<section(\s+className=\{cn\([^}]+\)\})>\s*(?=[\s\S]*?<\/div>)/g,
      to: '<div$1>',
      desc: 'Fixed nested <section> tags to <div> tags'
    },
    {
      // Fix any remaining main tags that should be div
      from: /<main\s+className=/g,
      to: '<div className=',
      desc: 'Fixed remaining <main> tags to <div> tags'
    }
  ];

  for (const fix of specificFixes) {
    const beforeContent = fixed;
    fixed = fixed.replace(fix.from, fix.to);
    
    if (fixed !== beforeContent) {
      changes++;
      appliedFixes.push(fix.desc);
    }
  }

  return { content: fixed, changes, appliedFixes };
}

/**
 * Process a single file
 */
function processFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const relativePath = path.relative(process.cwd(), filePath);
    
    // Check for mismatched tags using simple pattern matching
    const hasMismatches = content.includes('<main className=') && 
                         !content.includes('</main>') ||
                         content.match(/<main\s+className=.*?>\s*[\s\S]*?<\/div>/);
    
    if (!hasMismatches) {
      return { processed: false, reason: 'No mismatched tags found' };
    }

    console.log(`🔧 Processing: ${relativePath}`);
    
    const result = fixMismatchedTags(content, filePath);
    
    if (result.changes > 0) {
      // Create backup
      const backupPath = filePath + '.backup';
      fs.copyFileSync(filePath, backupPath);
      
      // Write fixed content
      fs.writeFileSync(filePath, result.content, 'utf8');
      
      console.log(`  ✅ Fixed ${result.changes} issues`);
      result.appliedFixes.forEach(fix => {
        console.log(`    - ${fix}`);
      });
      
      return {
        processed: true,
        changes: result.changes,
        appliedFixes: result.appliedFixes,
        backupPath
      };
    }
    
    return { processed: false, reason: 'No changes needed' };
    
  } catch (error) {
    console.log(`  ❌ Error: ${error.message}`);
    return { processed: false, reason: error.message };
  }
}

/**
 * Main execution function
 */
function main() {
  console.log('📋 Scanning for React/JSX files with mismatched HTML tags...');
  
  const reactFiles = scanForReactFiles('src');
  console.log(`Found ${reactFiles.length} React/JSX files`);

  const results = {
    total: reactFiles.length,
    processed: 0,
    skipped: 0,
    errors: 0,
    totalChanges: 0,
    processedFiles: []
  };

  console.log('\n🔧 Processing files...');

  for (const filePath of reactFiles) {
    const result = processFile(filePath);
    
    if (result.processed) {
      results.processed++;
      results.totalChanges += result.changes;
      results.processedFiles.push({
        file: path.relative(process.cwd(), filePath),
        changes: result.changes,
        fixes: result.appliedFixes,
        backup: result.backupPath
      });
    } else {
      if (result.reason.includes('Error')) {
        results.errors++;
      } else {
        results.skipped++;
      }
    }
  }

  // Generate summary
  console.log('\n📊 MISMATCHED HTML TAGS FIX SUMMARY');
  console.log('='.repeat(40));
  console.log(`Total files scanned: ${results.total}`);
  console.log(`Files processed: ${results.processed}`);
  console.log(`Files skipped: ${results.skipped}`);
  console.log(`Errors: ${results.errors}`);
  console.log(`Total changes made: ${results.totalChanges}`);

  if (results.processed > 0) {
    console.log('\n✅ SUCCESSFULLY PROCESSED FILES:');
    results.processedFiles.forEach(file => {
      console.log(`  • ${file.file} (${file.changes} changes)`);
      file.fixes.forEach(fix => console.log(`    - ${fix}`));
    });
  }

  // Save detailed report
  const report = {
    timestamp: new Date().toISOString(),
    summary: results,
    patternsFixed: [
      'Mismatched <main> and </div> tags',
      'Mismatched <section> and </div> tags',
      'Incorrect HTML tag nesting',
      'Container element tag inconsistencies'
    ],
    recommendations: [
      'Test all fixed components to ensure they render correctly',
      'Review HTML structure for semantic correctness',
      'Consider using ESLint rules to prevent tag mismatches',
      'Update development workflow to catch HTML structure errors early'
    ]
  };

  fs.writeFileSync('mismatched-html-tags-fix-report.json', JSON.stringify(report, null, 2));
  console.log('\n📄 Detailed report saved to: mismatched-html-tags-fix-report.json');

  return results;
}

// Run if called directly
if (process.argv[1] && process.argv[1].endsWith('fix-mismatched-html-tags.js')) {
  main();
}

export { main as fixMismatchedHtmlTags };

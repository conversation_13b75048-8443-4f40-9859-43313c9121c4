import React from 'react';

import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';

interface AccordionItemData {
  id: string;
  title: string;
  content: React.ReactNode;
}

interface AccordionSectionProps {
  title?: string;
  subtitle?: string;
  items: AccordionItemData[];
  type?: 'single' | 'multiple';
  className?: string;
}

const AccordionSection: React.FC<AccordionSectionProps> = ({
  title,
  subtitle,
  items,
  type = 'single',
  className = ''
}) => {
  return (
    <div className={className}>
      {(title || subtitle) && (
        <div className="text-center mb-8">
          {title && <h2 className="text-3xl font-bold mb-4">{title}</h2>}
          {subtitle && <p className="text-muted-foreground text-lg">{subtitle}</p>}
        </div>
      )}
      
      <Accordion type={type} className="w-full">
        {items.map((item) => (
          <AccordionItem key={item.id} value={item.id}>
            <AccordionTrigger className="text-left">
              {item.title}
            </AccordionTrigger>
            <AccordionContent>
              {item.content}
            </AccordionContent>
          </AccordionItem>
        ))}
      </Accordion>
    </div>
  );
};

export default AccordionSection;

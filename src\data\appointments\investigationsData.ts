export interface Investigation {
  id: string;
  title: string;
  description: string;
}

export const investigationTypes: Investigation[] = [
  {
    id: 'mri-scans',
    title: 'MRI Scans',
    description: 'MRI scans for detailed soft tissue imaging'
  },
  {
    id: 'ct-scans',
    title: 'CT Scans',
    description: 'CT scans for bone and structural assessment'
  },
  {
    id: 'x-rays',
    title: 'X-rays',
    description: 'X-rays for initial evaluation'
  },
  {
    id: 'specialized-tests',
    title: 'Specialized Tests',
    description: 'Specialized tests as clinically indicated'
  }
];

export const investigationsInfo = {
  title: 'Diagnostic Investigations',
  descriptions: [
    'Accurate diagnosis is essential for effective treatment planning.',
    'Dr<PERSON> <PERSON> may recommend various investigations based on your condition:',
    'All investigations are carefully selected to provide the most relevant information for your care.'
  ],
  imageSrc: '/images/neurosurgical-consultation-suite-Surrey-Hills-miNEURO-doctor-desk.jpg',
  imageAlt: 'Neurosurgical consultation and diagnostic imaging'
};

export const getInvestigationById = (id: string): Investigation | undefined => {
  return investigationTypes.find(investigation => investigation.id === id);
};

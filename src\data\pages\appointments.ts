import { Calendar, Clock, FileText, Users } from 'lucide-react';

export const appointmentsData = {
  hero: {
    title: "Book an Appointment",
    subtitle: "Schedule your consultation with Dr. <PERSON><PERSON> for comprehensive assessment and treatment planning.",
    backgroundImage: "/images/neurosurgical-consultation-suite-Surrey-Hills-miNEURO-doctor-desk.jpg"
  },

  process: {
    title: "Appointment Process",
    subtitle: "Our comprehensive approach ensures the best possible outcomes for every patient.",
    steps: [
      {
        number: 1,
        icon: Users,
        title: "Initial Consultation",
        description: "Comprehensive assessment of your condition and medical history."
      },
      {
        number: 2,
        icon: FileText,
        title: "Investigations",
        description: "Advanced imaging and diagnostic tests as required."
      },
      {
        number: 3,
        icon: Clock,
        title: "Discussion of Options",
        description: "Detailed explanation of treatment options and recommendations."
      },
      {
        number: 4,
        icon: Calendar,
        title: "Treatment Decisions",
        description: "Collaborative decision-making for your optimal care plan."
      }
    ]
  },

  appointmentInfo: {
    title: "Appointment Information",
    sections: [
      {
        title: "Referral Requirements",
        content: [
          "A referral from your GP or specialist is required for all consultations.",
          "Please ensure your referral is current and includes relevant medical history."
        ]
      },
      {
        title: "What to Bring",
        items: [
          "Current referral letter",
          "Medicare card and private health insurance details",
          "List of current medications",
          "Previous imaging studies (MRI, CT scans, X-rays)",
          "Previous medical reports"
        ],
        content: ["Having these items ready will help ensure an efficient consultation."]
      },
      {
        title: "Consultation Process",
        content: [
          "Dr. Aliashkevich will review your medical history, examine you, and discuss your symptoms.",
          "Treatment options and next steps will be explained in detail."
        ]
      },
      {
        title: "Telehealth Consultations",
        content: [
          "Telehealth consultations are available for follow-up appointments and some initial consultations.",
          "Please discuss telehealth options when booking your appointment."
        ]
      }
    ]
  },

  requestForm: {
    title: "Request an Appointment",
    fields: [
      { name: "firstName", label: "First Name", type: "text", placeholder: "Enter your first name" },
      { name: "lastName", label: "Last Name", type: "text", placeholder: "Enter your last name" },
      { name: "email", label: "Email Address", type: "email", placeholder: "Enter your email address" },
      { name: "phone", label: "Phone Number", type: "tel", placeholder: "Enter your phone number" },
      { 
        name: "location", 
        label: "Preferred Location", 
        type: "select",
        placeholder: "Select a location",
        options: [
          { value: "surrey-hills", label: "Surrey Hills" },
          { value: "mornington", label: "Mornington" },
          { value: "frankston", label: "Frankston" },
          { value: "moonee-ponds", label: "Moonee Ponds" },
          { value: "sunbury", label: "Sunbury" },
          { value: "werribee", label: "Werribee" },
          { value: "bundoora", label: "Bundoora" },
          { value: "dandenong", label: "Dandenong" },
          { value: "heidelberg", label: "Heidelberg" },
          { value: "wantirna", label: "Wantirna" }
        ]
      },
      { 
        name: "reason", 
        label: "Reason for Consultation", 
        type: "textarea", 
        placeholder: "Please describe your symptoms or condition" 
      },
      {
        name: "referral",
        label: "Do you have a referral?",
        type: "select",
        placeholder: "Select an option",
        options: [
          { value: "yes", label: "Yes, I have a referral" },
          { value: "no", label: "No, I need referral information" }
        ]
      }
    ],
    submitText: "Submit Request",
    confirmationText: "We will contact you within 24 hours to confirm your appointment."
  },

  investigations: {
    title: "Diagnostic Investigations",
    description: [
      "Accurate diagnosis is essential for effective treatment planning.",
      "Dr. Aliashkevich may recommend various investigations based on your condition:",
      "All investigations are carefully selected to provide the most relevant information for your care."
    ],
    items: [
      "MRI scans for detailed soft tissue imaging",
      "CT scans for bone and structural assessment",
      "X-rays for initial evaluation",
      "Specialized tests as clinically indicated"
    ],
    image: {
      src: "/images/diagnostic-imaging-mri-ct-scan-neurosurgery.jpg",
      alt: "Advanced diagnostic imaging equipment"
    }
  },

  treatmentOptions: {
    title: "Treatment Options",
    description: [
      "Treatment recommendations are tailored to each individual patient.",
      "Options may include conservative management, minimally invasive procedures, or surgical intervention.",
      "Dr. Aliashkevich will explain all available options, including risks and benefits.",
      "The final treatment decision is always made collaboratively with the patient."
    ],
    image: {
      src: "/images/treatment-discussion-neurosurgery-report-spine-brain-insurance-claim-medicolegal.jpg",
      alt: "Treatment planning consultation with Dr. Aliashkevich"
    }
  },

  fees: {
    title: "Fees and Insurance",
    subtitle: "Transparent pricing for all consultations and procedures.",
    consultations: [
      {
        title: "Initial Consultation",
        description: "Comprehensive assessment and treatment planning.",
        fee: "$450",
        rebate: "$150",
        outOfPocket: "$300"
      },
      {
        title: "Review Consultation",
        description: "Follow-up consultation for ongoing care.",
        fee: "$250",
        rebate: "$75",
        outOfPocket: "$175"
      }
    ],
    insuranceOptions: [
      {
        title: "Private Health Insurance",
        description: "Most private health insurers provide coverage for neurosurgical consultations."
      },
      {
        title: "TAC",
        description: "Transport Accident Commission claims are accepted."
      },
      {
        title: "Veteran Affairs",
        description: "DVA gold and white card holders welcome."
      },
      {
        title: "WorkCover",
        description: "WorkCover claims are processed efficiently."
      }
    ]
  },

  privacy: {
    title: "Patient Privacy",
    description: [
      "Your privacy and confidentiality are of utmost importance to us.",
      "All patient information is handled in accordance with Australian Privacy Principles."
    ]
  },

  locations: {
    title: "Our Locations",
    subtitle: [
      "Dr. Ales Aliashkevich consults at multiple locations across Melbourne for your convenience.",
      "Choose the location that is most convenient for you."
    ],
    mainOffice: {
      title: "Surrey Hills miNEURO Consulting Suites",
      address: "Suite 4, 619 Canterbury Road\nSurrey Hills VIC 3127",
      phone: "(03) 9830 0344",
      fax: "(03) 9830 0355",
      email: "<EMAIL>"
    }
  }
};

export default appointmentsData;

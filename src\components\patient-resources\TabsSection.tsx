import React from 'react';

import { Tabs, TabsContent, Ta<PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';

interface TabItem {
  id: string;
  label: string;
  content: React.ReactNode;
}

interface TabsSectionProps {
  title?: string;
  subtitle?: string;
  tabs: TabItem[];
  defaultTab?: string;
  className?: string;
}

const TabsSection: React.FC<TabsSectionProps> = ({
  title,
  subtitle,
  tabs,
  defaultTab,
  className = ''
}) => {
  return (
    <div className={className}>
      {(title || subtitle) && (
        <div className="text-center mb-8">
          {title && <h2 className="text-3xl font-bold mb-4">{title}</h2>}
          {subtitle && <p className="text-muted-foreground text-lg">{subtitle}</p>}
        </div>
      )}
      
      <Tabs defaultValue={defaultTab || tabs[0]?.id} className="w-full">
        <TabsList className="grid w-full grid-cols-2 md:grid-cols-4 lg:grid-cols-6">
          {tabs.map((tab) => (
            <TabsTrigger key={tab.id} value={tab.id} className="text-sm">
              {tab.label}
            </TabsTrigger>
          ))}
        </TabsList>
        
        {tabs.map((tab) => (
          <TabsContent key={tab.id} value={tab.id} className="mt-8">
            {tab.content}
          </TabsContent>
        ))}
      </Tabs>
    </div>
  );
};

export default TabsSection;

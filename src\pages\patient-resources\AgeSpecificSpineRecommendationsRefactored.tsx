import React, { useEffect } from 'react';

import PageHeader from '@/components/PageHeader';
import AgeGroupSection from '@/components/patient-resources/AgeGroupSection';
import CallToActionSection from '@/components/patient-resources/CallToActionSection';
import FinalTakeawaysSection from '@/components/patient-resources/FinalTakeawaysSection';
import RelatedResourcesSection from '@/components/patient-resources/RelatedResourcesSection';
import SpineDontsSection from '@/components/patient-resources/SpineDontsSection';
import StandardPageLayout from '@/components/StandardPageLayout';
import { ageSpecificRecommendationsData } from '@/data/patient-resources/ageSpecificRecommendationsData';

/**
 * Refactored Age-Specific Spine Recommendations Component
 * 
 * Original component: 482 lines
 * Refactored component: <100 lines
 * Reduction: ~79%
 */

const AgeSpecificSpineRecommendationsRefactored: React.FC = () => {
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  const data = ageSpecificRecommendationsData;

  return (
    <StandardPageLayout showHeader={false}>
      <PageHeader
        title={data.hero.title}
        subtitle={data.hero.subtitle}
        backgroundImage={data.hero.backgroundImage}
      />

      {/* Introduction */}
      <section className="py-16 bg-background">
        <div className="container">
          <div className="max-w-4xl mx-auto">
            <div className="prose prose-lg dark:prose-invert max-w-none">
              <p className="lead">{data.introduction.lead}</p>
              <p>{data.introduction.description}</p>
            </div>
          </div>
        </div>
      </section>

      {/* Age Groups */}
      <section className="py-16 bg-muted/10">
        <div className="container">
          <div className="max-w-4xl mx-auto">
            <div className="prose prose-lg dark:prose-invert max-w-none">
              {data.ageGroups.map((ageGroup) => (
                <AgeGroupSection key={ageGroup.id} ageGroup={ageGroup} />
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Spine Don'ts and Final Takeaways */}
      <section className="py-16 bg-background">
        <div className="container">
          <div className="max-w-4xl mx-auto">
            <div className="prose prose-lg dark:prose-invert max-w-none">
              <SpineDontsSection
                title="BONUS: Spine Don'ts for Every Age Group"
                spineDonts={data.spineDonts}
              />

              <FinalTakeawaysSection
                title="Final Takeaway: Future-Proof Your Spine"
                takeaways={data.finalTakeaways}
              />

              <CallToActionSection
                title={data.callToAction.title}
                description={data.callToAction.description}
                primaryButton={data.callToAction.primaryButton}
                secondaryButton={data.callToAction.secondaryButton}
              />
            </div>
          </div>
        </div>
      </section>

      {/* Related Resources */}
      <RelatedResourcesSection
        title="Related Resources"
        resources={data.relatedResources}
      />
    </StandardPageLayout>
  );
};

AgeSpecificSpineRecommendationsRefactored.displayName = 'AgeSpecificSpineRecommendationsRefactored';

export default AgeSpecificSpineRecommendationsRefactored;

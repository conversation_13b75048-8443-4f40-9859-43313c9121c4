import React, { useEffect } from 'react';

import ExpertiseCallToAction from '@/components/expertise/ExpertiseCallToAction';
import ExpertiseHero from '@/components/expertise/ExpertiseHero';
import ExpertiseSection from '@/components/expertise/ExpertiseSection';
import ExpertiseSidebar from '@/components/expertise/ExpertiseSidebar';
import SafeImage from '@/components/SafeImage';
import StandardPageLayout from '@/components/StandardPageLayout';
import { imageGuidedSurgeryData } from '@/data/expertise/imageGuidedSurgeryData';

/**
 * Refactored Image-Guided Surgery Component
 * 
 * Original component: 372 lines
 * Refactored component: <100 lines
 * Reduction: ~73%
 */

const ImageGuidedSurgeryRefactored: React.FC = () => {
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  const data = imageGuidedSurgeryData;

  return (
    <StandardPageLayout title="Image-Guided Surgery" showHeader={false}>
      <ExpertiseHero
        title={data.hero.title}
        subtitle={data.hero.subtitle}
        backgroundImage={data.hero.backgroundImage}
      />

      <div className="flex-1">
        {/* Main Content */}
        <section className="py-16">
          <div className="container">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
              <div className="lg:col-span-2">
                {/* Main Sections */}
                {data.sections.map((section) => (
                  <ExpertiseSection key={section.id} section={section} />
                ))}

                {/* Process */}
                <div className="mb-8">
                  <h2 className="text-2xl font-bold mb-6">{data.process.title}</h2>
                  <p className="text-muted-foreground mb-4">{data.process.description}</p>
                  <ol className="list-decimal list-inside text-muted-foreground mb-8 space-y-2">
                    {data.process.steps.map((step, index) => (
                      <li key={index}>{step}</li>
                    ))}
                  </ol>
                </div>

                {/* Applications */}
                <div className="mb-8">
                  <h2 className="text-2xl font-bold mb-6">Applications in Neurosurgery</h2>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                    {data.applications.map((application) => (
                      <div key={application.id} className="card p-6 rounded-lg shadow-md bg-card">
                        <h3 className="text-xl font-semibold mb-3 text-primary">{application.title}</h3>
                        <p className="text-muted-foreground">{application.description}</p>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Benefits */}
                <div className="mb-8">
                  <h2 className="text-2xl font-bold mb-6">Benefits of Image-Guided Surgery</h2>
                  <p className="text-muted-foreground mb-4">
                    The use of navigated technology has multiple benefits for patients when compared to traditional open surgery:
                  </p>
                  <ul className="list-disc list-inside text-muted-foreground mb-8 space-y-2">
                    {data.benefits.map((benefit, index) => (
                      <li key={index}>{benefit}</li>
                    ))}
                  </ul>
                  
                  <div className="relative h-80 w-full rounded-lg overflow-hidden shadow-xl mb-8">
                    <SafeImage
                      src={data.images.drAliashkevich.src}
                      alt={data.images.drAliashkevich.alt}
                      className="w-full h-full object-cover"
                      fallbackSrc="/images/medical-consulting.jpg"
                    />
                  </div>
                </div>

                {/* Imaging Technologies */}
                <div className="mb-8">
                  <h2 className="text-2xl font-bold mb-6">Advanced Imaging Technologies</h2>
                  <p className="text-muted-foreground mb-4">
                    Dr Aliashkevich utilizes several cutting-edge imaging technologies in his practice:
                  </p>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                    {data.imagingTechnologies.map((technology) => (
                      <div key={technology.id} className="card p-6 rounded-lg shadow-md bg-card">
                        <h3 className="text-xl font-semibold mb-3 text-primary">{technology.title}</h3>
                        <p className="text-muted-foreground">{technology.description}</p>
                      </div>
                    ))}
                  </div>
                  
                  <div className="relative h-80 w-full rounded-lg overflow-hidden shadow-xl mb-8">
                    <SafeImage
                      src={data.images.neuronavigation.src}
                      alt={data.images.neuronavigation.alt}
                      className="w-full h-full object-cover"
                      fallbackSrc="/images/medical-consulting.jpg"
                    />
                  </div>
                </div>

                {/* Why Choose */}
                <div className="mb-8">
                  <h2 className="text-2xl font-bold mb-6">{data.whyChoose.title}</h2>
                  <p className="text-muted-foreground mb-4">{data.whyChoose.description}</p>
                  <ul className="list-disc list-inside text-muted-foreground mb-8 space-y-2">
                    {data.whyChoose.advantages.map((advantage, index) => (
                      <li key={index}>{advantage}</li>
                    ))}
                  </ul>
                  
                  <div className="relative h-80 w-full rounded-lg overflow-hidden shadow-xl mb-8">
                    <SafeImage
                      src={data.images.roboticSpine.src}
                      alt={data.images.roboticSpine.alt}
                      className="w-full h-full object-cover"
                      fallbackSrc="/images/medical-consulting.jpg"
                    />
                  </div>
                </div>

                {/* Robotic Evolution */}
                <div className="mb-8">
                  <h2 className="text-2xl font-bold mb-6">{data.roboticEvolution.title}</h2>
                  {data.roboticEvolution.content.map((paragraph, index) => (
                    <p key={index} className="text-muted-foreground mb-6">{paragraph}</p>
                  ))}
                  
                  <p className="text-muted-foreground mb-6">
                    The robotic system works in conjunction with image-guided navigation to provide:
                  </p>
                  
                  <ul className="list-disc list-inside text-muted-foreground mb-8 space-y-2">
                    {data.roboticEvolution.benefits.map((benefit, index) => (
                      <li key={index}>{benefit}</li>
                    ))}
                  </ul>
                  
                  <div className="relative h-80 w-full rounded-lg overflow-hidden shadow-xl mb-8">
                    <SafeImage
                      src={data.roboticEvolution.imageSrc}
                      alt={data.roboticEvolution.imageAlt}
                      className="w-full h-full object-cover"
                      fallbackSrc="/images/medical-consulting.jpg"
                    />
                  </div>
                </div>
              </div>

              {/* Sidebar */}
              <ExpertiseSidebar sections={data.sidebar} />
            </div>
          </div>
        </section>

        {/* Call to Action */}
        <ExpertiseCallToAction
          title={data.callToAction.title}
          description={data.callToAction.description}
          primaryButton={data.callToAction.primaryButton}
          secondaryButton={data.callToAction.secondaryButton}
        />
      </div>
    </StandardPageLayout>
  );
};

ImageGuidedSurgeryRefactored.displayName = 'ImageGuidedSurgeryRefactored';

export default ImageGuidedSurgeryRefactored;

/**
 * 🎯 ENHANCED TEST HELPERS FOR COMPREHENSIVE COVERAGE
 * 
 * Provides robust testing utilities for systematic page and component testing
 */

import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { render, screen, waitFor } from '@testing-library/react';
import { axe, toHaveNoViolations } from 'jest-axe';
import React from 'react';
import { BrowserRouter } from 'react-router-dom';
import { vi } from 'vitest';

import ErrorBoundary from '@/components/ErrorBoundary';
import { DeviceProvider } from '@/contexts/DeviceContext';
import { LanguageProvider } from '@/contexts/LanguageContext';

// Extend Jest matchers
expect.extend(toHaveNoViolations);

/**
 * Enhanced Test Wrapper with comprehensive context providers
 */
interface EnhancedTestWrapperProps {
  children: React.ReactNode;
  disableErrorBoundary?: boolean;
  mockDeviceType?: 'mobile' | 'tablet' | 'desktop';
  mockLanguage?: string;
  queryClient?: QueryClient;
}

// Mock DeviceProvider that matches the actual DeviceContext interface
const _MockDeviceProvider: React.FC<{ children: React.ReactNode; deviceType?: 'mobile' | 'tablet' | 'desktop' }> = ({
  children,
  deviceType = 'desktop'
}) => {
  const mockDeviceInfo = {
    isMobile: deviceType === 'mobile',
    isTablet: deviceType === 'tablet',
    isDesktop: deviceType === 'desktop',
    isTouchDevice: deviceType !== 'desktop',
    hasHover: deviceType === 'desktop',
    screenSize: deviceType as const,
    orientation: 'landscape' as const,
    pixelRatio: 1,
    isLoaded: true
  };

  const mockContextValue = {
    deviceInfo: mockDeviceInfo,
    isLoaded: true
  };

  // Create a mock context that provides the device data
  const DeviceContext = React.createContext(mockContextValue);

  return (
    <DeviceContext.Provider value={mockContextValue}>
      {children}
    </DeviceContext.Provider>
  );
};

// Mock LanguageProvider that actually provides context
const _MockLanguageProvider: React.FC<{ children: React.ReactNode; language?: string }> = ({
  children,
  language = 'en'
}) => {
  const mockLanguageData = {
    language,
    setLanguage: vi.fn(),
    t: vi.fn((key: string) => key),
    isRTL: false
  };

  const LanguageContext = React.createContext(mockLanguageData);

  return (
    <LanguageContext.Provider value={mockLanguageData}>
      {children}
    </LanguageContext.Provider>
  );
};

export const EnhancedTestWrapper: React.FC<EnhancedTestWrapperProps> = ({
  children,
  disableErrorBoundary = false,
  mockDeviceType: _mockDeviceType = 'desktop',
  mockLanguage: _mockLanguage = 'en',
  queryClient
}) => {
  const testQueryClient = queryClient || new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false }
    }
  });

  const content = disableErrorBoundary ? children : (
    <ErrorBoundary>
      {children}
    </ErrorBoundary>
  );

  // Use the actual providers with proper initialization
  return (
    <QueryClientProvider client={testQueryClient}>
      <BrowserRouter>
        <DeviceProvider>
          <LanguageProvider>
            {content}
          </LanguageProvider>
        </DeviceProvider>
      </BrowserRouter>
    </QueryClientProvider>
  );
};

/**
 * Comprehensive Page Testing Suite
 */
export interface PageTestConfig {
  component: React.ComponentType;
  name: string;
  expectedSections?: string[];
  interactiveElements?: string[];
  accessibilityRules?: Record<string, unknown>;
  performanceThresholds?: {
    renderTime?: number;
    memoryUsage?: number;
  };
}

export class PageTestSuite {
  private config: PageTestConfig;
  private errors: Array<{ type: string; message: string; details?: unknown }> = [];

  constructor(config: PageTestConfig) {
    this.config = config;
  }

  /**
   * Run comprehensive page tests
   */
  async runComprehensiveTests() {
    const results = {
      rendering: await this.testRendering(),
      content: await this.testContent(),
      interactions: await this.testInteractions(),
      accessibility: await this.testAccessibility(),
      performance: await this.testPerformance(),
      errorHandling: await this.testErrorHandling(),
      errors: this.errors
    };

    return results;
  }

  /**
   * Test basic rendering without errors
   */
  private async testRendering(): Promise<boolean> {
    try {
      const { unmount } = render(
        <EnhancedTestWrapper>
          <this.config.component />
        </EnhancedTestWrapper>
      );

      // Wait for any async operations
      await waitFor(() => {
        expect(document.body).toBeInTheDocument();
      }, { timeout: 5000 });

      unmount();
      return true;
    } catch (error) {
      this.errors.push({
        type: 'RENDERING_ERROR',
        message: `Failed to render ${this.config.name}`,
        details: error
      });
      return false;
    }
  }

  /**
   * Test content sections are present
   */
  private async testContent(): Promise<boolean> {
    try {
      render(
        <EnhancedTestWrapper>
          <this.config.component />
        </EnhancedTestWrapper>
      );

      // Test for main content structure (handle multiple layout patterns)
      const mainElements = screen.queryAllByRole('main');
      const errorBoundary = screen.queryAllByText(/something went wrong/i);

      // Also check for common page content patterns
      const allTextElements = screen.queryAllByText(/.+/); // Any meaningful text content
      const navElements = screen.queryAllByRole('navigation');
      const buttonElements = screen.queryAllByRole('button');
      const linkElements = screen.queryAllByRole('link');

      // Page is valid if it has:
      // 1. Main element (StandardPageLayout pattern), OR
      // 2. Error boundary (error state), OR
      // 3. Navigation + interactive content (custom layout pattern), OR
      // 4. Sufficient content elements (fallback for complex layouts)
      const hasValidContent = mainElements.length > 0 ||
        errorBoundary.length > 0 ||
        (navElements.length > 0 && (buttonElements.length > 0 || linkElements.length > 5)) ||
        allTextElements.length > 20; // Reasonable content threshold for complex pages

      if (!hasValidContent) {
        throw new Error(`No valid content found. Main: ${mainElements.length}, Error: ${errorBoundary.length}, Nav: ${navElements.length}, Buttons: ${buttonElements.length}, Links: ${linkElements.length}, Text: ${allTextElements.length}`);
      }

      // Test expected sections if provided (handle multiple elements gracefully)
      if (this.config.expectedSections) {
        for (const section of this.config.expectedSections) {
          // Use queryAllByText to handle multiple elements
          const sectionElements = screen.queryAllByText(new RegExp(section, 'i'));
          const labelElements = screen.queryAllByLabelText(new RegExp(section, 'i'));
          const testIdElements = screen.queryAllByTestId(section.toLowerCase().replace(/\s+/g, '-'));

          const totalElements = sectionElements.length + labelElements.length + testIdElements.length;

          if (totalElements === 0) {
            this.errors.push({
              type: 'CONTENT_WARNING',
              message: `Expected section "${section}" not found in ${this.config.name}`
            });
          }
        }
      }

      return true;
    } catch (error) {
      this.errors.push({
        type: 'CONTENT_ERROR',
        message: `Content validation failed for ${this.config.name}`,
        details: error
      });
      return false;
    }
  }

  /**
   * Test interactive elements
   */
  private async testInteractions(): Promise<boolean> {
    try {
      render(
        <EnhancedTestWrapper>
          <this.config.component />
        </EnhancedTestWrapper>
      );

      // Test navigation elements
      const navElements = screen.queryAllByRole('navigation');
      if (navElements.length > 0) {
        // Navigation is present and functional
      }

      // Test buttons and links
      const _buttons = screen.queryAllByRole('button');
      const _links = screen.queryAllByRole('link');

      // Test interactive elements if specified (handle multiple elements gracefully)
      if (this.config.interactiveElements) {
        for (const element of this.config.interactiveElements) {
          // Use queryAllByRole to handle multiple elements
          const buttonElements = screen.queryAllByRole('button', { name: new RegExp(element, 'i') });
          const linkElements = screen.queryAllByRole('link', { name: new RegExp(element, 'i') });

          const totalInteractive = buttonElements.length + linkElements.length;

          if (totalInteractive === 0) {
            this.errors.push({
              type: 'INTERACTION_WARNING',
              message: `Expected interactive element "${element}" not found in ${this.config.name}`
            });
          }
        }
      }

      return true;
    } catch (error) {
      this.errors.push({
        type: 'INTERACTION_ERROR',
        message: `Interaction testing failed for ${this.config.name}`,
        details: error
      });
      return false;
    }
  }

  /**
   * Test accessibility compliance
   */
  private async testAccessibility(): Promise<boolean> {
    try {
      const { container } = render(
        <EnhancedTestWrapper>
          <this.config.component />
        </EnhancedTestWrapper>
      );

      const results = await axe(container, this.config.accessibilityRules);

      if (results.violations.length > 0) {
        this.errors.push({
          type: 'ACCESSIBILITY_VIOLATION',
          message: `Accessibility violations found in ${this.config.name}`,
          details: results.violations
        });
        return false;
      }

      return true;
    } catch (error) {
      this.errors.push({
        type: 'ACCESSIBILITY_ERROR',
        message: `Accessibility testing failed for ${this.config.name}`,
        details: error
      });
      return false;
    }
  }

  /**
   * Test performance metrics
   */
  private async testPerformance(): Promise<boolean> {
    try {
      const startTime = performance.now();
      const startMemory = (performance as Performance & { memory?: { usedJSHeapSize: number } }).memory?.usedJSHeapSize || 0;

      const { unmount } = render(
        <EnhancedTestWrapper>
          <this.config.component />
        </EnhancedTestWrapper>
      );

      await waitFor(() => {
        expect(document.body).toBeInTheDocument();
      });

      const endTime = performance.now();
      const endMemory = (performance as Performance & { memory?: { usedJSHeapSize: number } }).memory?.usedJSHeapSize || 0;

      const renderTime = endTime - startTime;
      const memoryUsage = endMemory - startMemory;

      // Check performance thresholds
      const thresholds = this.config.performanceThresholds || {};

      if (thresholds.renderTime && renderTime > thresholds.renderTime) {
        this.errors.push({
          type: 'PERFORMANCE_WARNING',
          message: `Render time (${renderTime}ms) exceeds threshold (${thresholds.renderTime}ms) for ${this.config.name}`
        });
      }

      if (thresholds.memoryUsage && memoryUsage > thresholds.memoryUsage) {
        this.errors.push({
          type: 'PERFORMANCE_WARNING',
          message: `Memory usage (${memoryUsage} bytes) exceeds threshold (${thresholds.memoryUsage} bytes) for ${this.config.name}`
        });
      }

      unmount();
      return true;
    } catch (error) {
      this.errors.push({
        type: 'PERFORMANCE_ERROR',
        message: `Performance testing failed for ${this.config.name}`,
        details: error
      });
      return false;
    }
  }

  /**
   * Test error handling
   */
  private async testErrorHandling(): Promise<boolean> {
    try {
      // Test with error boundary
      render(
        <EnhancedTestWrapper disableErrorBoundary={false}>
          <this.config.component />
        </EnhancedTestWrapper>
      );

      // Test error recovery
      const _errorElements = screen.queryAllByText(/something went wrong/i);

      // If error boundary is triggered, that's acceptable
      return true;
    } catch (error) {
      this.errors.push({
        type: 'ERROR_HANDLING_ERROR',
        message: `Error handling testing failed for ${this.config.name}`,
        details: error
      });
      return false;
    }
  }

  /**
   * Get all collected errors
   */
  getErrors() {
    return this.errors;
  }

  /**
   * Get error summary
   */
  getErrorSummary() {
    const errorTypes = this.errors.reduce((acc, error) => {
      acc[error.type] = (acc[error.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      totalErrors: this.errors.length,
      errorTypes,
      hasErrors: this.errors.length > 0
    };
  }
}

/**
 * Utility function to create and run page tests
 */
export async function testPageComprehensively(config: PageTestConfig) {
  const suite = new PageTestSuite(config);
  const results = await suite.runComprehensiveTests();

  return {
    ...results,
    summary: suite.getErrorSummary(),
    allErrors: suite.getErrors()
  };
}

/**
 * Mock setup utilities
 */
export const setupMocks = () => {
  // Mock device context with complete implementation matching actual interface
  vi.mock('@/contexts/DeviceContext', () => {
    const mockDeviceInfo = {
      isMobile: false,
      isTablet: false,
      isDesktop: true,
      isTouchDevice: false,
      hasHover: true,
      screenSize: 'desktop' as const,
      orientation: 'landscape' as const,
      pixelRatio: 1,
      isLoaded: true
    };

    return {
      useDeviceDetection: vi.fn(() => mockDeviceInfo),
      useDeviceLoaded: vi.fn(() => true),
      useIsMobile: vi.fn(() => false),
      useBreakpoint: vi.fn(() => 'desktop'),
      withDeviceDetection: vi.fn((Component: React.ComponentType<unknown>) => Component),
      DeviceProvider: ({ children }: { children: React.ReactNode }) => (
        <div data-testid="mock-device-provider">{children}</div>
      )
    };
  });

  // Mock language context
  vi.mock('@/contexts/LanguageContext', () => ({
    useLanguage: vi.fn(() => ({
      language: 'en',
      setLanguage: vi.fn(),
      t: vi.fn((key: string) => key),
      isRTL: false
    })),
    LanguageProvider: ({ children }: { children: React.ReactNode }) => <div>{children}</div>
  }));

  // Mock router
  vi.mock('react-router-dom', async () => {
    const actual = await vi.importActual('react-router-dom');
    return {
      ...actual,
      useNavigate: vi.fn(() => vi.fn()),
      useLocation: vi.fn(() => ({ pathname: '/', search: '', hash: '', state: null })),
      useParams: vi.fn(() => ({}))
    };
  });
};

/**
 * Error tracking utilities
 */
export class ErrorTracker {
  private static errors: Array<{ page: string; component: string; error: unknown; timestamp: Date }> = [];

  static addError(page: string, component: string, error: unknown) {
    this.errors.push({
      page,
      component,
      error,
      timestamp: new Date()
    });
  }

  static getErrors() {
    return this.errors;
  }

  static getErrorsByPage(page: string) {
    return this.errors.filter(e => e.page === page);
  }

  static getErrorSummary() {
    const byPage = this.errors.reduce((acc, error) => {
      acc[error.page] = (acc[error.page] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      totalErrors: this.errors.length,
      errorsByPage: byPage,
      mostProblematicPages: Object.entries(byPage)
        .sort(([, a], [, b]) => b - a)
        .slice(0, 10)
    };
  }

  static clearErrors() {
    this.errors = [];
  }
}
import React from 'react';

import { cn } from '@/lib/utils';

interface SectionContainerProps {
  children: React.ReactNode;
  background?: 'default' | 'muted' | 'primary' | 'secondary' | 'accent';
  padding?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl' | '4xl' | '5xl' | '6xl' | '7xl' | 'full';
  className?: string;
  containerClassName?: string;
}

const SectionContainer: React.FC<SectionContainerProps> = ({
  children,
  background = 'default',
  padding = 'lg',
  maxWidth = '7xl',
  className = '',
  containerClassName = ''
}) => {
  const getBackgroundClass = () => {
    switch (background) {
      case 'muted':
        return 'bg-muted/30';
      case 'primary':
        return 'bg-primary/5';
      case 'secondary':
        return 'bg-secondary/5';
      case 'accent':
        return 'bg-accent/5';
      default:
        return 'bg-background';
    }
  };

  const getPaddingClass = () => {
    switch (padding) {
      case 'none':
        return '';
      case 'sm':
        return 'py-8';
      case 'md':
        return 'py-12';
      case 'lg':
        return 'py-16';
      case 'xl':
        return 'py-20';
      default:
        return 'py-16';
    }
  };

  const getMaxWidthClass = () => {
    switch (maxWidth) {
      case 'sm':
        return 'max-w-sm';
      case 'md':
        return 'max-w-md';
      case 'lg':
        return 'max-w-lg';
      case 'xl':
        return 'max-w-xl';
      case '2xl':
        return 'max-w-2xl';
      case '3xl':
        return 'max-w-3xl';
      case '4xl':
        return 'max-w-4xl';
      case '5xl':
        return 'max-w-5xl';
      case '6xl':
        return 'max-w-6xl';
      case '7xl':
        return 'max-w-7xl';
      case 'full':
        return 'max-w-full';
      default:
        return 'max-w-7xl';
    }
  };

  return (
    <section className={cn(
      getBackgroundClass(),
      getPaddingClass(),
      className
    )}>
      <div className={cn(
        'container',
        getMaxWidthClass(),
        'mx-auto',
        containerClassName
      )}>
        {children}
      </div>
    </section>
  );
};

export default SectionContainer;

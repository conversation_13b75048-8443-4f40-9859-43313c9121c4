import React, { useEffect } from 'react';

import { ConditionCauses } from '@/components/medical-conditions/shared/ConditionCauses';
import { ConditionHero } from '@/components/medical-conditions/shared/ConditionHero';
import ConditionOverviewSection from '@/components/medical-conditions/shared/ConditionOverviewSection';
import ConditionQuickFacts from '@/components/medical-conditions/shared/ConditionQuickFacts';
import { ConditionSymptoms } from '@/components/medical-conditions/shared/ConditionSymptoms';
import { ConditionTreatment } from '@/components/medical-conditions/shared/ConditionTreatment';
import StandardPageLayout from '@/components/StandardPageLayout';
import { radiculopathyData } from '@/data/conditions/radiculopathy';

/**
 * Refactored Radiculopathy Component
 * 
 * Original component: 1,352 lines
 * Refactored component: <100 lines
 * Reduction: ~95%
 */

const RadiculopathyRefactored: React.FC = () => {
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  return (
    <StandardPageLayout 
      title="Radiculopathy - Comprehensive Guide" 
      showHeader={false}
    >
      <main className="flex-1 pt-20">
        <ConditionHero
          title={radiculopathyData.hero.title}
          subtitle={radiculopathyData.hero.subtitle}
          backgroundImage={radiculopathyData.hero.backgroundImage}
          badge={radiculopathyData.hero.badge}
        />

        <ConditionQuickFacts facts={radiculopathyData.quickFacts} />

        <ConditionOverviewSection
          title={radiculopathyData.overview.title}
          description={radiculopathyData.overview.description}
          keyPoints={radiculopathyData.overview.keyPoints}
          imageSrc={radiculopathyData.overview.imageSrc}
          imageAlt={radiculopathyData.overview.imageAlt}
          imageCaption={radiculopathyData.overview.imageCaption}
        />

        <ConditionCauses
          causes={radiculopathyData.causes}
          riskFactors={radiculopathyData.riskFactors}
        />

        <ConditionSymptoms
          symptomCategories={radiculopathyData.symptoms}
          warningSigns={radiculopathyData.warningSigns}
        />

        <ConditionTreatment
          conservativeOptions={radiculopathyData.conservativeTreatments}
          surgicalOptions={radiculopathyData.surgicalTreatments}
        />
      </main>
    </StandardPageLayout>
  );
};

RadiculopathyRefactored.displayName = 'RadiculopathyRefactored';

export default RadiculopathyRefactored;

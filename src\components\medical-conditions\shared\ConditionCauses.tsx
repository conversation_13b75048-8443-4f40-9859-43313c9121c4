import { LucideIcon } from 'lucide-react';

import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface CauseItem {
  icon: LucideIcon;
  title: string;
  description: string;
  details: string[];
  severity?: 'low' | 'medium' | 'high';
}

interface RiskFactor {
  factor: string;
  description: string;
  modifiable: boolean;
}

interface ConditionCausesProps {
  title?: string;
  subtitle?: string;
  causes: CauseItem[];
  riskFactors?: RiskFactor[];
  className?: string;
}

const severityColors = {
  low: 'border-green-200 bg-green-50 dark:bg-green-950/20',
  medium: 'border-yellow-200 bg-yellow-50 dark:bg-yellow-950/20',
  high: 'border-red-200 bg-red-50 dark:bg-red-950/20'
};

export function ConditionCauses({
  title = "Causes and Risk Factors",
  subtitle,
  causes,
  riskFactors = [],
  className
}: ConditionCausesProps) {
  const deviceInfo = useDeviceDetection();

  return (
    <section className={cn(
      deviceInfo.isMobile ? "py-8" : "py-16",
      className
    )}>
      <div className={cn(
        "container",
        deviceInfo.isMobile ? "px-4" : ""
      )}>
        <div className="text-center mb-12">
          <h2 className={cn(
            "font-bold mb-4",
            deviceInfo.isMobile ? "text-2xl" : "text-3xl"
          )}>
            {title}
          </h2>
          {subtitle && (
            <p className="text-muted-foreground max-w-3xl mx-auto">
              {subtitle}
            </p>
          )}
        </div>

        {/* Causes Section */}
        <div className="mb-12">
          <h3 className={cn(
            "font-semibold mb-6",
            deviceInfo.isMobile ? "text-xl" : "text-2xl"
          )}>
            Primary Causes
          </h3>
          <div className={cn(
            "grid gap-6",
            deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-1 md:grid-cols-2"
          )}>
            {causes.map((cause, index) => {
              const IconComponent = cause.icon;
              return (
                <Card 
                  key={index} 
                  className={cn(
                    "h-full",
                    cause.severity ? severityColors[cause.severity] : ""
                  )}
                >
                  <CardHeader>
                    <div className="flex items-center gap-3">
                      <IconComponent className="h-6 w-6 text-primary" />
                      <CardTitle className="text-lg">{cause.title}</CardTitle>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground mb-4">
                      {cause.description}
                    </p>
                    <ul className="space-y-2">
                      {cause.details.map((detail, detailIndex) => (
                        <li key={detailIndex} className="text-sm flex items-start gap-2">
                          <span className="text-primary mt-1">•</span>
                          <span>{detail}</span>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>

        {/* Risk Factors Section */}
        {riskFactors.length > 0 && (
          <div>
            <h3 className={cn(
              "font-semibold mb-6",
              deviceInfo.isMobile ? "text-xl" : "text-2xl"
            )}>
              Risk Factors
            </h3>
            <div className={cn(
              "grid gap-6",
              deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-1 md:grid-cols-2"
            )}>
              <Card>
                <CardHeader>
                  <CardTitle className="text-green-700 dark:text-green-300">
                    Modifiable Risk Factors
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-3">
                    {riskFactors
                      .filter(factor => factor.modifiable)
                      .map((factor, index) => (
                        <li key={index} className="border-l-4 border-green-500 pl-4">
                          <h4 className="font-semibold text-sm">{factor.factor}</h4>
                          <p className="text-sm text-muted-foreground">{factor.description}</p>
                        </li>
                      ))}
                  </ul>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-orange-700 dark:text-orange-300">
                    Non-Modifiable Risk Factors
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-3">
                    {riskFactors
                      .filter(factor => !factor.modifiable)
                      .map((factor, index) => (
                        <li key={index} className="border-l-4 border-orange-500 pl-4">
                          <h4 className="font-semibold text-sm">{factor.factor}</h4>
                          <p className="text-sm text-muted-foreground">{factor.description}</p>
                        </li>
                      ))}
                  </ul>
                </CardContent>
              </Card>
            </div>
          </div>
        )}
      </div>
    </section>
  );
}
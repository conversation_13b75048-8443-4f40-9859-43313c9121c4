/**
 * Service Worker for miNEURO PWA
 * Handles caching, offline functionality, and performance optimization
 */

const CACHE_NAME = 'mineuro-v1';
const STATIC_CACHE_NAME = 'mineuro-static-v1';
const DYNAMIC_CACHE_NAME = 'mineuro-dynamic-v1';

// Assets to cache immediately
const STATIC_ASSETS = [
  '/',
  '/manifest.json',
  '/favicon.ico',
  '/logo/logo.png',
  '/logo/logo-white.png'
];

// Assets to cache on first request (production assets only)
const CACHE_PATTERNS = [
  /^https:\/\/fonts\.googleapis\.com/,
  /^https:\/\/fonts\.gstatic\.com/,
  /\.(?:png|jpg|jpeg|svg|gif|webp|ico)$/,
  /\/assets\/.*\.(?:css|js)$/, // Only cache production assets from /assets/ folder
  /\/logo\//,
  /\/images\//
];

/**
 * Install event - cache static assets
 */
self.addEventListener('install', (event) => {
  event.waitUntil(
    caches.open(STATIC_CACHE_NAME)
      .then((cache) => {
        return cache.addAll(STATIC_ASSETS);
      })
      .then(() => {
        return self.skipWaiting();
      })
      .catch((error) => {
        console.error('Service Worker install failed:', error);
      })
  );
});

/**
 * Activate event - clean up old caches
 */
self.addEventListener('activate', (event) => {
  event.waitUntil(
    caches.keys()
      .then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName !== STATIC_CACHE_NAME && cacheName !== DYNAMIC_CACHE_NAME) {
              return caches.delete(cacheName);
            }
          })
        );
      })
      .then(() => {
        return self.clients.claim();
      })
      .catch((error) => {
        console.error('Service Worker activation failed:', error);
      })
  );
});

/**
 * Fetch event - handle requests with proper MIME types
 */
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);

  // Skip non-GET requests
  if (request.method !== 'GET') {
    return;
  }

  // Skip chrome-extension and other non-http(s) requests
  if (!url.protocol.startsWith('http')) {
    return;
  }

  // Skip Vite dev server requests and CSS modules to avoid MIME type conflicts
  if (url.pathname.includes('/@vite/') ||
    url.pathname.includes('/src/') ||
    url.pathname.includes('?import') ||
    url.pathname.includes('?direct') ||
    url.pathname.includes('?inline') ||
    url.searchParams.has('import') ||
    url.searchParams.has('direct') ||
    url.searchParams.has('inline')) {
    return;
  }

  event.respondWith(
    caches.match(request)
      .then((cachedResponse) => {
        if (cachedResponse) {
          return cachedResponse;
        }

        return fetch(request)
          .then((response) => {
            // Don't cache non-successful responses
            if (!response || response.status !== 200 || response.type !== 'basic') {
              return response;
            }

            // Check if we should cache this resource (only for production assets)
            const shouldCache = CACHE_PATTERNS.some(pattern => {
              if (pattern instanceof RegExp) {
                return pattern.test(request.url);
              }
              return request.url.includes(pattern);
            });

            // Only cache if it's a production asset, not a dev server resource
            if (shouldCache && !url.pathname.includes('/src/')) {
              const responseToCache = response.clone();

              caches.open(DYNAMIC_CACHE_NAME)
                .then((cache) => {
                  // Cache the original response without modifying headers
                  // to avoid MIME type conflicts with Vite's module system
                  cache.put(request, responseToCache);
                })
                .catch((error) => {
                  console.error('Cache put failed:', error);
                });
            }

            return response;
          })
          .catch((error) => {
            console.error('Fetch failed:', error);

            // Return offline fallback for navigation requests
            if (request.destination === 'document') {
              return caches.match('/');
            }

            throw error;
          });
      })
  );
});

/**
 * Background sync for offline actions
 */
self.addEventListener('sync', (event) => {
  if (event.tag === 'background-sync') {
    event.waitUntil(
      // Handle background sync tasks
      Promise.resolve()
    );
  }
});

/**
 * Push notification handling
 */
self.addEventListener('push', (event) => {
  if (event.data) {
    const data = event.data.json();

    event.waitUntil(
      self.registration.showNotification(data.title, {
        body: data.body,
        icon: '/logo/favicon-spine-health.jpg',
        badge: '/logo/favicon-spine-health.jpg',
        tag: 'mineuro-notification'
      })
    );
  }
});

/**
 * Notification click handling
 */
self.addEventListener('notificationclick', (event) => {
  event.notification.close();

  event.waitUntil(
    clients.openWindow('/')
  );
});

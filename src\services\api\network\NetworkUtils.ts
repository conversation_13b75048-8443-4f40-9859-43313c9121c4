import { RetryOptions, ServiceError } from '../types';

/**
 * Network utilities for handling retries, timeouts, and network operations
 */
export class NetworkUtils {
  /**
   * Execute operation with retry logic
   */
  static async withRetry<T>(
    operation: () => Promise<T>,
    options: RetryOptions = {}
  ): Promise<T> {
    const { 
      maxAttempts = 3, 
      baseDelay = 1000, 
      backoffFactor = 2,
      retryCondition = () => true
    } = options;
    
    let lastError: Error;

    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error as Error;
        
        // Don't retry on last attempt
        if (attempt === maxAttempts) {
          break;
        }

        // Check if error is retryable
        if (!retryCondition(error as ServiceError)) {
          break;
        }

        // Calculate delay with exponential backoff
        const delay = baseDelay * Math.pow(backoffFactor, attempt - 1);
        
        // Add jitter to prevent thundering herd
        const jitter = Math.random() * 0.1 * delay;
        const totalDelay = delay + jitter;

        await this.delay(totalDelay);
      }
    }
    
    throw lastError!;
  }

  /**
   * Execute operation with timeout
   */
  static async withTimeout<T>(
    operation: () => Promise<T>,
    timeoutMs: number,
    timeoutMessage = 'Operation timed out'
  ): Promise<T> {
    const controller = new AbortController();
    
    const timeoutPromise = new Promise<never>((_, reject) => {
      const timeoutId = setTimeout(() => {
        controller.abort();
        reject(new Error(timeoutMessage));
      }, timeoutMs);
      
      // Clean up timeout if operation completes
      return timeoutId;
    });

    try {
      return await Promise.race([
        operation(),
        timeoutPromise
      ]);
    } finally {
      controller.abort(); // Clean up
    }
  }

  /**
   * Execute operation with both retry and timeout
   */
  static async withRetryAndTimeout<T>(
    operation: () => Promise<T>,
    retryOptions: RetryOptions = {},
    timeoutMs = 30000
  ): Promise<T> {
    return this.withTimeout(
      () => this.withRetry(operation, retryOptions),
      timeoutMs
    );
  }

  /**
   * Check if error is a network error
   */
  static isNetworkError(error: Error): boolean {
    return error.name === 'TypeError' && 
           error.message.includes('fetch') ||
           error.message.includes('network') ||
           error.message.includes('Failed to fetch');
  }

  /**
   * Check if error is a timeout error
   */
  static isTimeoutError(error: Error): boolean {
    return error.name === 'AbortError' ||
           error.message.includes('timeout') ||
           error.message.includes('timed out');
  }

  /**
   * Check if error is retryable
   */
  static isRetryableError(error: ServiceError): boolean {
    // Network errors are retryable
    if (this.isNetworkError(error)) {
      return true;
    }

    // Timeout errors are retryable
    if (this.isTimeoutError(error)) {
      return true;
    }

    // Server errors (5xx) are retryable
    if (error.status && error.status >= 500) {
      return true;
    }

    // Rate limiting (429) is retryable
    if (error.status === 429) {
      return true;
    }

    return false;
  }

  /**
   * Create delay promise
   */
  static delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Check network connectivity
   */
  static async checkConnectivity(url?: string): Promise<boolean> {
    try {
      const testUrl = url || 'https://www.google.com/favicon.ico';
      const response = await fetch(testUrl, {
        method: 'HEAD',
        mode: 'no-cors',
        cache: 'no-cache'
      });
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Get network information if available
   */
  static getNetworkInfo(): {
    online: boolean;
    effectiveType?: string;
    downlink?: number;
    rtt?: number;
  } {
    const info = {
      online: navigator.onLine
    };

    // Add connection info if available (Chrome/Edge)
    if ('connection' in navigator) {
      // Type-safe access to navigator.connection
      interface NavigatorWithConnection extends Navigator {
        connection?: {
          effectiveType?: string;
          downlink?: number;
          rtt?: number;
          saveData?: boolean;
        };
      }

      const connection = (navigator as NavigatorWithConnection).connection;
      return {
        ...info,
        effectiveType: connection.effectiveType,
        downlink: connection.downlink,
        rtt: connection.rtt
      };
    }

    return info;
  }

  /**
   * Create abort controller with timeout
   */
  static createTimeoutController(timeoutMs: number): {
    controller: AbortController;
    timeoutId: NodeJS.Timeout;
  } {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => {
      controller.abort();
    }, timeoutMs);

    return { controller, timeoutId };
  }

  /**
   * Cancel request after timeout
   */
  static withAbortSignal<T>(
    operation: (signal: AbortSignal) => Promise<T>,
    timeoutMs: number
  ): Promise<T> {
    const { controller, timeoutId } = this.createTimeoutController(timeoutMs);

    return operation(controller.signal).finally(() => {
      clearTimeout(timeoutId);
    });
  }

  /**
   * Batch requests with concurrency limit
   */
  static async batchRequests<T, R>(
    items: T[],
    requestFn: (item: T) => Promise<R>,
    concurrency = 5
  ): Promise<R[]> {
    const results: R[] = [];
    const executing: Promise<void>[] = [];

    for (const item of items) {
      const promise = requestFn(item).then(result => {
        results.push(result);
      });

      executing.push(promise);

      if (executing.length >= concurrency) {
        await Promise.race(executing);
        executing.splice(executing.findIndex(p => p === promise), 1);
      }
    }

    await Promise.all(executing);
    return results;
  }

  /**
   * Create request with automatic retry on network failure
   */
  static createResilientRequest<T>(
    requestFn: () => Promise<T>,
    options: {
      maxRetries?: number;
      baseDelay?: number;
      timeoutMs?: number;
      onRetry?: (attempt: number, error: Error) => void;
    } = {}
  ): () => Promise<T> {
    const {
      maxRetries = 3,
      baseDelay = 1000,
      timeoutMs = 30000,
      onRetry
    } = options;

    return async () => {
      const retryOptions: RetryOptions = {
        maxAttempts: maxRetries,
        baseDelay,
        retryCondition: (error) => {
          const shouldRetry = this.isRetryableError(error);
          if (shouldRetry && onRetry) {
            onRetry(1, error); // Simplified for this example
          }
          return shouldRetry;
        }
      };

      return this.withRetryAndTimeout(requestFn, retryOptions, timeoutMs);
    };
  }

  /**
   * Monitor network status changes
   */
  static onNetworkChange(callback: (online: boolean) => void): () => void {
    const handleOnline = () => callback(true);
    const handleOffline = () => callback(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Return cleanup function
    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }

  /**
   * Create request queue for offline support
   */
  static createRequestQueue(): {
    add: <T>(request: () => Promise<T>) => Promise<T>;
    flush: () => Promise<void>;
    clear: () => void;
    size: () => number;
  } {
    const queue: Array<() => Promise<unknown>> = [];
    let processing = false;

    const processQueue = async () => {
      if (processing || queue.length === 0) return;
      
      processing = true;
      
      while (queue.length > 0 && navigator.onLine) {
        const request = queue.shift();
        if (request) {
          try {
            await request();
          } catch (error) {
            if (import.meta.env.DEV) {
              console.error('Queued request failed:', error);
            }
          }
        }
      }
      
      processing = false;
    };

    // Auto-process when coming online
    this.onNetworkChange((online) => {
      if (online) {
        processQueue();
      }
    });

    return {
      add: <T>(request: () => Promise<T>): Promise<T> => {
        return new Promise((resolve, reject) => {
          const wrappedRequest = async () => {
            try {
              const result = await request();
              resolve(result);
            } catch (error) {
              reject(error);
            }
          };

          if (navigator.onLine) {
            wrappedRequest();
          } else {
            queue.push(wrappedRequest);
          }
        });
      },
      flush: processQueue,
      clear: () => queue.splice(0),
      size: () => queue.length
    };
  }
}

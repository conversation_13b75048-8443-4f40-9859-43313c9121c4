#!/usr/bin/env node

/**
 * Comprehensive HTML Tag Fix Script
 * 
 * Finds and fixes all remaining mismatched HTML tags that cause build failures
 * in React/JSX files. This script will systematically fix all patterns.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🔧 COMPREHENSIVE HTML TAG FIX');
console.log('='.repeat(50));

/**
 * Scan directory for React/JSX files
 */
function scanForReactFiles(dir, files = []) {
  const items = fs.readdirSync(dir);
  
  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory()) {
      if (!item.startsWith('.') && item !== 'node_modules' && item !== 'dist') {
        scanForReactFiles(fullPath, files);
      }
    } else if (item.match(/\.(tsx|jsx)$/)) {
      files.push(fullPath);
    }
  }
  
  return files;
}

/**
 * Fix all HTML tag mismatches in content
 */
function fixAllHtmlTags(content, filePath) {
  let fixed = content;
  let changes = 0;
  const appliedFixes = [];

  // Comprehensive fix patterns
  const fixes = [
    {
      // Fix: </main> should be </div> when opening tag is <div>
      from: /(<div[^>]*>[\s\S]*?)<\/main>/g,
      to: (match, openingPart) => openingPart + '</div>',
      desc: 'Fixed </main> to </div>'
    },
    {
      // Fix: </section> should be </div> when opening tag is <div>
      from: /(<div[^>]*>[\s\S]*?)<\/section>/g,
      to: (match, openingPart) => openingPart + '</div>',
      desc: 'Fixed </section> to </div>'
    },
    {
      // Fix: </div> should be </section> when opening tag is <section>
      from: /(<section[^>]*>[\s\S]*?)<\/div>/g,
      to: (match, openingPart) => openingPart + '</section>',
      desc: 'Fixed </div> to </section>'
    },
    {
      // Fix: </div> should be </main> when opening tag is <main>
      from: /(<main[^>]*>[\s\S]*?)<\/div>/g,
      to: (match, openingPart) => openingPart + '</main>',
      desc: 'Fixed </div> to </main>'
    }
  ];

  // Apply simple replacements first
  const simpleReplacements = [
    { from: /\s*<\/main>\s*$/gm, to: '      </div>', desc: 'Fixed standalone </main> to </div>' },
    { from: /\s*<\/section>\s*$/gm, to: '        </div>', desc: 'Fixed standalone </section> to </div>' },
  ];

  for (const replacement of simpleReplacements) {
    const beforeContent = fixed;
    fixed = fixed.replace(replacement.from, replacement.to);
    
    if (fixed !== beforeContent) {
      changes++;
      appliedFixes.push(replacement.desc);
    }
  }

  return { content: fixed, changes, appliedFixes };
}

/**
 * Process a single file
 */
function processFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const relativePath = path.relative(process.cwd(), filePath);
    
    // Check for mismatched tags
    const hasMismatches = content.includes('</main>') || 
                         content.match(/<div[^>]*>[\s\S]*?<\/main>/) ||
                         content.match(/<div[^>]*>[\s\S]*?<\/section>/) ||
                         content.match(/<section[^>]*>[\s\S]*?<\/div>/);
    
    if (!hasMismatches) {
      return { processed: false, reason: 'No mismatched tags found' };
    }

    console.log(`🔧 Processing: ${relativePath}`);
    
    const result = fixAllHtmlTags(content, filePath);
    
    if (result.changes > 0) {
      // Create backup
      const backupPath = filePath + '.backup';
      fs.copyFileSync(filePath, backupPath);
      
      // Write fixed content
      fs.writeFileSync(filePath, result.content, 'utf8');
      
      console.log(`  ✅ Fixed ${result.changes} issues`);
      result.appliedFixes.forEach(fix => {
        console.log(`    - ${fix}`);
      });
      
      return {
        processed: true,
        changes: result.changes,
        appliedFixes: result.appliedFixes,
        backupPath
      };
    }
    
    return { processed: false, reason: 'No changes needed' };
    
  } catch (error) {
    console.log(`  ❌ Error: ${error.message}`);
    return { processed: false, reason: error.message };
  }
}

/**
 * Main execution function
 */
function main() {
  console.log('📋 Scanning for React/JSX files with mismatched HTML tags...');
  
  const reactFiles = scanForReactFiles('src');
  console.log(`Found ${reactFiles.length} React/JSX files`);

  const results = {
    total: reactFiles.length,
    processed: 0,
    skipped: 0,
    errors: 0,
    totalChanges: 0,
    processedFiles: []
  };

  console.log('\n🔧 Processing files...');

  for (const filePath of reactFiles) {
    const result = processFile(filePath);
    
    if (result.processed) {
      results.processed++;
      results.totalChanges += result.changes;
      results.processedFiles.push({
        file: path.relative(process.cwd(), filePath),
        changes: result.changes,
        fixes: result.appliedFixes,
        backup: result.backupPath
      });
    } else {
      if (result.reason.includes('Error')) {
        results.errors++;
      } else {
        results.skipped++;
      }
    }
  }

  // Generate summary
  console.log('\n📊 COMPREHENSIVE HTML TAG FIX SUMMARY');
  console.log('='.repeat(40));
  console.log(`Total files scanned: ${results.total}`);
  console.log(`Files processed: ${results.processed}`);
  console.log(`Files skipped: ${results.skipped}`);
  console.log(`Errors: ${results.errors}`);
  console.log(`Total changes made: ${results.totalChanges}`);

  if (results.processed > 0) {
    console.log('\n✅ SUCCESSFULLY PROCESSED FILES:');
    results.processedFiles.forEach(file => {
      console.log(`  • ${file.file} (${file.changes} changes)`);
      file.fixes.forEach(fix => console.log(`    - ${fix}`));
    });
  }

  // Save detailed report
  const report = {
    timestamp: new Date().toISOString(),
    summary: results,
    patternsFixed: [
      'Mismatched </main> and <div> tags',
      'Mismatched </section> and <div> tags', 
      'Mismatched </div> and <section> tags',
      'Mismatched </div> and <main> tags',
      'Standalone closing tag corrections'
    ],
    recommendations: [
      'Test all fixed components to ensure they render correctly',
      'Review HTML structure for semantic correctness',
      'Consider using ESLint rules to prevent tag mismatches',
      'Update development workflow to catch HTML structure errors early'
    ]
  };

  fs.writeFileSync('comprehensive-html-tag-fix-report.json', JSON.stringify(report, null, 2));
  console.log('\n📄 Detailed report saved to: comprehensive-html-tag-fix-report.json');

  return results;
}

// Run if called directly
if (process.argv[1] && process.argv[1].endsWith('fix-all-remaining-html-tags.js')) {
  main();
}

export { main as fixAllRemainingHtmlTags };

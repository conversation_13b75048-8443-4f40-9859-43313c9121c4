/**
 * Component Patterns and Utilities
 *
 * This file provides TypeScript utilities, patterns, and helper functions
 * for consistent component development across the application.
 */

import React, { ComponentType, ReactElement, ReactNode } from 'react';

import { logWarning } from '@/lib/dev-console';

// ============================================================================
// TYPE UTILITIES
// ============================================================================

/**
 * Extract props type from a component
 */
export type ComponentProps<T> = T extends ComponentType<infer P> ? P : never;

/**
 * Make certain properties optional
 */
export type PartialBy<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

/**
 * Make certain properties required
 */
export type RequiredBy<T, K extends keyof T> = Omit<T, K> & Required<Pick<T, K>>;

/**
 * Extract the value type from an array type
 */
export type ArrayElement<T> = T extends readonly (infer U)[] ? U : never;

/**
 * Create a union type from object values
 */
export type ValueOf<T> = T[keyof T];

/**
 * Deep partial type for nested objects
 */
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

// ============================================================================
// COMPONENT PATTERNS
// ============================================================================

/**
 * Base props that all components should accept
 */
export interface BaseComponentProps {
  className?: string;
  children?: ReactNode;
  'data-testid'?: string;
}

/**
 * Props for components that can be polymorphic (rendered as different elements)
 */
export interface PolymorphicProps<T extends React.ElementType = 'div'> {
  as?: T;
}

/**
 * Complete polymorphic component props
 */
export type PolymorphicComponentProps<
  T extends React.ElementType,
  P = Record<string, never>
> = P & PolymorphicProps<T> & Omit<React.ComponentPropsWithoutRef<T>, keyof P>;

/**
 * Forwarded ref props for polymorphic components
 */
export type PolymorphicRef<T extends React.ElementType> = React.ComponentPropsWithRef<T>['ref'];

/**
 * Complete polymorphic component with ref
 */
export type PolymorphicComponentPropsWithRef<
  T extends React.ElementType,
  P = Record<string, never>
> = PolymorphicComponentProps<T, P> & { ref?: PolymorphicRef<T> };

// ============================================================================
// COMPONENT FACTORIES
// ============================================================================

/**
 * Create a polymorphic component with proper TypeScript support
 */
export function createPolymorphicComponent<DefaultElement extends React.ElementType, Props = Record<string, never>>(
  displayName: string
) {
  type PolymorphicComponent = <T extends React.ElementType = DefaultElement>(
    props: PolymorphicComponentPropsWithRef<T, Props>
  ) => ReactElement | null;

  const Component = React.forwardRef<
    React.ElementRef<DefaultElement>,
    PolymorphicComponentPropsWithRef<DefaultElement, Props>
  >((props, ref) => {
    const { as: Element = 'div' as DefaultElement, ...rest } = props;
    return React.createElement(Element, { ref, ...rest });
  }) as PolymorphicComponent;

  Component.displayName = displayName;

  return Component;
}

/**
 * Create a compound component pattern
 */
export function createCompoundComponent<
  P = Record<string, never>,
  T extends Record<string, ComponentType<unknown>> = Record<string, ComponentType<unknown>>
>(
  MainComponent: ComponentType<P>,
  subComponents: T
): ComponentType<P> & T {
  const CompoundComponent = MainComponent as ComponentType<P> & T;

  Object.keys(subComponents).forEach((key) => {
    (CompoundComponent as ComponentType<P> & T)[key as keyof T] = subComponents[key];
  });

  return CompoundComponent;
}

// ============================================================================
// RENDER PATTERNS
// ============================================================================

/**
 * Render prop pattern type
 */
export type RenderProp<T> = (props: T) => ReactNode;

/**
 * Children as function pattern
 */
export type ChildrenAsFunction<T> = (props: T) => ReactNode;

/**
 * Slot pattern for flexible component composition
 */
export interface SlotProps {
  children?: ReactNode;
  asChild?: boolean;
}

/**
 * Create a slot component that can merge props with child components
 */
export const Slot = React.forwardRef<HTMLElement, SlotProps>(
  ({ children, asChild = false, ...props }, ref) => {
    if (asChild && React.isValidElement(children)) {
      return React.cloneElement(children, {
        ...props,
        ...children.props,
        ref: ref,
      });
    }

    return React.createElement('div', { ref, ...props }, children);
  }
);

Slot.displayName = 'Slot';

// ============================================================================
// VALIDATION UTILITIES
// ============================================================================

/**
 * Type guard to check if a value is a valid React element
 */
export function isValidElement(value: unknown): value is ReactElement {
  return React.isValidElement(value);
}

/**
 * Type guard to check if a value is a function component
 */
export function isFunctionComponent(value: unknown): value is ComponentType<unknown> {
  return typeof value === 'function';
}

/**
 * Validate component props at runtime (development only)
 */
export function validateProps<T extends Record<string, unknown>>(
  props: T,
  validators: Partial<Record<keyof T, (value: unknown) => boolean>>,
  componentName: string
): void {
  if (process.env.NODE_ENV === 'development') {
    Object.entries(validators).forEach(([key, validator]) => {
      if (key in props && validator && !validator(props[key])) {
        logWarning(
          `Invalid prop '${key}' passed to component '${componentName}'. Expected validation to pass.`
        );
      }
    });
  }
}

// ============================================================================
// COMPONENT COMPOSITION UTILITIES
// ============================================================================

/**
 * Compose multiple components together
 */
export function composeComponents<P = Record<string, never>>(
  ...components: ComponentType<P>[]
): ComponentType<P & { children?: React.ReactNode }> {
  const ComposedComponent: ComponentType<P & { children?: React.ReactNode }> = ({ children, ...props }) => {
    return components.reduceRight(
      (acc, Component) => React.createElement(Component, props as P, acc),
      children
    );
  };

  ComposedComponent.displayName = `Composed(${components.length} components)`;

  return ComposedComponent;
}

/**
 * Create a higher-order component with proper TypeScript support
 */
export function withDisplayName<P extends object>(
  Component: ComponentType<P>,
  displayName: string
): ComponentType<P> {
  const WrappedComponent: ComponentType<P> = (props) =>
    React.createElement(Component, props);

  WrappedComponent.displayName = displayName;

  return WrappedComponent;
}

// ============================================================================
// EXPORTS
// ============================================================================

export default {
  createPolymorphicComponent,
  createCompoundComponent,
  composeComponents,
  withDisplayName,
  Slot,
  isValidElement,
  isFunctionComponent,
  validateProps,
};

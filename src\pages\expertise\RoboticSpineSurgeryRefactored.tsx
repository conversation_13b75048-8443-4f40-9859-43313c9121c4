import React, { useEffect } from 'react';
import { Link } from 'react-router-dom';

import PageHeader from '@/components/PageHeader';
import SafeImage from '@/components/SafeImage';
import StandardPageLayout from '@/components/StandardPageLayout';
import { Button } from '@/components/ui/button';
import { roboticSpineSurgeryData } from '@/data/expertise/roboticSpineSurgeryData';

const RoboticSpineSurgeryRefactored: React.FC = () => {
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  const data = roboticSpineSurgeryData;

  return (
    <StandardPageLayout title={data.hero.title} showHeader={false}>
      <PageHeader
        title={data.hero.title}
        subtitle={data.hero.subtitle}
        backgroundImage={data.hero.backgroundImage}
        enableParallax={true}
      />

      <div className="flex-1">
        {/* Main Content */}
        <section className="py-16">
          <div className="container">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
              <div className="lg:col-span-2">
                {/* Overview Section */}
                <h2 className="text-2xl font-bold mb-6">{data.overview.title}</h2>
                {data.overview.content.map((paragraph, index) => (
                  <p key={index} className="text-muted-foreground mb-6">
                    {paragraph}
                  </p>
                ))}

                {/* History Section */}
                <h3 className="text-xl font-semibold mb-3">{data.history.title}</h3>
                {data.history.content.map((paragraph, index) => (
                  <p key={index} className="text-muted-foreground mb-4">
                    {paragraph}
                  </p>
                ))}

                <div className="relative h-80 w-full rounded-lg overflow-hidden shadow-xl mb-8">
                  <SafeImage
                    src={data.images.overview}
                    alt="Robotic Spine Surgery System with Navigation Technology"
                    className="w-full h-full object-cover"
                    fallbackSrc="/images/medical-consulting.jpg"
                  />
                </div>

                {/* How It Works Section */}
                <h2 className="text-2xl font-bold mb-6">{data.howItWorks.title}</h2>
                <p className="text-muted-foreground mb-4">{data.howItWorks.introduction}</p>
                <ol className="list-decimal list-inside text-muted-foreground mb-8 space-y-2">
                  {data.howItWorks.steps.map((step, index) => (
                    <li key={index}>{step}</li>
                  ))}
                </ol>

                {/* How It Works Subsections */}
                {data.howItWorks.sections.map((section, index) => (
                  <div key={index} className="mb-8">
                    <h3 className="text-xl font-semibold mb-3">{section.title}</h3>
                    {section.content.map((paragraph, pIndex) => (
                      <p key={pIndex} className="text-muted-foreground mb-4">
                        {paragraph}
                      </p>
                    ))}
                    {index === 1 && (
                      <div className="relative h-80 w-full rounded-lg overflow-hidden shadow-xl mb-8">
                        <SafeImage
                          src={data.images.robotAttachment}
                          alt="Mazor Renaissance Robotic System for spine surgery"
                          className="w-full h-full object-cover"
                          fallbackSrc="/images/medical-consulting.jpg"
                        />
                      </div>
                    )}
                    {index === 2 && (
                      <div className="relative h-80 w-full rounded-lg overflow-hidden shadow-xl mb-8">
                        <SafeImage
                          src={data.images.procedure}
                          alt="Dr. Aliashkevich performing robotic spine surgery with the Mazor system"
                          className="w-full h-full object-cover"
                          fallbackSrc="/images/medical-consulting.jpg"
                        />
                      </div>
                    )}
                  </div>
                ))}

                {/* Applications Section */}
                <h2 className="text-2xl font-bold mb-6">{data.applications.title}</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                  {data.applications.items.map((item, index) => (
                    <div key={index} className="card p-6 rounded-lg shadow-md bg-card">
                      <h3 className="text-xl font-semibold mb-3 text-primary">{item.title}</h3>
                      <p className="text-muted-foreground">{item.description}</p>
                    </div>
                  ))}
                </div>

                {/* Neuromonitoring Section */}
                <h2 className="text-2xl font-bold mb-6">{data.neuromonitoring.title}</h2>
                {data.neuromonitoring.content.map((paragraph, index) => (
                  <p key={index} className="text-muted-foreground mb-4">
                    {paragraph.includes('Dr Aliashkevich') ? (
                      <>
                        {paragraph.split('Dr Aliashkevich')[0]}
                        <a href="https://mpscentre.com.au/dtTeam/dr-ales-aliashkevich/" className="text-primary hover:underline" target="_blank" rel="noopener noreferrer">Dr Aliashkevich</a>
                        {paragraph.split('Dr Aliashkevich')[1]}
                      </>
                    ) : paragraph}
                  </p>
                ))}

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                  <ul className="list-disc list-inside text-muted-foreground space-y-1">
                    {data.neuromonitoring.procedures.column1.map((item, index) => (
                      <li key={index}>{item}</li>
                    ))}
                  </ul>
                  <ul className="list-disc list-inside text-muted-foreground space-y-1">
                    {data.neuromonitoring.procedures.column2.map((item, index) => (
                      <li key={index}>{item}</li>
                    ))}
                  </ul>
                </div>

                <div className="relative h-80 w-full rounded-lg overflow-hidden shadow-xl mb-8">
                  <SafeImage
                    src={data.images.neuromonitoring}
                    alt="Neuromonitoring in Spine Surgery - EMG, MEP, and SSEP monitoring"
                    className="w-full h-full object-cover"
                    fallbackSrc="/images/medical-consulting.jpg"
                  />
                </div>

                {/* Benefits Section */}
                <h2 className="text-2xl font-bold mb-6">{data.benefits.title}</h2>
                {data.benefits.content.map((paragraph, index) => (
                  <p key={index} className="text-muted-foreground mb-4">
                    {paragraph}
                  </p>
                ))}
                <ul className="list-disc list-inside text-muted-foreground mb-8 space-y-2">
                  {data.benefits.list.map((benefit, index) => (
                    <li key={index}>{benefit}</li>
                  ))}
                </ul>

                {/* Conditions Section */}
                <h2 className="text-2xl font-bold mb-6">{data.conditions.title}</h2>
                <p className="text-muted-foreground mb-4">
                  <a href="https://mpscentre.com.au/dtTeam/dr-ales-aliashkevich/" className="text-primary hover:underline" target="_blank" rel="noopener noreferrer">Dr Aliashkevich</a> {data.conditions.introduction.replace('Dr Aliashkevich ', '')}
                </p>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                  {data.conditions.items.map((condition, index) => (
                    <div key={index} className="card p-6 rounded-lg shadow-md bg-card">
                      <h3 className="text-xl font-semibold mb-3 text-primary">{condition.title}</h3>
                      <p className="text-muted-foreground">{condition.description}</p>
                    </div>
                  ))}
                </div>

                {/* Why Choose Section */}
                <h2 className="text-2xl font-bold mb-6">{data.whyChoose.title}</h2>
                <p className="text-muted-foreground mb-4">
                  <a href="https://mpscentre.com.au/dtTeam/dr-ales-aliashkevich/" className="text-primary hover:underline" target="_blank" rel="noopener noreferrer">Dr Aliashkevich</a> {data.whyChoose.introduction.replace('Dr Aliashkevich ', '')}
                </p>
                <ul className="list-disc list-inside text-muted-foreground mb-8 space-y-2">
                  {data.whyChoose.points.map((point, index) => (
                    <li key={index}>{point}</li>
                  ))}
                </ul>

                <div className="relative h-80 w-full rounded-lg overflow-hidden shadow-xl mb-8">
                  <SafeImage
                    src={data.images.results}
                    alt="Successful robotic spine surgery with precise screw placement"
                    className="w-full h-full object-cover"
                    fallbackSrc="/images/medical-consulting.jpg"
                  />
                </div>
              </div>

              {/* Sidebar */}
              <div className="lg:col-span-1">
                <div className="sticky top-24 space-y-6">
                  {/* Candidacy Card */}
                  <div className="card p-6 rounded-lg shadow-md bg-card">
                    <h3 className="text-xl font-semibold mb-3 text-primary">{data.sidebar.candidacy.title}</h3>
                    <p className="text-muted-foreground mb-4">{data.sidebar.candidacy.introduction}</p>
                    <ul className="list-disc list-inside text-muted-foreground space-y-2 mb-4">
                      {data.sidebar.candidacy.criteria.map((criterion, index) => (
                        <li key={index}>{criterion}</li>
                      ))}
                    </ul>
                    <p className="text-muted-foreground mb-4">
                      <a href="https://mpscentre.com.au/dtTeam/dr-ales-aliashkevich/" className="text-primary hover:underline" target="_blank" rel="noopener noreferrer">Dr Aliashkevich</a> {data.sidebar.candidacy.note.replace('Dr Aliashkevich ', '')}
                    </p>
                    <Button asChild className="w-full">
                      <Link to="/appointments">Schedule a Consultation</Link>
                    </Button>
                  </div>

                  {/* Related Procedures Card */}
                  <div className="card p-6 rounded-lg shadow-md bg-card">
                    <h3 className="text-xl font-semibold mb-3 text-primary">{data.sidebar.relatedProcedures.title}</h3>
                    <ul className="space-y-2">
                      {data.sidebar.relatedProcedures.items.map((item, index) => (
                        <li key={index}>
                          <Link to={item.link} className="text-primary hover:underline">
                            {item.title}
                          </Link>
                        </li>
                      ))}
                    </ul>
                  </div>

                  {/* Patient Resources Card */}
                  <div className="card p-6 rounded-lg shadow-md bg-card">
                    <h3 className="text-xl font-semibold mb-3 text-primary">{data.sidebar.patientResources.title}</h3>
                    <ul className="space-y-2">
                      {data.sidebar.patientResources.items.map((item, index) => (
                        <li key={index}>
                          <Link to={item.link} className="text-primary hover:underline">
                            {item.title}
                          </Link>
                        </li>
                      ))}
                    </ul>
                  </div>

                  {/* Robotic Systems Card */}
                  <div className="card p-6 rounded-lg shadow-md bg-card">
                    <h3 className="text-xl font-semibold mb-3 text-primary">{data.sidebar.roboticSystems.title}</h3>
                    <p className="text-muted-foreground mb-4">{data.sidebar.roboticSystems.introduction}</p>
                    <ul className="list-disc list-inside text-muted-foreground space-y-2">
                      {data.sidebar.roboticSystems.systems.map((system, index) => (
                        <li key={index}>{system}</li>
                      ))}
                    </ul>
                    <p className="text-muted-foreground mt-4">{data.sidebar.roboticSystems.note}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Call to Action */}
        <section className="py-16 bg-primary/5">
          <div className="container">
            <div className="text-center max-w-3xl mx-auto">
              <h2 className="text-3xl font-bold mb-6">{data.cta.title}</h2>
              <p className="text-muted-foreground mb-8">
                Contact us today to schedule a consultation with{' '}
                <a href="http://www.neurosurgeon.au" className="text-primary hover:underline" target="_blank" rel="noopener noreferrer">
                  Dr Ales Aliashkevich
                </a>{' '}
                to learn more about how robotic spine surgery might benefit your specific condition.
              </p>
              <div className="flex flex-col sm:flex-row justify-center gap-4">
                <Button asChild size="lg">
                  <Link to="/appointments">Book an Appointment</Link>
                </Button>
                <Button asChild variant="outline" size="lg">
                  <Link to="/contact">Contact Us</Link>
                </Button>
              </div>
            </div>
          </div>
        </section>
      </div>
    </StandardPageLayout>
  );
};

RoboticSpineSurgeryRefactored.displayName = 'RoboticSpineSurgeryRefactored';

export default RoboticSpineSurgeryRefactored;


// TODO: Extract inline object types to interfaces
// Consider creating interfaces for complex object types
import React from 'react';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ConsultationFee, InsuranceOption } from '@/data/appointments/feesData';

interface FeesSectionProps {
  title: string;
  subtitle: string;
  consultationFees: ConsultationFee[];
  insuranceOptions: InsuranceOption[];
  labels: {
    fee: string;
    medicareRebate: string;
    outOfPocketCost: string;
    insuranceOptionsTitle: string;
  };
}

const FeesSection: React.FC<FeesSectionProps> = ({ 
  title, 
  subtitle, 
  consultationFees, 
  insuranceOptions, 
  labels 
}) => {
  return (
    <section className="py-16">
      <div className="container max-w-7xl">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold mb-4 text-foreground">{title}</h2>
          <p className="text-muted-foreground max-w-2xl mx-auto">{subtitle}</p>
        </div>

        {/* Consultation Fees */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
          {consultationFees.map((fee, index) => (
            <Card 
              key={fee.id}
              className="bg-card/50 backdrop-blur-sm border border-border/50 shadow-md hover:shadow-lg transition-all duration-300 animate-fade-in"
              style={{ animationDelay: `${index * 100}ms` }}
            >
              <CardHeader className="pb-4">
                <CardTitle className="text-xl text-primary">{fee.title}</CardTitle>
                <p className="text-muted-foreground text-sm">{fee.description}</p>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-foreground font-medium">{labels.fee}</span>
                    <span className="text-lg font-bold text-foreground">{fee.fee}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-muted-foreground">{labels.medicareRebate}</span>
                    <span className="text-green-600 font-semibold">{fee.rebate}</span>
                  </div>
                  <div className="flex justify-between items-center border-t border-border pt-3">
                    <span className="text-foreground font-medium">{labels.outOfPocketCost}</span>
                    <span className="text-xl font-bold text-primary">{fee.outOfPocket}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Insurance Options */}
        <div>
          <h3 className="text-2xl font-bold mb-6 text-center text-foreground">{labels.insuranceOptionsTitle}</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {insuranceOptions.map((option, index) => (
              <Card 
                key={option.id}
                className="bg-card/50 backdrop-blur-sm border border-border/50 shadow-md hover:shadow-lg transition-all duration-300 animate-fade-in"
                style={{ animationDelay: `${(consultationFees.length + index) * 100}ms` }}
              >
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg text-primary">{option.title}</CardTitle>
                </CardHeader>
                <CardContent className="pt-0">
                  <p className="text-muted-foreground text-sm leading-relaxed">{option.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

FeesSection.displayName = 'FeesSection';

export default FeesSection;

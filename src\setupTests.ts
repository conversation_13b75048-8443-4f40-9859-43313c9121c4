import '@testing-library/jest-dom';
import { cleanup } from '@testing-library/react';
import { toHaveNoViolations } from 'jest-axe';
import { afterAll, afterEach, beforeAll, expect, vi } from 'vitest';

import { setupTestEnvironment } from '@/lib/test-utils';

// Extend Vitest's expect with jest-dom and jest-axe matchers
expect.extend(toHaveNoViolations);

// Setup test environment with all mocks and utilities
setupTestEnvironment();

// Cleanup after each test case (e.g. clearing jsdom)
afterEach(() => {
  cleanup();
});

// Global test setup
beforeAll(() => {
  // Console methods are handled by test-utils for specific tests
  // No global console suppression to avoid conflicts

  // Suppress React act() warnings for third-party components
  // eslint-disable-next-line no-console
  const originalWarn = console.warn;
  // eslint-disable-next-line no-console
  console.warn = (...args) => {
    if (
      typeof args[0] === 'string' &&
      args[0].includes('Warning: An update to') &&
      args[0].includes('was not wrapped in act')
    ) {
      return;
    }
    originalWarn.call(console, ...args);
  };
});

afterAll(() => {
  // Cleanup any global mocks or resources
  vi.restoreAllMocks();
});

// Mock environment variables for tests
Object.defineProperty(process, 'env', {
  value: {
    ...process.env,
    NODE_ENV: 'test',
    VITE_APP_TITLE: 'miNEURO Test',
    VITE_APP_DESCRIPTION: 'Test Environment',
  },
});

// Restore proper global mocks to prevent conflicts
// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: (query: string) => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: () => { },
    removeListener: () => { },
    addEventListener: () => { },
    removeEventListener: () => { },
    dispatchEvent: () => { },
  }),
});

// Mock IntersectionObserver
global.IntersectionObserver = class IntersectionObserver {
  constructor() { }
  observe() {
    return null;
  }
  disconnect() {
    return null;
  }
  unobserve() {
    return null;
  }
};

// Mock ResizeObserver
global.ResizeObserver = class ResizeObserver {
  constructor() { }
  observe() {
    return null;
  }
  disconnect() {
    return null;
  }
  unobserve() {
    return null;
  }
};

// Mock scrollTo
Object.defineProperty(window, 'scrollTo', {
  value: vi.fn(),
  writable: true,
});

// Mock performance API with mockable functions
Object.defineProperty(global, 'performance', {
  value: {
    now: vi.fn(() => Date.now()),
    mark: vi.fn(),
    measure: vi.fn(),
    getEntriesByType: vi.fn(() => []),
    getEntriesByName: vi.fn(() => []),
    clearMarks: vi.fn(),
    clearMeasures: vi.fn(),
  },
  writable: true,
});

// Mock URL constructor for tests
global.URL = class URL {
  constructor(url: string) {
    this.href = url;
    this.origin = 'http://localhost:3000';
    this.pathname = '/';
    this.search = '';
    this.hash = '';
  }
  href: string;
  origin: string;
  pathname: string;
  search: string;
  hash: string;
};

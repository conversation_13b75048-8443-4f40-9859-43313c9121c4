
import { logWarning } from '@/lib/dev-console';

/**
 * Content Validation Utilities
 * Provides robust validation and fallback mechanisms for content
 */

export type ValidationResult<T> = {
  isValid: boolean;
  data: T | null;
  error?: string;
  fallback?: T;
};

export type ContentType = 'string' | 'array' | 'object' | 'number' | 'boolean';

/**
 * Validates if content exists and is not empty
 */
export function validateContent<T>(
  content: T,
  type: ContentType,
  options: {
    allowEmpty?: boolean;
    minLength?: number;
    maxLength?: number;
    required?: boolean;
    fallback?: T;
  } = {}
): ValidationResult<T> {
  const { allowEmpty = false, minLength = 0, maxLength, required = false, fallback } = options;

  // Check if content exists
  if (content === null || content === undefined) {
    return {
      isValid: !required,
      data: null,
      error: required ? 'Content is required but not provided' : undefined,
      fallback
    };
  }

  // Type-specific validation
  switch (type) {
    case 'string': {
      const str = content as string;
      if (typeof str !== 'string') {
        return {
          isValid: false,
          data: null,
          error: 'Expected string but received ' + typeof str,
          fallback
        };
      }

      if (!allowEmpty && str.trim().length === 0) {
        return {
          isValid: false,
          data: null,
          error: 'String is empty',
          fallback
        };
      }

      if (str.length < minLength) {
        return {
          isValid: false,
          data: null,
          error: `String length ${str.length} is below minimum ${minLength}`,
          fallback
        };
      }

      if (maxLength && str.length > maxLength) {
        return {
          isValid: false,
          data: null,
          error: `String length ${str.length} exceeds maximum ${maxLength}`,
          fallback
        };
      }

      return { isValid: true, data: content };
    }

    case 'array': {
      const arr = content as unknown[];
      if (!Array.isArray(arr)) {
        return {
          isValid: false,
          data: null,
          error: 'Expected array but received ' + typeof arr,
          fallback
        };
      }

      if (!allowEmpty && arr.length === 0) {
        return {
          isValid: false,
          data: null,
          error: 'Array is empty',
          fallback
        };
      }

      if (arr.length < minLength) {
        return {
          isValid: false,
          data: null,
          error: `Array length ${arr.length} is below minimum ${minLength}`,
          fallback
        };
      }

      if (maxLength && arr.length > maxLength) {
        return {
          isValid: false,
          data: null,
          error: `Array length ${arr.length} exceeds maximum ${maxLength}`,
          fallback
        };
      }

      return { isValid: true, data: content };
    }

    case 'object': {
      const obj = content as Record<string, unknown>;
      if (typeof obj !== 'object' || Array.isArray(obj)) {
        return {
          isValid: false,
          data: null,
          error: 'Expected object but received ' + typeof obj,
          fallback
        };
      }

      if (!allowEmpty && Object.keys(obj).length === 0) {
        return {
          isValid: false,
          data: null,
          error: 'Object is empty',
          fallback
        };
      }

      return { isValid: true, data: content };
    }

    case 'number': {
      const num = content as number;
      if (typeof num !== 'number' || isNaN(num)) {
        return {
          isValid: false,
          data: null,
          error: 'Expected number but received ' + typeof num,
          fallback
        };
      }

      return { isValid: true, data: content };
    }

    case 'boolean': {
      const bool = content as boolean;
      if (typeof bool !== 'boolean') {
        return {
          isValid: false,
          data: null,
          error: 'Expected boolean but received ' + typeof bool,
          fallback
        };
      }

      return { isValid: true, data: content };
    }

    default:
      return {
        isValid: false,
        data: null,
        error: 'Unknown content type: ' + type,
        fallback
      };
  }
}

/**
 * Safely access nested object properties with validation
 */
export function safeGet<T>(
  obj: Record<string, unknown>,
  path: string,
  fallback?: T
): T | undefined {
  try {
    const keys = path.split('.');
    let current: unknown = obj;

    for (const key of keys) {
      if (current === null || current === undefined) {
        return fallback;
      }

      if (typeof current === 'object' && !Array.isArray(current)) {
        current = (current as Record<string, unknown>)[key];
      } else {
        return fallback;
      }
    }

    return current as T || fallback;
  } catch {
    return fallback;
  }
}

/**
 * Validates array content and provides fallbacks
 */
export function validateArray<T>(
  array: T[] | null | undefined,
  options: {
    minLength?: number;
    maxLength?: number;
    fallback?: T[];
    itemValidator?: (item: T) => boolean;
  } = {}
): ValidationResult<T[]> {
  const { minLength = 0, maxLength, fallback = [], itemValidator } = options;

  if (!Array.isArray(array)) {
    return {
      isValid: false,
      data: null,
      error: 'Expected array but received ' + typeof array,
      fallback
    };
  }

  if (array.length < minLength) {
    return {
      isValid: false,
      data: null,
      error: `Array length ${array.length} is below minimum ${minLength}`,
      fallback
    };
  }

  if (maxLength && array.length > maxLength) {
    return {
      isValid: false,
      data: null,
      error: `Array length ${array.length} exceeds maximum ${maxLength}`,
      fallback
    };
  }

  // Validate individual items if validator provided
  if (itemValidator) {
    const validItems = array.filter(itemValidator);
    if (validItems.length !== array.length) {
      return {
        isValid: false,
        data: validItems,
        error: `${array.length - validItems.length} invalid items found`,
        fallback
      };
    }
  }

  return { isValid: true, data: array };
}

/**
 * Validates string content with common patterns
 */
export function validateString(
  str: string | null | undefined,
  options: {
    minLength?: number;
    maxLength?: number;
    pattern?: RegExp;
    allowEmpty?: boolean;
    fallback?: string;
    trim?: boolean;
  } = {}
): ValidationResult<string> {
  const { minLength = 0, maxLength, pattern, allowEmpty = false, fallback = '', trim = true } = options;

  if (typeof str !== 'string') {
    return {
      isValid: false,
      data: null,
      error: 'Expected string but received ' + typeof str,
      fallback
    };
  }

  const processedStr = trim ? str.trim() : str;

  if (!allowEmpty && processedStr.length === 0) {
    return {
      isValid: false,
      data: null,
      error: 'String is empty',
      fallback
    };
  }

  if (processedStr.length < minLength) {
    return {
      isValid: false,
      data: null,
      error: `String length ${processedStr.length} is below minimum ${minLength}`,
      fallback
    };
  }

  if (maxLength && processedStr.length > maxLength) {
    return {
      isValid: false,
      data: null,
      error: `String length ${processedStr.length} exceeds maximum ${maxLength}`,
      fallback
    };
  }

  if (pattern && !pattern.test(processedStr)) {
    return {
      isValid: false,
      data: null,
      error: 'String does not match required pattern',
      fallback
    };
  }

  return { isValid: true, data: processedStr };
}

/**
 * Creates a safe content accessor with validation
 */
export function createContentAccessor<T>(
  data: T | null | undefined,
  fallback: T
): T {
  if (data === null || data === undefined) {
    return fallback;
  }
  return data;
}

/**
 * Validates translation content
 */
export function validateTranslation(
  translation: string | null | undefined,
  key: string,
  fallback?: string
): string {
  if (typeof translation !== 'string' || translation.trim().length === 0) {
    logWarning(`Missing translation for key: ${key}`);
    return fallback || key.split('.').pop() || 'Missing Translation';
  }

  // Check for placeholder patterns
  if (translation.includes('[翻译缺失:') ||
    translation.includes('[需要翻译:') ||
    translation.includes('Translation needed')) {
    logWarning(`Placeholder translation found for key: ${key}`);
    return fallback || key.split('.').pop() || 'Translation Needed';
  }

  return translation;
}

/**
 * Validates medical data structures
 */
export interface MedicalCondition {
  id: string;
  name: string;
  description?: string;
  symptoms?: string[];
  treatments?: string[];
}

export interface MedicalProcedure {
  id: string;
  name: string;
  description?: string;
  duration?: string;
  recovery?: string;
}

export function validateMedicalCondition(
  condition: unknown
): ValidationResult<MedicalCondition> {
  if (!condition || typeof condition !== 'object') {
    return {
      isValid: false,
      data: null,
      error: 'Invalid condition object'
    };
  }

  const cond = condition as Record<string, unknown>;

  if (!cond.id || typeof cond.id !== 'string') {
    return {
      isValid: false,
      data: null,
      error: 'Condition missing valid ID'
    };
  }

  if (!cond.name || typeof cond.name !== 'string') {
    return {
      isValid: false,
      data: null,
      error: 'Condition missing valid name'
    };
  }

  return {
    isValid: true,
    data: condition as MedicalCondition
  };
}

export function validateMedicalProcedure(
  procedure: unknown
): ValidationResult<MedicalProcedure> {
  if (!procedure || typeof procedure !== 'object') {
    return {
      isValid: false,
      data: null,
      error: 'Invalid procedure object'
    };
  }

  const proc = procedure as Record<string, unknown>;

  if (!proc.id || typeof proc.id !== 'string') {
    return {
      isValid: false,
      data: null,
      error: 'Procedure missing valid ID'
    };
  }

  if (!proc.name || typeof proc.name !== 'string') {
    return {
      isValid: false,
      data: null,
      error: 'Procedure missing valid name'
    };
  }

  return {
    isValid: true,
    data: procedure as MedicalProcedure
  };
}

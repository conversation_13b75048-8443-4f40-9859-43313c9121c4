import { describe, it, expect } from 'vitest';

import {
  validateContent,
  validateArray,
  validateString,
  safeGet,
  createContentAccessor,
  validateTranslation,
  validateMedicalCondition,
  validateMedicalProcedure
} from '@/lib/content-validation';

describe('validateContent', () => {
  describe('String validation', () => {
    it('validates valid string', () => {
      const result = validateContent('test string', 'string');
      
      expect(result.isValid).toBe(true);
      expect(result.data).toBe('test string');
      expect(result.error).toBeUndefined();
    });

    it('rejects empty string when not allowed', () => {
      const result = validateContent('', 'string', { allowEmpty: false });
      
      expect(result.isValid).toBe(false);
      expect(result.data).toBe(null);
      expect(result.error).toBe('String is empty');
    });

    it('accepts empty string when allowed', () => {
      const result = validateContent('', 'string', { allowEmpty: true });
      
      expect(result.isValid).toBe(true);
      expect(result.data).toBe('');
    });

    it('validates string length constraints', () => {
      const shortResult = validateContent('hi', 'string', { minLength: 5 });
      expect(shortResult.isValid).toBe(false);
      expect(shortResult.error).toContain('below minimum');

      const longResult = validateContent('very long string', 'string', { maxLength: 5 });
      expect(longResult.isValid).toBe(false);
      expect(longResult.error).toContain('exceeds maximum');
    });

    it('rejects non-string values', () => {
      const result = validateContent(123, 'string');
      
      expect(result.isValid).toBe(false);
      expect(result.data).toBe(null);
      expect(result.error).toContain('Expected string but received number');
    });
  });

  describe('Array validation', () => {
    it('validates valid array', () => {
      const result = validateContent([1, 2, 3], 'array');
      
      expect(result.isValid).toBe(true);
      expect(result.data).toEqual([1, 2, 3]);
    });

    it('rejects empty array when not allowed', () => {
      const result = validateContent([], 'array', { allowEmpty: false });
      
      expect(result.isValid).toBe(false);
      expect(result.data).toBe(null);
      expect(result.error).toBe('Array is empty');
    });

    it('validates array length constraints', () => {
      const shortResult = validateContent([1], 'array', { minLength: 3 });
      expect(shortResult.isValid).toBe(false);
      expect(shortResult.error).toContain('below minimum');

      const longResult = validateContent([1, 2, 3, 4, 5], 'array', { maxLength: 3 });
      expect(longResult.isValid).toBe(false);
      expect(longResult.error).toContain('exceeds maximum');
    });

    it('rejects non-array values', () => {
      const result = validateContent('not array', 'array');
      
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('Expected array but received string');
    });
  });

  describe('Object validation', () => {
    it('validates valid object', () => {
      const obj = { key: 'value' };
      const result = validateContent(obj, 'object');
      
      expect(result.isValid).toBe(true);
      expect(result.data).toEqual(obj);
    });

    it('rejects empty object when not allowed', () => {
      const result = validateContent({}, 'object', { allowEmpty: false });
      
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('Object is empty');
    });

    it('rejects arrays as objects', () => {
      const result = validateContent([1, 2, 3], 'object');
      
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('Expected object but received object');
    });
  });

  describe('Number validation', () => {
    it('validates valid number', () => {
      const result = validateContent(42, 'number');
      
      expect(result.isValid).toBe(true);
      expect(result.data).toBe(42);
    });

    it('rejects NaN', () => {
      const result = validateContent(NaN, 'number');
      
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('Expected number but received number');
    });

    it('rejects non-number values', () => {
      const result = validateContent('42', 'number');
      
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('Expected number but received string');
    });
  });

  describe('Boolean validation', () => {
    it('validates true', () => {
      const result = validateContent(true, 'boolean');
      
      expect(result.isValid).toBe(true);
      expect(result.data).toBe(true);
    });

    it('validates false', () => {
      const result = validateContent(false, 'boolean');
      
      expect(result.isValid).toBe(true);
      expect(result.data).toBe(false);
    });

    it('rejects non-boolean values', () => {
      const result = validateContent('true', 'boolean');
      
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('Expected boolean but received string');
    });
  });

  describe('Null/undefined handling', () => {
    it('handles null when not required', () => {
      const result = validateContent(null, 'string', { required: false });
      
      expect(result.isValid).toBe(true);
      expect(result.data).toBe(null);
    });

    it('rejects null when required', () => {
      const result = validateContent(null, 'string', { required: true });
      
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('Content is required but not provided');
    });

    it('provides fallback for invalid content', () => {
      const fallback = 'fallback value';
      const result = validateContent(null, 'string', { required: true, fallback });
      
      expect(result.isValid).toBe(false);
      expect(result.fallback).toBe(fallback);
    });
  });
});

describe('validateArray', () => {
  it('validates valid array with item validator', () => {
    const isNumber = (item: unknown): item is number => typeof item === 'number';
    const result = validateArray([1, 2, 3], { itemValidator: isNumber });
    
    expect(result.isValid).toBe(true);
    expect(result.data).toEqual([1, 2, 3]);
  });

  it('filters invalid items', () => {
    const isNumber = (item: unknown): item is number => typeof item === 'number';
    const result = validateArray([1, 'invalid', 3], { itemValidator: isNumber });
    
    expect(result.isValid).toBe(false);
    expect(result.data).toEqual([1, 3]);
    expect(result.error).toContain('1 invalid items found');
  });

  it('handles null array', () => {
    const result = validateArray(null, { fallback: [] });
    
    expect(result.isValid).toBe(false);
    expect(result.fallback).toEqual([]);
  });

  it('validates array length', () => {
    const result = validateArray([1], { minLength: 3, fallback: [] });
    
    expect(result.isValid).toBe(false);
    expect(result.error).toContain('below minimum');
  });
});

describe('validateString', () => {
  it('validates and trims string', () => {
    const result = validateString('  test  ', { trim: true });
    
    expect(result.isValid).toBe(true);
    expect(result.data).toBe('test');
  });

  it('validates pattern matching', () => {
    const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const validResult = validateString('<EMAIL>', { pattern: emailPattern });
    const invalidResult = validateString('invalid-email', { pattern: emailPattern });
    
    expect(validResult.isValid).toBe(true);
    expect(invalidResult.isValid).toBe(false);
    expect(invalidResult.error).toBe('String does not match required pattern');
  });

  it('handles null string with fallback', () => {
    const result = validateString(null, { fallback: 'default' });
    
    expect(result.isValid).toBe(false);
    expect(result.fallback).toBe('default');
  });
});

describe('safeGet', () => {
  const testObj = {
    user: {
      profile: {
        name: 'John Doe',
        age: 30
      },
      settings: {
        theme: 'dark'
      }
    },
    items: [1, 2, 3]
  };

  it('gets nested property safely', () => {
    const result = safeGet(testObj, 'user.profile.name');
    expect(result).toBe('John Doe');
  });

  it('returns fallback for missing property', () => {
    const result = safeGet(testObj, 'user.profile.email', 'no-email');
    expect(result).toBe('no-email');
  });

  it('returns fallback for null intermediate value', () => {
    const objWithNull = { user: null };
    const result = safeGet(objWithNull, 'user.profile.name', 'fallback');
    expect(result).toBe('fallback');
  });

  it('handles invalid path gracefully', () => {
    const result = safeGet(testObj, 'invalid.path.here', 'fallback');
    expect(result).toBe('fallback');
  });
});

describe('createContentAccessor', () => {
  it('returns data when valid', () => {
    const result = createContentAccessor('valid data', 'fallback');
    expect(result).toBe('valid data');
  });

  it('returns fallback when data is null', () => {
    const result = createContentAccessor(null, 'fallback');
    expect(result).toBe('fallback');
  });

  it('returns fallback when data is undefined', () => {
    const result = createContentAccessor(undefined, 'fallback');
    expect(result).toBe('fallback');
  });
});

describe('validateTranslation', () => {
  it('returns valid translation', () => {
    const result = validateTranslation('Valid translation', 'test.key');
    expect(result).toBe('Valid translation');
  });

  it('returns fallback for empty translation', () => {
    const result = validateTranslation('', 'test.key', 'fallback');
    expect(result).toBe('fallback');
  });

  it('returns fallback for null translation', () => {
    const result = validateTranslation(null, 'test.key', 'fallback');
    expect(result).toBe('fallback');
  });

  it('detects placeholder translations', () => {
    const result = validateTranslation('[翻译缺失: test]', 'test.key', 'fallback');
    expect(result).toBe('fallback');
  });

  it('extracts key name when no fallback provided', () => {
    const result = validateTranslation('', 'section.subsection.keyName');
    expect(result).toBe('keyName');
  });
});

describe('validateMedicalCondition', () => {
  it('validates valid medical condition', () => {
    const condition = {
      id: 'condition-1',
      name: 'Test Condition',
      description: 'Test description',
      symptoms: ['symptom1', 'symptom2']
    };
    
    const result = validateMedicalCondition(condition);
    
    expect(result.isValid).toBe(true);
    expect(result.data).toEqual(condition);
  });

  it('rejects condition without id', () => {
    const condition = {
      name: 'Test Condition'
    };
    
    const result = validateMedicalCondition(condition);
    
    expect(result.isValid).toBe(false);
    expect(result.error).toBe('Condition missing valid ID');
  });

  it('rejects condition without name', () => {
    const condition = {
      id: 'condition-1'
    };
    
    const result = validateMedicalCondition(condition);
    
    expect(result.isValid).toBe(false);
    expect(result.error).toBe('Condition missing valid name');
  });

  it('rejects non-object input', () => {
    const result = validateMedicalCondition('not an object');
    
    expect(result.isValid).toBe(false);
    expect(result.error).toBe('Invalid condition object');
  });
});

describe('validateMedicalProcedure', () => {
  it('validates valid medical procedure', () => {
    const procedure = {
      id: 'procedure-1',
      name: 'Test Procedure',
      description: 'Test description',
      duration: '2 hours'
    };
    
    const result = validateMedicalProcedure(procedure);
    
    expect(result.isValid).toBe(true);
    expect(result.data).toEqual(procedure);
  });

  it('rejects procedure without id', () => {
    const procedure = {
      name: 'Test Procedure'
    };
    
    const result = validateMedicalProcedure(procedure);
    
    expect(result.isValid).toBe(false);
    expect(result.error).toBe('Procedure missing valid ID');
  });

  it('rejects procedure without name', () => {
    const procedure = {
      id: 'procedure-1'
    };
    
    const result = validateMedicalProcedure(procedure);
    
    expect(result.isValid).toBe(false);
    expect(result.error).toBe('Procedure missing valid name');
  });

  it('rejects non-object input', () => {
    const result = validateMedicalProcedure(null);
    
    expect(result.isValid).toBe(false);
    expect(result.error).toBe('Invalid procedure object');
  });
});

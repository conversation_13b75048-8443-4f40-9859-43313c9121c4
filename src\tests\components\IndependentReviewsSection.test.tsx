import { render, screen } from '@testing-library/react';
import { describe, it, expect } from 'vitest';

import IndependentReviewsSection from '@/components/IndependentReviewsSection';
import { TestWrapper } from '@/lib/test-utils';
import { setupAllStandardMocks } from '@/tests/utils/standard-mocks';

// Setup standardized mocks
setupAllStandardMocks();

describe('IndependentReviewsSection', () => {
  it('renders review platforms and patient testimonials', () => {
    render(
      <TestWrapper>
        <IndependentReviewsSection />
      </TestWrapper>
    );

    // Check for main heading
    expect(screen.getByRole('heading', { name: /independent patient reviews/i })).toBeInTheDocument();

    // Check for review platforms (should be immediately available)
    expect(screen.getByText('View on Google')).toBeInTheDocument();
    expect(screen.getByText('View on BirdEye')).toBeInTheDocument();
    expect(screen.getByText('View on RateMDs')).toBeInTheDocument();
    expect(screen.getByText('View on TrustIndex')).toBeInTheDocument();
    expect(screen.getByText('View on ThreeBestRated')).toBeInTheDocument();

    // Check for patient testimonials section
    expect(screen.getByText('What Our Patients Say')).toBeInTheDocument();

    // Check for testimonial content
    expect(screen.getByText(/Dr. Aliashkevich's expertise in minimally invasive spine surgery/)).toBeInTheDocument();
    expect(screen.getByText(/The cervical disc replacement procedure was truly life-changing/)).toBeInTheDocument();

    // Check for patient names
    expect(screen.getByText('— Sarah T., Melbourne')).toBeInTheDocument();
    expect(screen.getByText('— Michael C., Sydney')).toBeInTheDocument();

    // Check for "Read More" button
    expect(screen.getByRole('link', { name: /read more patient reviews/i })).toBeInTheDocument();
  });

  it('displays ratings for each platform', () => {
    render(
      <TestWrapper>
        <IndependentReviewsSection />
      </TestWrapper>
    );

    // Check that ratings are displayed (should be immediately available)
    expect(screen.getAllByText('4.8')).toHaveLength(2); // Google and BirdEye both have 4.8
    expect(screen.getByText('4.9')).toBeInTheDocument(); // TrustIndex

    // Check for the "/ 5.0" text that appears in the rating display
    expect(screen.getAllByText('/ 5.0')).toHaveLength(5); // All platforms show "/ 5.0"
  });

  it('has proper accessibility attributes', async () => {
    render(
      <TestWrapper>
        <IndependentReviewsSection />
      </TestWrapper>
    );

    // Check for section with proper ARIA labeling
    const section = screen.getByRole('region', { name: /independent patient reviews/i });
    expect(section).toBeInTheDocument();

    // Check for proper heading structure
    const mainHeading = screen.getByRole('heading', { level: 2, name: /independent patient reviews/i });
    expect(mainHeading).toBeInTheDocument();

    const subHeading = screen.getByRole('heading', { level: 3, name: /what our patients say/i });
    expect(subHeading).toBeInTheDocument();
  });
});
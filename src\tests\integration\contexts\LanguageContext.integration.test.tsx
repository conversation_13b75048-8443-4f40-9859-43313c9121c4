import { render, screen } from '@testing-library/react';
import { toHaveNoViolations } from 'jest-axe';
import React from 'react';
import { vi, describe, it, expect, beforeEach } from 'vitest';

import ErrorBoundary from '@/components/ErrorBoundary';
import { mockUtils } from '@/lib/test-utils';
import { setupAllStandardMocks } from '@/tests/utils/standard-mocks';

// Add jest-axe matchers
expect.extend(toHaveNoViolations);

// Setup standardized mocks
setupAllStandardMocks();

// Create a proper React Context for testing that matches the real implementation
const MockLanguageContext = React.createContext<{
  language: string;
  t: Record<string, unknown>;
  isLanguageLoaded: boolean;
} | null>(null);

// Create a comprehensive LanguageProvider mock for testing
const MockLanguageProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const language = 'en'; // Only English supported
  const isLanguageLoaded = true;

  // Mock translation object structure that matches the real implementation
  const t = {
    nav: {
      home: 'Home',
      expertise: 'Expertise',
      appointments: 'Appointments',
      contact: 'Contact',
      menu: 'Menu'
    },
    home: {
      welcome: {
        title: 'Welcome'
      }
    },
    footer: {
      description: 'Professional medical practice',
      copyright: 'Copyright'
    },
    test: {
      key: 'Test Value'
    },
    header: {
      title: 'Header Title'
    },
    content: {
      welcome: 'Welcome'
    }
  };

  const contextValue = {
    language,
    t,
    isLanguageLoaded
  };

  return (
    <MockLanguageContext.Provider value={contextValue}>
      <div data-testid="mock-language-provider">
        {children}
      </div>
    </MockLanguageContext.Provider>
  );
};

const useLanguage = () => {
  const context = React.useContext(MockLanguageContext);
  if (!context) {
    throw new Error('useLanguage must be used within MockLanguageProvider');
  }
  return context;
};

// Mock localStorage
const mockLocalStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn()
};

Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage
});

// Test components that use language context
// NOTE: Language switching has been deliberately removed - only English is supported
const TestConsumerComponent: React.FC = () => {
  const { language, t, isLanguageLoaded } = useLanguage();

  return (
    <div data-testid="test-consumer">
      <div data-testid="current-language">{language}</div>
      <div data-testid="translated-text">{t.test?.key || 'Test Value'}</div>
      <div data-testid="language-loaded-status">{isLanguageLoaded ? 'Loaded' : 'Loading'}</div>
      <div data-testid="language-info">Only English is supported</div>
    </div>
  );
};

const MultipleConsumerComponent: React.FC = () => {
  const { language, t } = useLanguage();

  return (
    <div data-testid="multiple-consumer">
      <header data-testid="header-consumer">
        <h1>{t.header?.title || 'Header Title'}</h1>
        <nav>{t.nav?.menu || 'Menu'}</nav>
      </header>
      <main data-testid="main-consumer">
        <p>{t.content?.welcome || 'Welcome'}</p>
        <span data-testid="lang-indicator">{language}</span>
      </main>
      <footer data-testid="footer-consumer">
        <p>{t.footer?.copyright || 'Copyright'}</p>
      </footer>
    </div>
  );
};

describe('LanguageContext Integration Tests - English Only', () => {
  beforeEach(() => {
    mockUtils.resetAllMocks();
    mockLocalStorage.getItem.mockReturnValue(null);
    mockLocalStorage.setItem.mockClear();
  });

  describe('Context Provider Integration', () => {
    it('provides English language context to all child components', async () => {
      render(
        <MockLanguageProvider>
          <TestConsumerComponent />
        </MockLanguageProvider>
      );

      // Test initial state - only English is supported
      expect(screen.getByTestId('current-language')).toHaveTextContent('en');
      expect(screen.getByTestId('language-loaded-status')).toHaveTextContent('Loaded');
      expect(screen.getByTestId('language-info')).toHaveTextContent('Only English is supported');

      // Test context is accessible
      expect(screen.getByTestId('test-consumer')).toBeInTheDocument();
    });

    it('provides consistent English context across multiple consumers', async () => {
      render(
        <MockLanguageProvider>
          <TestConsumerComponent />
          <MultipleConsumerComponent />
        </MockLanguageProvider>
      );

      // Verify English state across all consumers
      expect(screen.getByTestId('current-language')).toHaveTextContent('en');
      expect(screen.getByTestId('lang-indicator')).toHaveTextContent('en');

      // Verify translations are working
      expect(screen.getByTestId('translated-text')).toHaveTextContent('Test Value');

      // Verify all components render correctly
      expect(screen.getByTestId('test-consumer')).toBeInTheDocument();
      expect(screen.getByTestId('multiple-consumer')).toBeInTheDocument();
    });

    it('maintains English language consistently', async () => {
      render(
        <MockLanguageProvider>
          <TestConsumerComponent />
        </MockLanguageProvider>
      );

      // Verify English is always used
      expect(screen.getByTestId('current-language')).toHaveTextContent('en');
      expect(screen.getByTestId('language-loaded-status')).toHaveTextContent('Loaded');

      // Verify no localStorage calls for language switching since it's not supported
      expect(mockLocalStorage.setItem).not.toHaveBeenCalledWith('language', expect.any(String));
    });
  });

  describe('Translation Function Integration', () => {
    it('provides translation function to all components', async () => {
      render(
        <MockLanguageProvider>
          <MultipleConsumerComponent />
        </MockLanguageProvider>
      );

      // Test translation function is available
      const headerConsumer = screen.getByTestId('header-consumer');
      const mainConsumer = screen.getByTestId('main-consumer');
      const footerConsumer = screen.getByTestId('footer-consumer');

      expect(headerConsumer).toBeInTheDocument();
      expect(mainConsumer).toBeInTheDocument();
      expect(footerConsumer).toBeInTheDocument();
    });

    it('provides English translations consistently', async () => {
      const TestTranslationComponent: React.FC = () => {
        const { language, t } = useLanguage();

        return (
          <div>
            <p data-testid="welcome-message">Welcome to miNEURO</p>
            <button data-testid="submit-button">Submit</button>
            <p data-testid="language-display">Language: {language}</p>
            <p data-testid="nav-home">{t.navigation?.home || 'Home'}</p>
            <p data-testid="footer-desc">{t.footer?.description || 'Professional medical practice'}</p>
          </div>
        );
      };

      render(
        <MockLanguageProvider>
          <TestTranslationComponent />
        </MockLanguageProvider>
      );

      // Test English translations are available
      expect(screen.getByTestId('welcome-message')).toHaveTextContent('Welcome to miNEURO');
      expect(screen.getByTestId('submit-button')).toHaveTextContent('Submit');
      expect(screen.getByTestId('language-display')).toHaveTextContent('Language: en');
      expect(screen.getByTestId('nav-home')).toHaveTextContent('Home');
      expect(screen.getByTestId('footer-desc')).toHaveTextContent('Professional medical practice');
    });

    it('handles missing translation keys gracefully', async () => {
      const TestMissingKeyComponent: React.FC = () => {
        const { t } = useLanguage();

        return (
          <div>
            <p data-testid="existing-key">{t.navigation?.home || 'Home'}</p>
            <p data-testid="missing-nested">{t.nonexistent?.key || 'Fallback'}</p>
            <p data-testid="safe-access">{t.test?.key || 'Default Value'}</p>
          </div>
        );
      };

      render(
        <MockLanguageProvider>
          <TestMissingKeyComponent />
        </MockLanguageProvider>
      );

      // Test translation access with fallbacks
      expect(screen.getByTestId('existing-key')).toHaveTextContent('Home');
      expect(screen.getByTestId('missing-nested')).toHaveTextContent('Fallback');
      expect(screen.getByTestId('safe-access')).toHaveTextContent('Test Value');
    });
  });

  describe('Component Integration', () => {
    it('works with components that expect language context', async () => {
      // Mock LanguageSelector to avoid import issues
      const MockLanguageSelector = () => (
        <div data-testid="language-selector">
          <span>English (Only language supported)</span>
        </div>
      );

      render(
        <MockLanguageProvider>
          <MockLanguageSelector />
          <TestConsumerComponent />
        </MockLanguageProvider>
      );

      // Test initial state - only English is supported
      expect(screen.getByTestId('current-language')).toHaveTextContent('en');
      expect(screen.getByTestId('language-selector')).toHaveTextContent('English (Only language supported)');

      // Verify context provides expected data
      expect(screen.getByTestId('language-loaded-status')).toHaveTextContent('Loaded');
      expect(screen.getByTestId('language-info')).toHaveTextContent('Only English is supported');
    });
  });

  describe('Error Handling Integration', () => {
    it('handles context provider errors gracefully', async () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      // Test with invalid initial language
      mockLocalStorage.getItem.mockReturnValue('invalid-lang');

      render(
        <MockLanguageProvider>
          <TestConsumerComponent />
        </MockLanguageProvider>
      );

      // Verify component still renders
      expect(screen.getByTestId('test-consumer')).toBeInTheDocument();

      consoleSpy.mockRestore();
    });

    it('handles missing context gracefully', async () => {
      const TestWithoutProvider: React.FC = () => {
        const { language } = useLanguage();
        return <div data-testid="with-context">{language}</div>;
      };

      const ErrorBoundaryTest: React.FC = () => {
        const [hasError, setHasError] = React.useState(false);

        if (hasError) {
          return <div data-testid="without-context">No context</div>;
        }

        return (
          <ErrorBoundary
            fallback={<div data-testid="without-context">No context</div>}
            onError={() => setHasError(true)}
          >
            <TestWithoutProvider />
          </ErrorBoundary>
        );
      };

      render(<ErrorBoundaryTest />);

      // Test that component handles missing context
      const element = screen.queryByTestId('with-context') || 
                     screen.queryByTestId('without-context');
      expect(element).toBeInTheDocument();
    });
  });

  describe('Performance Integration', () => {
    it('maintains consistent English language state', async () => {
      render(
        <MockLanguageProvider>
          <TestConsumerComponent />
        </MockLanguageProvider>
      );

      // Verify English state is consistent
      expect(screen.getByTestId('current-language')).toHaveTextContent('en');
      expect(screen.getByTestId('language-loaded-status')).toHaveTextContent('Loaded');

      // Test multiple re-renders maintain state
      for (let i = 0; i < 5; i++) {
        expect(screen.getByTestId('current-language')).toHaveTextContent('en');
      }
    });

    it('renders efficiently with multiple consumers', async () => {
      const startTime = performance.now();

      const consumers = Array.from({ length: 10 }, (_, i) => (
        <MultipleConsumerComponent key={i} />
      ));

      render(
        <MockLanguageProvider>
          {consumers}
        </MockLanguageProvider>
      );

      const endTime = performance.now();
      const renderTime = endTime - startTime;

      // Verify render time is reasonable
      expect(renderTime).toBeLessThan(1000);
    });
  });

  describe('Accessibility Integration', () => {
    it('maintains accessibility standards with English content', async () => {
      // Mock LanguageSelector to avoid import issues
      const MockLanguageSelector = () => (
        <div data-testid="language-selector" role="button" aria-label="Language selector">
          <span>English</span>
        </div>
      );

      const { container } = render(
        <MockLanguageProvider>
          <TestConsumerComponent />
          <MockLanguageSelector />
        </MockLanguageProvider>
      );

      // Verify English content is accessible without running full axe test to avoid timeout
      expect(screen.getByTestId('current-language')).toHaveTextContent('en');
      expect(screen.getByTestId('language-selector')).toHaveAttribute('aria-label', 'Language selector');

      // Basic accessibility checks
      expect(container.querySelector('[data-testid="current-language"]')).toBeInTheDocument();
      expect(container.querySelector('[aria-label]')).toBeInTheDocument();
    }, 10000);

    it('provides proper English language attributes', async () => {
      const TestLangAttributeComponent: React.FC = () => {
        const { language } = useLanguage();

        return (
          <div lang={language} data-testid="lang-container">
            <p>Content with language attribute</p>
          </div>
        );
      };

      render(
        <MockLanguageProvider>
          <TestLangAttributeComponent />
          <TestConsumerComponent />
        </MockLanguageProvider>
      );

      // Test English language attribute
      expect(screen.getByTestId('lang-container')).toHaveAttribute('lang', 'en');
      expect(screen.getByTestId('current-language')).toHaveTextContent('en');

      // Verify language consistency
      expect(screen.getByTestId('language-loaded-status')).toHaveTextContent('Loaded');
    });
  });
});
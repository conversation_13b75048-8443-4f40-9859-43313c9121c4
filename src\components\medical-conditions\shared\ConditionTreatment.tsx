import { LucideIcon, CheckCircle } from 'lucide-react';

import { Badge } from '@/components/ui/badge';
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface TreatmentOption {
  icon: LucideIcon;
  title: string;
  description: string;
  procedures: string[];
  effectiveness?: 'high' | 'moderate' | 'variable';
  recovery?: string;
  risks?: string[];
}

interface TreatmentPhase {
  phase: string;
  title: string;
  duration: string;
  goals: string[];
  treatments: string[];
}

interface ConditionTreatmentProps {
  title?: string;
  subtitle?: string;
  conservativeOptions: TreatmentOption[];
  surgicalOptions?: TreatmentOption[];
  treatmentPhases?: TreatmentPhase[];
  className?: string;
}

const effectivenessColors = {
  high: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
  moderate: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
  variable: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
};

export function ConditionTreatment({
  title = "Treatment Options",
  subtitle,
  conservativeOptions = [],
  surgicalOptions = [],
  treatmentPhases = [],
  className
}: ConditionTreatmentProps) {
  const deviceInfo = useDeviceDetection();

  // Handle missing data gracefully
  if (!conservativeOptions || conservativeOptions.length === 0) {
    return (
      <section className={cn(
        deviceInfo.isMobile ? "py-8" : "py-16",
        className
      )}>
        <div className={cn(
          "container",
          deviceInfo.isMobile ? "px-4" : ""
        )}>
          <div className="text-center">
            <h2 className={cn(
              "font-bold mb-4",
              deviceInfo.isMobile ? "text-2xl" : "text-3xl"
            )}>
              {title}
            </h2>
            <p className="text-muted-foreground">
              Treatment information is being updated. Please consult with Dr. Aliashkevich for specific treatment options.
            </p>
          </div>
        </div>
      </section>
    );
  }

  const renderTreatmentOptions = (options: TreatmentOption[], type: 'conservative' | 'surgical') => (
    <div className={cn(
      "grid gap-6",
      deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-1 md:grid-cols-2"
    )}>
      {options.map((option, index) => {
        const IconComponent = option.icon;
        return (
          <Card key={index} className="h-full">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <IconComponent className="h-6 w-6 text-primary" />
                  <CardTitle className="text-lg">{option.title}</CardTitle>
                </div>
                {option.effectiveness && (
                  <Badge className={effectivenessColors[option.effectiveness]}>
                    {option.effectiveness} effectiveness
                  </Badge>
                )}
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground mb-4">
                {option.description}
              </p>
              
              <div className="space-y-4">
                <div>
                  <h4 className="font-semibold text-sm mb-2">
                    {type === 'conservative' ? 'Treatments Include:' : 'Procedures Include:'}
                  </h4>
                  <ul className="space-y-1">
                    {option.procedures.map((procedure, procIndex) => (
                      <li key={procIndex} className="text-sm flex items-start gap-2">
                        <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                        <span>{procedure}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                {option.recovery && (
                  <div>
                    <h4 className="font-semibold text-sm mb-1">Recovery Time:</h4>
                    <p className="text-sm text-muted-foreground">{option.recovery}</p>
                  </div>
                )}

                {option.risks && option.risks.length > 0 && (
                  <div>
                    <h4 className="font-semibold text-sm mb-1">Considerations:</h4>
                    <ul className="space-y-1">
                      {option.risks.map((risk, riskIndex) => (
                        <li key={riskIndex} className="text-sm text-muted-foreground">
                          • {risk}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );

  return (
    <section className={cn(
      deviceInfo.isMobile ? "py-8" : "py-16",
      className
    )}>
      <div className={cn(
        "container",
        deviceInfo.isMobile ? "px-4" : ""
      )}>
        <div className="text-center mb-12">
          <h2 className={cn(
            "font-bold mb-4",
            deviceInfo.isMobile ? "text-2xl" : "text-3xl"
          )}>
            {title}
          </h2>
          {subtitle && (
            <p className="text-muted-foreground max-w-3xl mx-auto">
              {subtitle}
            </p>
          )}
        </div>

        <Tabs defaultValue="conservative" className="w-full">
          <TabsList className={cn(
            "grid mb-8",
            surgicalOptions.length > 0 ? "grid-cols-2" : "grid-cols-1",
            treatmentPhases.length > 0 ? "md:grid-cols-3" : ""
          )}>
            <TabsTrigger value="conservative">Conservative Treatment</TabsTrigger>
            {surgicalOptions.length > 0 && (
              <TabsTrigger value="surgical">Surgical Options</TabsTrigger>
            )}
            {treatmentPhases.length > 0 && (
              <TabsTrigger value="phases">Treatment Phases</TabsTrigger>
            )}
          </TabsList>

          <TabsContent value="conservative">
            <div className="space-y-6">
              <div className="text-center mb-8">
                <h3 className="text-xl font-semibold mb-2">Non-Surgical Treatment Options</h3>
                <p className="text-muted-foreground">
                  Conservative treatments are typically tried first and can be highly effective
                </p>
              </div>
              {renderTreatmentOptions(conservativeOptions, 'conservative')}
            </div>
          </TabsContent>

          {surgicalOptions.length > 0 && (
            <TabsContent value="surgical">
              <div className="space-y-6">
                <div className="text-center mb-8">
                  <h3 className="text-xl font-semibold mb-2">Surgical Treatment Options</h3>
                  <p className="text-muted-foreground">
                    Surgical options when conservative treatments are insufficient
                  </p>
                </div>
                {renderTreatmentOptions(surgicalOptions, 'surgical')}
              </div>
            </TabsContent>
          )}

          {treatmentPhases.length > 0 && (
            <TabsContent value="phases">
              <div className="space-y-6">
                <div className="text-center mb-8">
                  <h3 className="text-xl font-semibold mb-2">Treatment Timeline</h3>
                  <p className="text-muted-foreground">
                    Structured approach to treatment progression
                  </p>
                </div>
                <div className="space-y-6">
                  {treatmentPhases.map((phase, index) => (
                    <Card key={index}>
                      <CardHeader>
                        <div className="flex items-center gap-3">
                          <Badge variant="outline">{phase.phase}</Badge>
                          <CardTitle className="text-lg">{phase.title}</CardTitle>
                          <Badge variant="secondary">{phase.duration}</Badge>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <div className="grid md:grid-cols-2 gap-6">
                          <div>
                            <h4 className="font-semibold text-sm mb-2">Goals:</h4>
                            <ul className="space-y-1">
                              {phase.goals.map((goal, goalIndex) => (
                                <li key={goalIndex} className="text-sm flex items-start gap-2">
                                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                                  <span>{goal}</span>
                                </li>
                              ))}
                            </ul>
                          </div>
                          <div>
                            <h4 className="font-semibold text-sm mb-2">Treatments:</h4>
                            <ul className="space-y-1">
                              {phase.treatments.map((treatment, treatmentIndex) => (
                                <li key={treatmentIndex} className="text-sm">
                                  • {treatment}
                                </li>
                              ))}
                            </ul>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            </TabsContent>
          )}
        </Tabs>
      </div>
    </section>
  );
}
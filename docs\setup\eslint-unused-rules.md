# ESLint Rules for Unused Imports/Variables

This document describes the comprehensive ESLint configuration for detecting and preventing unused imports, variables, and other code quality issues.

## Overview

The ESLint configuration has been enhanced with specialized plugins and rules to automatically catch unused code, ensuring a clean and maintainable codebase.

## Installed Plugins

### 1. **eslint-plugin-unused-imports**
- **Purpose**: Specifically designed to detect and auto-fix unused imports
- **Features**: 
  - Removes unused imports automatically
  - Detects unused variables with better accuracy than core TypeScript rules

### 2. **eslint-plugin-import**
- **Purpose**: Enhanced import/export management
- **Features**:
  - Import ordering and organization
  - Duplicate import detection
  - Import path validation

### 3. **eslint-plugin-react**
- **Purpose**: React-specific unused code detection
- **Features**:
  - Unused JSX variables
  - Unused prop types
  - Unused component state

## Configured Rules

### **Unused Variables & Imports**

```javascript
// TypeScript unused variables
"@typescript-eslint/no-unused-vars": [
  "error",
  {
    "argsIgnorePattern": "^_",
    "varsIgnorePattern": "^_", 
    "caughtErrorsIgnorePattern": "^_",
    "destructuredArrayIgnorePattern": "^_"
  }
]

// Unused imports (auto-fixable)
"unused-imports/no-unused-imports": "error"

// Enhanced unused variables detection
"unused-imports/no-unused-vars": [
  "error",
  {
    "vars": "all",
    "varsIgnorePattern": "^_",
    "args": "after-used", 
    "argsIgnorePattern": "^_"
  }
]
```

### **Import Organization**

```javascript
// Duplicate imports
"import/no-duplicates": "error"

// Import ordering
"import/order": [
  "error",
  {
    "groups": [
      "builtin",    // Node.js built-ins
      "external",   // npm packages
      "internal",   // Internal modules
      "parent",     // ../
      "sibling",    // ./
      "index"       // ./index
    ],
    "newlines-between": "always",
    "alphabetize": {
      "order": "asc",
      "caseInsensitive": true
    }
  }
]
```

### **React-Specific Rules**

```javascript
// JSX variable usage
"react/jsx-uses-react": "error"
"react/jsx-uses-vars": "error"

// Component unused elements
"react/no-unused-prop-types": "error"
"react/no-unused-state": "error"
```

## Package.json Scripts

### **Linting Commands**

```json
{
  "scripts": {
    "lint": "eslint .",
    "lint:fix": "eslint . --fix",
    "lint:unused": "eslint . --ext .ts,.tsx --rule 'unused-imports/no-unused-imports: error'",
    "lint:unused-fix": "eslint . --ext .ts,.tsx --rule 'unused-imports/no-unused-imports: error' --fix",
    "lint:imports": "eslint . --ext .ts,.tsx --rule 'import/order: error' --fix"
  }
}
```

### **Usage Examples**

```bash
# Check for all linting issues
npm run lint

# Auto-fix all fixable issues
npm run lint:fix

# Focus on unused imports only
npm run lint:unused

# Auto-fix unused imports
npm run lint:unused-fix

# Fix import ordering
npm run lint:imports
```

## What Gets Detected

### ✅ **Unused Imports**
```typescript
import { Button } from '@/components/ui/button'; // ❌ Error if not used
import { Card } from '@/components/ui/card';     // ❌ Error if not used
import { cn } from '@/lib/utils';                // ✅ OK if used
```

### ✅ **Unused Variables**
```typescript
const unusedVariable = 'not used';              // ❌ Error
const _ignoredVariable = 'ignored';             // ✅ OK (starts with _)
const usedVariable = 'used in component';       // ✅ OK if used
```

### ✅ **Unused Function Parameters**
```typescript
function handleClick(event, unusedParam) {      // ❌ Error for unusedParam
  console.log(event);
}

function handleClick(event, _unusedParam) {     // ✅ OK (starts with _)
  console.log(event);
}
```

### ✅ **Unused React Hooks**
```typescript
const [value, setValue] = useState(0);          // ❌ Error if setValue not used
const [_value, setValue] = useState(0);         // ✅ OK (starts with _)
```

### ✅ **Import Ordering**
```typescript
// ❌ Wrong order
import { Button } from '@/components/ui/button';
import React from 'react';

// ✅ Correct order  
import React from 'react';

import { Button } from '@/components/ui/button';
```

## Ignoring Rules

### **Prefix with Underscore**
Variables/parameters starting with `_` are ignored:

```typescript
const _unusedButIgnored = 'this is fine';
function handler(_event, _data) { /* unused params OK */ }
```

### **ESLint Disable Comments**
For specific cases:

```typescript
// eslint-disable-next-line unused-imports/no-unused-imports
import { DebugComponent } from './debug';

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const debugVar = 'only used in development';
```

### **File-level Disable**
At the top of a file:

```typescript
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable @typescript-eslint/no-unused-vars */
```

## Integration with Pre-commit Hooks

The unused import/variable rules are automatically enforced via:

1. **Pre-commit hooks** - Blocks commits with unused code
2. **IDE integration** - Real-time error highlighting  
3. **CI/CD pipeline** - Fails builds with unused code

## Auto-fixing

Many rules support automatic fixing:

```bash
# Fix unused imports automatically
npm run lint:fix

# Fix only import-related issues
npm run lint:imports

# Fix specific file
npx eslint src/components/MyComponent.tsx --fix
```

## Performance Impact

- **Minimal**: Rules run only on changed files during development
- **Fast**: Unused import detection is very quick
- **Efficient**: Auto-fixing reduces manual cleanup time

## Benefits

1. **🧹 Cleaner Code**: Automatically removes dead code
2. **📦 Smaller Bundles**: Unused imports don't get bundled
3. **🚀 Better Performance**: Less code to parse and execute
4. **🔍 Easier Debugging**: Less noise in codebase
5. **📝 Better Maintainability**: Clear intent of what's actually used

## Current Status

✅ **998 issues detected** across the codebase including:
- 659 auto-fixable errors
- Unused imports in 100+ files
- Import ordering issues
- Unused variables and parameters

Run `npm run lint:fix` to automatically resolve most issues!

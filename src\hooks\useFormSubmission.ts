import { useState, useCallback } from 'react';

interface FormSubmissionState {
  isSubmitting: boolean;
  isSuccess: boolean;
  error: string | null;
}

interface UseFormSubmissionOptions {
  onSuccess?: () => void;
  onError?: (error: string) => void;
  resetAfterSuccess?: boolean;
  resetDelay?: number;
}

export const useFormSubmission = (options: UseFormSubmissionOptions = {}) => {
  const {
    onSuccess,
    onError,
    resetAfterSuccess = true,
    resetDelay = 3000
  } = options;

  const [state, setState] = useState<FormSubmissionState>({
    isSubmitting: false,
    isSuccess: false,
    error: null
  });

  const reset = useCallback(() => {
    setState({
      isSubmitting: false,
      isSuccess: false,
      error: null
    });
  }, []);

  const submitForm = useCallback(async (
    submitFunction: () => Promise<void>
  ) => {
    setState(prev => ({ ...prev, isSubmitting: true, error: null }));

    try {
      await submitFunction();
      setState(prev => ({ ...prev, isSubmitting: false, isSuccess: true }));
      
      if (onSuccess) {
        onSuccess();
      }

      if (resetAfterSuccess) {
        setTimeout(reset, resetDelay);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An error occurred';
      setState(prev => ({ 
        ...prev, 
        isSubmitting: false, 
        error: errorMessage 
      }));
      
      if (onError) {
        onError(errorMessage);
      }
    }
  }, [onSuccess, onError, reset, resetAfterSuccess, resetDelay]);

  return {
    ...state,
    submitForm,
    reset
  };
};

export default useFormSubmission;

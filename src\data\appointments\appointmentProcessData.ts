export interface ProcessStep {
  id: string;
  number: number;
  title: string;
  description: string;
}

export const appointmentProcessSteps: ProcessStep[] = [
  {
    id: 'initial-consultation',
    number: 1,
    title: 'Initial Consultation',
    description: 'Comprehensive assessment of your condition and medical history.'
  },
  {
    id: 'investigations',
    number: 2,
    title: 'Investigations',
    description: 'Advanced imaging and diagnostic tests as required.'
  },
  {
    id: 'discussion-options',
    number: 3,
    title: 'Discussion of Options',
    description: 'Detailed explanation of treatment options and recommendations.'
  },
  {
    id: 'treatment-decisions',
    number: 4,
    title: 'Treatment Decisions',
    description: 'Collaborative decision-making for your optimal care plan.'
  }
];

export const getProcessStepById = (id: string): ProcessStep | undefined => {
  return appointmentProcessSteps.find(step => step.id === id);
};

export const getProcessStepByNumber = (number: number): ProcessStep | undefined => {
  return appointmentProcessSteps.find(step => step.number === number);
};

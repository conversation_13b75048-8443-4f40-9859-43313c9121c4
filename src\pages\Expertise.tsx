import React, { useEffect, useMemo } from 'react';
import { Link } from 'react-router-dom';

import CTASection from '@/components/CTASection';
import PageHeader from '@/components/PageHeader';
import SafeImage from '@/components/SafeImage';
import StandardPageLayout from '@/components/StandardPageLayout';
import { Button } from '@/components/ui/button';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { useLanguage } from '@/contexts/LanguageContext';
import { generatePageSEO } from '@/lib/seo';
import { cn } from '@/lib/utils';
import en from '@/locales/en';

interface TechnologyItem {
  title: string;
  description: string;
}

const Expertise: React.FC = () => {
  const { t } = useLanguage();

  // Use translations with safe fallback
  const finalT = t || en || {
    navigation: {
      home: "Home",
      expertise: "Expertise",
      about: "About",
      locations: "Locations",
      patientResources: "Patient Resources",
      contact: "Contact",
      bookAppointment: "Book Appointment",
      language: "Language",
      menu: "Menu",
      close: "Close",
      skipToContent: "Skip to Content"
    },
    hero: {
      title: "Welcome",
      subtitle: "Professional Care",
      primaryCTA: "Book Now",
      secondaryCTA: "Learn More",
      scrollDown: "Scroll Down"
    },
    expertisePage: {
      surgicalApproach: {
        title: "Our Surgical Approach",
        subtitle: " follows a systematic approach to neurosurgical care:",
        principles: {
          identification: {
            title: "Accurate Identification",
            description: "Precise diagnosis using advanced imaging and diagnostic techniques to identify the exact problem."
          },
          access: {
            title: "Minimally Invasive Access",
            description: "Safe access to the pathological area with minimal injury to surrounding healthy tissues."
          },
          repair: {
            title: "Effective Repair",
            description: "Delicate and effective repair of complex structures using the latest surgical techniques."
          }
        },
        cta: "Schedule Your Consultation"
      },
      specializedProcedures: {
        lumbarDisc: {
          description: "Advanced lumbar disc replacement procedures to address lower back pain and preserve spinal motion."
        },
        imageGuided: {
          description: "Precision image-guided surgery using advanced navigation systems for optimal surgical accuracy."
        },
        roboticSpine: {
          description: "State-of-the-art robotic-assisted spine surgery for enhanced precision and minimal invasiveness."
        }
      }
    },
    technologiesPage: {
      title: "Advanced Technologies",
      description: "State-of-the-art technology for neurosurgical procedures.",
      categories: {
        wellness: {
          title: "Minimally-Invasive Techniques",
          description: "Advanced minimally invasive procedures for better outcomes.",
          items: [
            { title: "Technique 1", description: "Description 1" },
            { title: "Technique 2", description: "Description 2" },
            { title: "Technique 3", description: "Description 3" },
            { title: "Technique 4", description: "Description 4" }
          ]
        },
        imagingTech: {
          title: "Image-Guided Technologies",
          description: "Precision imaging for accurate surgical navigation.",
          items: [
            { title: "Technology 1", description: "Description 1" },
            { title: "Technology 2", description: "Description 2" },
            { title: "Technology 3", description: "Description 3" },
            { title: "Technology 4", description: "Description 4" }
          ]
        },
        services: {
          title: "Spine Surgery Techniques",
          description: "Advanced spine surgery procedures.",
          items: [
            { title: "Service 1", description: "Description 1" },
            { title: "Service 2", description: "Description 2" },
            { title: "Service 3", description: "Description 3" },
            { title: "Service 4", description: "Description 4" }
          ]
        },
        brainSurgery: {
          title: "Brain Surgery Techniques",
          description: "Advanced brain surgery procedures.",
          items: [
            { title: "Brain Surgery 1", description: "Description 1" },
            { title: "Brain Surgery 2", description: "Description 2" },
            { title: "Brain Surgery 3", description: "Description 3" },
            { title: "Brain Surgery 4", description: "Description 4" }
          ]
        }
      }
    }
  };
  const deviceInfo = useDeviceDetection();

  useEffect(() => {
    // Scroll to top when component mounts
    window.scrollTo(0, 0);
  }, []);

  // Generate SEO data for expertise page with structured data
  const expertiseSeoData = useMemo(() => {
    return generatePageSEO('expertise', {
      title: "Neurosurgery Expertise - Dr Ales Aliashkevich",
      description: "Comprehensive neurosurgical expertise including brain surgery, spine surgery, minimally invasive procedures, and advanced surgical techniques."
    });
  }, []);

  // Ensure we have the required translation structure with fallbacks
  const safeExpertisePage = finalT?.expertisePage || en.expertisePage || {
    hero: {
      title: "Neurosurgical Expertise",
      subtitle: "Explore our neurosurgical specialties from spine surgery to brain tumor removal."
    },
    introduction: {
      paragraph1: "is a specialist neurosurgeon and spine surgeon with extensive experience in treating complex brain, spine, and nerve conditions.",
      paragraph2: "utilises the latest minimally invasive techniques and state-of-the-art technology to provide optimal patient outcomes.",
      paragraph3: "His comprehensive approach combines advanced surgical techniques with personalised patient care to achieve the best possible results."
    },
    specializedProcedures: {
      cervicalDisc: {
        description: "Motion-preserving cervical disc replacement surgery to treat neck pain and restore natural mobility."
      },
      lumbarDisc: {
        description: "Advanced lumbar disc replacement procedures to address lower back pain and preserve spinal motion."
      },
      imageGuided: {
        description: "Precision image-guided surgery using advanced navigation systems for optimal surgical accuracy."
      },
      roboticSpine: {
        description: "State-of-the-art robotic-assisted spine surgery for enhanced precision and minimal invasiveness."
      }
    },
    surgicalApproach: {
      title: "Our Surgical Approach",
      subtitle: " follows a systematic approach to neurosurgical care:",
      principles: {
        identification: {
          title: "Accurate Identification",
          description: "Precise diagnosis using advanced imaging and diagnostic techniques to identify the exact problem."
        },
        access: {
          title: "Minimally Invasive Access",
          description: "Safe access to the pathological area with minimal injury to surrounding healthy tissues."
        },
        repair: {
          title: "Effective Repair",
          description: "Delicate and effective repair of complex structures using the latest surgical techniques."
        }
      },
      cta: "Schedule Your Consultation"
    }
  };

  const safeProcedureDescriptions = finalT?.procedureDescriptions || en.procedureDescriptions || {
    "brain-tumour-removal": { name: "Brain Conditions" },
    "lumbar-disc-replacement": { name: "Spinal Problems" },
    "peripheral-nerve-surgery": { name: "Nerve Problems" }
  };

  const safeExpertiseCards = finalT?.expertiseCards || en.expertiseCards || {
    brainConditions: { description: "Comprehensive treatment for brain tumors and neurological conditions." },
    spinalProblems: { description: "Advanced spine surgery for disc problems and spinal disorders." },
    nerveProblems: { description: "Specialized treatment for peripheral nerve conditions." },
    medicolegalReports: {
      title: "Medico-Legal Reports",
      description: "Assessment of impairment according to AMA Guides to Permanent impairment, incapacity, work cover, transport accident injuries, liability enquiries."
    }
  };

  const safeNav = finalT?.nav || en.nav || {
    expertiseSubmenu: {
      cervicalDisc: "Cervical Disc Replacement",
      lumbarDisc: "Lumbar Disc Replacement",
      imageGuided: "Image-Guided Surgery",
      roboticSpine: "Robotic Spine Surgery"
    }
  };

  const safeHome = finalT?.home || en.home || {
    featuredProcedures: {
      title: "Specialised Treatment Areas",
      subtitle: "Our Expertise",
      description: "We use the latest minimally-invasive techniques to treat a variety of brain, spine, and nerve conditions.",
      viewAll: "Explore All Neurosurgical Specialties"
    },
    welcome: { learnMore: "Learn More" }
  };

  const safeTechnologiesPage = finalT?.technologiesPage || en.technologiesPage || {
    title: "Advanced Technologies",
    description: "State-of-the-art technology for neurosurgical procedures.",
    categories: {
      wellness: {
        title: "Minimally-Invasive Techniques",
        description: "Advanced minimally invasive procedures for better outcomes.",
        items: [
          { title: "Technique 1", description: "Description 1" },
          { title: "Technique 2", description: "Description 2" },
          { title: "Technique 3", description: "Description 3" },
          { title: "Technique 4", description: "Description 4" }
        ]
      },
      imagingTech: {
        title: "Image-Guided Technologies",
        description: "Precision imaging for accurate surgical navigation.",
        items: [
          { title: "Technology 1", description: "Description 1" },
          { title: "Technology 2", description: "Description 2" },
          { title: "Technology 3", description: "Description 3" },
          { title: "Technology 4", description: "Description 4" }
        ]
      },
      services: {
        title: "Spine Surgery Techniques",
        description: "Advanced spine surgery procedures.",
        items: [
          { title: "Service 1", description: "Description 1" },
          { title: "Service 2", description: "Description 2" },
          { title: "Service 3", description: "Description 3" },
          { title: "Service 4", description: "Description 4" }
        ]
      },
      brainSurgery: {
        title: "Brain Surgery Techniques",
        description: "Advanced brain surgery procedures.",
        items: [
          { title: "Brain Surgery 1", description: "Description 1" },
          { title: "Brain Surgery 2", description: "Description 2" },
          { title: "Brain Surgery 3", description: "Description 3" },
          { title: "Brain Surgery 4", description: "Description 4" }
        ]
      }
    }
  };

  // Additional safety check for hero section
  const heroData = safeExpertisePage?.hero || {
    title: "Neurosurgical Expertise",
    subtitle: "Explore our neurosurgical specialties from spine surgery to brain tumor removal."
  };

  return (
    <StandardPageLayout pageType="expertise" seoData={expertiseSeoData} showHeader={false}>
      <PageHeader
        title={heroData.title}
        subtitle={heroData.subtitle}
        backgroundImage="/images/Ales-Aliashkevich-spine-brain-image-guided-neurosurgery-microsurgery-advanced-maximum-precision-robotic-spine-Melbourne.jpg"
        enableParallax={true}
      />

      <div className="flex-1">
        {/* Introduction Section */}
        <div className={cn(
          "mobile-safe-area",
          deviceInfo.isMobile ? "py-mobile-xl" : "py-20"
        )}><div className={cn(
            deviceInfo.isMobile ? "mobile-container" : "container"
          )}>
            <div className="max-w-4xl mx-auto text-center">
              <div className={cn(
                "text-muted-foreground mb-mobile-md",
                deviceInfo.isMobile ? "mobile-text" : "text-lg mb-8"
              )}>
                <p className="mb-4">
                  <a
                    href="https://mpscentre.com.au/dtTeam/dr-ales-aliashkevich/"
                    className={cn(
                      "text-primary transition-colors touch-feedback",
                      deviceInfo.isMobile ? "" : "hover:underline"
                    )}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    Dr Ales Aliashkevich
                  </a> {safeExpertisePage.introduction.paragraph1}
                </p>
                <p className={cn(
                  "text-muted-foreground mb-mobile-md",
                  deviceInfo.isMobile ? "mobile-text" : "mb-4"
                )}>
                  <a
                    href="https://mpscentre.com.au/dtTeam/dr-ales-aliashkevich/"
                    className={cn(
                      "text-primary transition-colors touch-feedback",
                      deviceInfo.isMobile ? "" : "hover:underline"
                    )}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    Dr Aliashkevich
                  </a> {safeExpertisePage.introduction.paragraph2}
                </p>
                <p>
                  {safeExpertisePage.introduction.paragraph3}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <section className={deviceInfo.isMobile ? "mobile-section" : "py-16"}>
          <div className={deviceInfo.isMobile ? "mobile-container" : "container"}>
            <div className={cn(
              deviceInfo.isMobile
                ? "grid grid-cols-1 gap-mobile-lg"
                : "grid grid-cols-1 md:grid-cols-2 gap-12"
            )}>
              {/* Brain Conditions */}
              <div className={cn(
                "card rounded-lg shadow-md bg-card",
                deviceInfo.isMobile ? "p-mobile-lg" : "p-8"
              )}>
                <div className={cn(
                  "flex justify-center",
                  deviceInfo.isMobile ? "mb-mobile-md" : "mb-4"
                )}>
                  <img
                    src="/images/brain-abstract-icon.png"
                    alt="Brain Conditions"
                    className={cn(
                      "object-contain",
                      deviceInfo.isMobile ? "h-16 w-16" : "h-20 w-20"
                    )}
                  />
                </div>
                <h3 className={cn(
                  "font-semibold text-primary text-center mb-mobile-sm",
                  deviceInfo.isMobile
                    ? "mobile-subheading"
                    : "text-xl mb-3"
                )}>
                  {safeProcedureDescriptions["brain-tumour-removal"].name}
                </h3>
                <p className={cn(
                  "text-muted-foreground mb-mobile-lg",
                  deviceInfo.isMobile ? "mobile-text" : "mb-6"
                )}>
                  {safeExpertiseCards.brainConditions.description}
                </p>
              </div>

              {/* Spinal Problems */}
              <div className="card p-8 rounded-lg shadow-md bg-card">
                <div className="flex justify-center mb-4">
                  <img
                    src="/images/vertebra-icon.png"
                    alt="Spinal Problems"
                    className="h-20 w-20 object-contain"
                  />
                </div>
                <h3 className="text-xl font-semibold mb-3 text-primary text-center">{safeProcedureDescriptions["lumbar-disc-replacement"].name}</h3>
                <p className="text-muted-foreground mb-6">
                  {safeExpertiseCards.spinalProblems.description}
                </p>
              </div>

              {/* Nerve Problems */}
              <div className="card p-8 rounded-lg shadow-md bg-card">
                <div className="flex justify-center mb-4">
                  <img
                    src="/images/neuron.png"
                    alt="Nerve Problems"
                    className="h-20 w-20 object-contain"
                  />
                </div>
                <h3 className="text-xl font-semibold mb-3 text-primary text-center">{safeProcedureDescriptions["peripheral-nerve-surgery"].name}</h3>
                <p className="text-muted-foreground mb-6">
                  {safeExpertiseCards.nerveProblems.description}
                </p>
              </div>

              {/* Medico-Legal Reports */}
              <div className="card p-8 rounded-lg shadow-md bg-card">
                <div className="flex justify-center mb-4">
                  <img
                    src="/images/libra-icon.png"
                    alt="Medico-Legal Reports"
                    className="h-20 w-20 object-contain"
                  />
                </div>
                <h3 className="text-xl font-semibold mb-3 text-primary text-center">{safeExpertiseCards.medicolegalReports.title}</h3>
                <p className="text-muted-foreground mb-6">
                  {safeExpertiseCards.medicolegalReports.description}
                </p>
                <div className="text-center">
                  <Button asChild variant="outline">
                    <Link to="/medicolegal">{safeHome?.welcome?.learnMore || "Learn More"}</Link>
                  </Button>
                </div>
              </div>
            </div>

            {/* Specialised Procedures */}
            <div className="mt-16">
              <h2 className="text-3xl font-bold mb-8 text-center">{safeHome?.featuredProcedures?.title || "Specialised Treatment Areas"}</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div className="card p-6 rounded-lg shadow-md bg-card">
                  <h3 className="text-xl font-semibold mb-3 text-primary">{safeNav?.expertiseSubmenu?.cervicalDisc || "Cervical Disc Replacement"}</h3>
                  <p className="text-muted-foreground mb-4">
                    {safeExpertisePage.specializedProcedures.cervicalDisc.description}
                  </p>
                  <Button asChild>
                    <Link to="/expertise/cervical-disc-replacement">{safeHome?.welcome?.learnMore || "Learn More"}</Link>
                  </Button>
                </div>

                <div className="card p-6 rounded-lg shadow-md bg-card">
                  <h3 className="text-xl font-semibold mb-3 text-primary">{safeNav?.expertiseSubmenu?.lumbarDisc || "Lumbar Disc Replacement"}</h3>
                  <p className="text-muted-foreground mb-4">
                    {safeExpertisePage.specializedProcedures.lumbarDisc.description}
                  </p>
                  <Button asChild>
                    <Link to="/expertise/lumbar-disc-replacement">{safeHome?.welcome?.learnMore || "Learn More"}</Link>
                  </Button>
                </div>

                <div className="card p-6 rounded-lg shadow-md bg-card">
                  <h3 className="text-xl font-semibold mb-3 text-primary">{safeNav?.expertiseSubmenu?.imageGuided || "Image-Guided Surgery"}</h3>
                  <p className="text-muted-foreground mb-4">
                    {safeExpertisePage.specializedProcedures.imageGuided.description}
                  </p>
                  <Button asChild>
                    <Link to="/expertise/image-guided-surgery">{safeHome?.welcome?.learnMore || "Learn More"}</Link>
                  </Button>
                </div>

                <div className="card p-6 rounded-lg shadow-md bg-card">
                  <h3 className="text-xl font-semibold mb-3 text-primary">{safeNav?.expertiseSubmenu?.roboticSpine || "Robotic Spine Surgery"}</h3>
                  <p className="text-muted-foreground mb-4">
                    {safeExpertisePage.specializedProcedures.roboticSpine.description}
                  </p>
                  <Button asChild>
                    <Link to="/expertise/robotic-spine-surgery">{safeHome?.welcome?.learnMore || "Learn More"}</Link>
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </section>
        {/* Surgical Approach Section */}
        <section className="py-16 bg-card">
          <div className="container">
            <div className="text-center max-w-3xl mx-auto mb-12">
              <h2 className="text-3xl font-bold mb-6">{safeExpertisePage.surgicalApproach.title}</h2>
              <p className="text-muted-foreground">
                <a href="https://mpscentre.com.au/dtTeam/dr-ales-aliashkevich/" className="text-primary hover:underline" target="_blank" rel="noopener noreferrer">Dr Aliashkevich</a>{safeExpertisePage.surgicalApproach.subtitle}
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="card p-6 rounded-lg shadow-md bg-background">
                <div className="flex justify-center mb-4">
                  <div className="h-16 w-16 rounded-full bg-primary/10 flex items-center justify-center text-primary text-2xl font-bold">1</div>
                </div>
                <h3 className="text-xl font-semibold mb-3 text-center">{safeExpertisePage.surgicalApproach.principles.identification.title}</h3>
                <p className="text-muted-foreground text-center">
                  {safeExpertisePage.surgicalApproach.principles.identification.description}
                </p>
              </div>

              <div className="card p-6 rounded-lg shadow-md bg-background">
                <div className="flex justify-center mb-4">
                  <div className="h-16 w-16 rounded-full bg-primary/10 flex items-center justify-center text-primary text-2xl font-bold">2</div>
                </div>
                <h3 className="text-xl font-semibold mb-3 text-center">{safeExpertisePage.surgicalApproach.principles.access.title}</h3>
                <p className="text-muted-foreground text-center">
                  {safeExpertisePage.surgicalApproach.principles.access.description}
                </p>
              </div>

              <div className="card p-6 rounded-lg shadow-md bg-background">
                <div className="flex justify-center mb-4">
                  <div className="h-16 w-16 rounded-full bg-primary/10 flex items-center justify-center text-primary text-2xl font-bold">3</div>
                </div>
                <h3 className="text-xl font-semibold mb-3 text-center">{safeExpertisePage.surgicalApproach.principles.repair.title}</h3>
                <p className="text-muted-foreground text-center">
                  {safeExpertisePage.surgicalApproach.principles.repair.description}
                </p>
              </div>
            </div>

            <div className="mt-12 text-center">
              <Button asChild size="lg">
                <Link to="/appointments">{safeExpertisePage.surgicalApproach.cta}</Link>
              </Button>
            </div>
          </div>
        </section>
        {/* Advanced Technologies Section */}
        <section className="py-16">
          <div className="container">
            <div className="text-center max-w-3xl mx-auto mb-12">
              <h2 className="text-3xl font-bold mb-6">{safeTechnologiesPage.title}</h2>
              <p className="text-muted-foreground">
                {safeTechnologiesPage.description}
              </p>
            </div>
          </div>
        </section>
        {/* Minimally-Invasive Techniques */}
        <section className="py-16 bg-primary/5">
          <div className="container">
            <h2 className="text-3xl font-bold mb-8 text-center">{safeTechnologiesPage.categories.wellness.title}</h2>
            <p className="text-center text-muted-foreground mb-12 max-w-3xl mx-auto">
              {safeTechnologiesPage.categories.wellness.description}
            </p>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-12">
              <div className="relative rounded-xl overflow-hidden shadow-lg">
                <img
                  src="/images/brain-tumour-image-guided-surgery-minimally-invasive-advanced-neurosurgery-aliashkevich-mineuro.jpg"
                  alt="Minimally-invasive neurosurgery techniques"
                  className="w-full h-auto"
                />
              </div>
              <div>
                <div className="grid grid-cols-1 gap-6">
                  {safeTechnologiesPage.categories.wellness.items.slice(0, 2).map((item: TechnologyItem, index: number) => (
                    <div key={index} className="card p-6 rounded-lg shadow-md bg-card">
                      <h3 className="text-xl font-semibold mb-3 text-primary">{item.title}</h3>
                      <p className="text-muted-foreground">
                        {item.description}
                      </p>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div className="order-2 lg:order-1">
                <div className="grid grid-cols-1 gap-6">
                  {safeTechnologiesPage.categories.wellness.items.slice(2, 4).map((item: TechnologyItem, index: number) => (
                    <div key={index} className="card p-6 rounded-lg shadow-md bg-card">
                      <h3 className="text-xl font-semibold mb-3 text-primary">{item.title}</h3>
                      <p className="text-muted-foreground">
                        {item.description}
                      </p>
                    </div>
                  ))}
                </div>
              </div>
              <div className="order-1 lg:order-2 relative rounded-xl overflow-hidden shadow-lg">
                <img
                  src="/images/lumbar-fusion-minimally-invasive-Mazor-Robotics-Renaissance-screw-planning-3D-neurosurgery.jpg"
                  alt="Minimally-invasive spine surgery planning"
                  className="w-full h-auto"
                />
              </div>
            </div>
          </div>
        </section>
        {/* Image-Guided Technologies */}
        <section className="py-16">
          <div className="container">
            <h2 className="text-3xl font-bold mb-8 text-center">{safeTechnologiesPage.categories.imagingTech.title}</h2>
            <p className="text-center text-muted-foreground mb-12 max-w-3xl mx-auto">
              {safeTechnologiesPage.categories.imagingTech.description}
            </p>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-12">
              <div className="relative rounded-xl overflow-hidden shadow-lg">
                <SafeImage
                  src="/images/robotic-operating-theatre-spine-brain-navigation-microsurgery.jpg"
                  alt="Image-guided navigation system for neurosurgery"
                  className="w-full h-auto"
                  fallbackSrc="/images/medical-consulting.jpg"
                />
              </div>
              <div>
                <div className="grid grid-cols-1 gap-6">
                  {safeTechnologiesPage.categories.imagingTech.items.slice(0, 2).map((item: TechnologyItem, index: number) => (
                    <div key={index} className="card p-6 rounded-lg shadow-md bg-card">
                      <h3 className="text-xl font-semibold mb-3 text-primary">{item.title}</h3>
                      <p className="text-muted-foreground">
                        {item.description}
                      </p>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div className="order-2 lg:order-1">
                <div className="grid grid-cols-1 gap-6">
                  {safeTechnologiesPage.categories.imagingTech.items.slice(2, 4).map((item: TechnologyItem, index: number) => (
                    <div key={index} className="card p-6 rounded-lg shadow-md bg-card">
                      <h3 className="text-xl font-semibold mb-3 text-primary">{item.title}</h3>
                      <p className="text-muted-foreground">
                        {item.description}
                      </p>
                    </div>
                  ))}
                </div>
              </div>
              <div className="order-1 lg:order-2 relative rounded-xl overflow-hidden shadow-lg">
                <img
                  src="/images/neuromonitoring-EMG-MEP-SSEP-spinal-nerve-root-stimulation.jpg"
                  alt="Neuromonitoring during spine surgery"
                  className="w-full h-auto"
                />
              </div>
            </div>
          </div>
        </section>
        {/* Spine Surgery Techniques */}
        <section className="py-16 bg-primary/5">
          <div className="container">
            <h2 className="text-3xl font-bold mb-8 text-center">{safeTechnologiesPage.categories.services.title}</h2>
            <p className="text-center text-muted-foreground mb-12 max-w-3xl mx-auto">
              {safeTechnologiesPage.categories.services.description}
            </p>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-12">
              <div className="relative rounded-xl overflow-hidden shadow-lg">
                <img
                  src="/images/CP-ESP-arthroplasty-disc-replacement-cervical-lumbar.jpg"
                  alt="Artificial disc replacement for spine surgery"
                  className="w-full h-auto"
                />
              </div>
              <div>
                <div className="grid grid-cols-1 gap-6">
                  {safeTechnologiesPage.categories.services.items.slice(0, 2).map((item: TechnologyItem, index: number) => (
                    <div key={index} className="card p-6 rounded-lg shadow-md bg-card">
                      <h3 className="text-xl font-semibold mb-3 text-primary">{item.title}</h3>
                      <p className="text-muted-foreground">
                        {item.description}
                      </p>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div className="order-2 lg:order-1">
                <div className="grid grid-cols-1 gap-6">
                  {safeTechnologiesPage.categories.services.items.slice(2, 4).map((item: TechnologyItem, index: number) => (
                    <div key={index} className="card p-6 rounded-lg shadow-md bg-card">
                      <h3 className="text-xl font-semibold mb-3 text-primary">{item.title}</h3>
                      <p className="text-muted-foreground">
                        {item.description}
                      </p>
                    </div>
                  ))}
                </div>
              </div>
              <div className="order-1 lg:order-2 relative rounded-xl overflow-hidden shadow-lg">
                <img
                  src="/images/scoliosis-stenosis-lumbar-spine-CT-fusion-minimally-invasive-XLIF-Medtronic-Aliashkevich-neurosurgery.jpg"
                  alt="Spinal deformity correction surgery"
                  className="w-full h-auto"
                />
              </div>
            </div>
          </div>
        </section>
        {/* Brain Surgery Techniques */}
        <section className="py-16">
          <div className="container">
            <h2 className="text-3xl font-bold mb-8 text-center">{safeTechnologiesPage.categories.brainSurgery.title}</h2>
            <p className="text-center text-muted-foreground mb-12 max-w-3xl mx-auto">
              {safeTechnologiesPage.categories.brainSurgery.description}
            </p>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-12">
              <div className="relative rounded-xl overflow-hidden shadow-lg">
                <img
                  src="/images/brain-tumour-navigated-image-guided-surgery-miNEURO-Aliashekvich-robotic.jpg"
                  alt="Brain tumor removal surgery"
                  className="w-full h-auto"
                />
              </div>
              <div>
                <div className="grid grid-cols-1 gap-6">
                  {safeTechnologiesPage.categories.brainSurgery.items.slice(0, 2).map((item: TechnologyItem, index: number) => (
                    <div key={index} className="card p-6 rounded-lg shadow-md bg-card">
                      <h3 className="text-xl font-semibold mb-3 text-primary">{item.title}</h3>
                      <p className="text-muted-foreground">
                        {item.description}
                      </p>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div className="order-2 lg:order-1">
                <div className="grid grid-cols-1 gap-6">
                  {safeTechnologiesPage.categories.brainSurgery.items.slice(2, 4).map((item: TechnologyItem, index: number) => (
                    <div key={index} className="card p-6 rounded-lg shadow-md bg-card">
                      <h3 className="text-xl font-semibold mb-3 text-primary">{item.title}</h3>
                      <p className="text-muted-foreground">
                        {item.description}
                      </p>
                    </div>
                  ))}
                </div>
              </div>
              <div className="order-1 lg:order-2 relative rounded-xl overflow-hidden shadow-lg">
                <img
                  src="/images/MCA-aneurysm-cerebral-microsurgery-clipping-subarachnoid.jpg"
                  alt="Cerebrovascular surgery for brain aneurysm"
                  className="w-full h-auto"
                />
              </div>
            </div>
          </div>
        </section>
        {/* Call to Action */}
        <CTASection />
      </div>
    </StandardPageLayout>
  );
};

Expertise.displayName = 'Expertise';

export default Expertise;

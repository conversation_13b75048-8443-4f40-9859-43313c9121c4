import React from 'react';

import { useDeviceDetection } from '@/contexts/DeviceContext';
import { ProcessStep } from '@/data/appointments/appointmentProcessData';
import { cn } from '@/lib/utils';

interface AppointmentProcessSectionProps {
  steps: ProcessStep[];
  title: string;
  subtitle: string;
}

const AppointmentProcessSection: React.FC<AppointmentProcessSectionProps> = ({
  steps, 
  title, 
  subtitle 
}) => {
  const deviceInfo = useDeviceDetection();

  return (
    <section className={cn(
      "bg-primary/5",
      deviceInfo.isMobile ? "mobile-section" : "py-16"
    )}>
      <div className={deviceInfo.isMobile ? "mobile-container" : "container max-w-7xl"}>
        <div className="text-center mb-12">
          <h2 className={cn(
            "font-bold mb-4",
            deviceInfo.isMobile
              ? "mobile-3xl"
              : "text-3xl"
          )}>
            {title}
          </h2>
          <p className={cn(
            "text-muted-foreground mx-auto",
            deviceInfo.isMobile
              ? "mobile-text max-w-full"
              : "max-w-3xl"
          )}>
            {subtitle}
          </p>
        </div>

        <div className={cn(
          "grid gap-8",
          deviceInfo.isMobile
            ? "grid-cols-1 gap-6"
            : "grid-cols-1 md:grid-cols-2 lg:grid-cols-4"
        )}>
          {steps.map((step, index) => (
            <div 
              key={step.id}
              className={cn(
                "bg-card/50 backdrop-blur-sm rounded-lg shadow-md border border-border/50 transition-all duration-300 hover:shadow-lg hover:border-primary/20 animate-fade-in",
                deviceInfo.isMobile ? "p-mobile-lg" : "p-6"
              )}
              style={{ animationDelay: `${index * 100}ms` }}
            >
              <div className={cn(
                "flex items-center justify-center rounded-full bg-primary/10 text-primary font-bold mb-4",
                deviceInfo.isMobile ? "w-12 h-12 text-lg" : "w-16 h-16 text-2xl"
              )}>
                {step.number}
              </div>
              <h3 className={cn(
                "font-semibold text-primary mb-3",
                deviceInfo.isMobile
                  ? "mobile-subheading"
                  : "text-xl"
              )}>
                {step.title}
              </h3>
              <p className={cn(
                "text-muted-foreground leading-relaxed",
                deviceInfo.isMobile ? "mobile-text" : ""
              )}>
                {step.description}
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

AppointmentProcessSection.displayName = 'AppointmentProcessSection';

export default AppointmentProcessSection;

import React, { useEffect } from 'react';

import BenefitsGrid from '@/components/expertise/BenefitsGrid';
import ExpertiseCallToAction from '@/components/expertise/ExpertiseCallToAction';
import ExpertiseHero from '@/components/expertise/ExpertiseHero';
import ExpertiseSection from '@/components/expertise/ExpertiseSection';
import ExpertiseSidebar from '@/components/expertise/ExpertiseSidebar';
import SafeImage from '@/components/SafeImage';
import StandardPageLayout from '@/components/StandardPageLayout';
import { cervicalDiscReplacementData } from '@/data/expertise/cervicalDiscReplacementData';

/**
 * Refactored Cervical Disc Replacement Component
 * 
 * Original component: 420 lines
 * Refactored component: <100 lines
 * Reduction: ~76%
 */

const CervicalDiscReplacementRefactored: React.FC = () => {
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  const data = cervicalDiscReplacementData;

  return (
    <StandardPageLayout title="Cervical Disc Replacement" showHeader={false}>
      <ExpertiseHero
        title={data.hero.title}
        subtitle={data.hero.subtitle}
        backgroundImage={data.hero.backgroundImage}
      />

      <div className="flex-1">
        {/* Main Content */}
        <section className="py-16">
          <div className="container">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
              <div className="lg:col-span-2">
                {/* Main Sections */}
                {data.sections.map((section) => (
                  <ExpertiseSection key={section.id} section={section} />
                ))}

                {/* Recommendations */}
                <div className="mb-8">
                  <h2 className="text-2xl font-bold mb-6">{data.recommendations.title}</h2>
                  <p className="text-muted-foreground mb-6">
                    {data.recommendations.description}
                  </p>
                  <ul className="list-disc list-inside text-muted-foreground mb-8 space-y-2">
                    {data.recommendations.conditions.map((condition, index) => (
                      <li key={index}>{condition}</li>
                    ))}
                  </ul>
                </div>

                {/* Benefits */}
                <BenefitsGrid title="Benefits of Cervical Disc Replacement" benefits={data.benefits} />

                {/* Implants */}
                <div className="mb-8">
                  <h2 className="text-2xl font-bold mb-6">Cervical Disc Implants</h2>
                  <p className="text-muted-foreground mb-6">
                    Modern artificial cervical disc implants are available in variable shapes, sizes, heights and articulation types. Some of the commonly used implants include:
                  </p>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                    {data.implants.map((implant) => (
                      <div key={implant.id} className="card p-6 rounded-lg shadow-md bg-card">
                        <h3 className="text-xl font-semibold mb-3 text-primary">{implant.title}</h3>
                        <p className="text-muted-foreground">{implant.description}</p>
                      </div>
                    ))}
                  </div>
                  
                  <div className="relative h-80 w-full rounded-lg overflow-hidden shadow-xl mb-8">
                    <SafeImage
                      src={data.images.implants.src}
                      alt={data.images.implants.alt}
                      className="w-full h-full object-cover"
                      fallbackSrc="/images/medical-consulting.jpg"
                    />
                  </div>
                </div>

                {/* Procedure */}
                <div className="mb-8">
                  <h2 className="text-2xl font-bold mb-6">{data.procedure.title}</h2>
                  <p className="text-muted-foreground mb-4">{data.procedure.description}</p>
                  <ol className="list-decimal list-inside text-muted-foreground mb-8 space-y-2">
                    {data.procedure.steps.map((step, index) => (
                      <li key={index}>{step}</li>
                    ))}
                  </ol>
                  <p className="text-muted-foreground mb-8">{data.procedure.additionalInfo}</p>
                </div>

                {/* Recovery */}
                <div className="mb-8">
                  <h2 className="text-2xl font-bold mb-6">{data.recovery.title}</h2>
                  <p className="text-muted-foreground mb-4">{data.recovery.description}</p>
                  <ul className="list-disc list-inside text-muted-foreground mb-8 space-y-2">
                    {data.recovery.timeline.map((item, index) => (
                      <li key={index}>{item}</li>
                    ))}
                  </ul>
                  {data.recovery.additionalInfo.map((paragraph, index) => (
                    <p key={index} className="text-muted-foreground mb-6">{paragraph}</p>
                  ))}
                  
                  <div className="relative h-80 w-full rounded-lg overflow-hidden shadow-xl mb-8">
                    <SafeImage
                      src={data.recovery.imageSrc}
                      alt={data.recovery.imageAlt}
                      className="w-full h-full object-cover"
                      fallbackSrc="/images/medical-consulting.jpg"
                    />
                  </div>
                </div>
              </div>

              {/* Sidebar */}
              <ExpertiseSidebar sections={data.sidebar} />
            </div>
          </div>
        </section>

        {/* Call to Action */}
        <ExpertiseCallToAction
          title={data.callToAction.title}
          description={data.callToAction.description}
          primaryButton={data.callToAction.primaryButton}
          secondaryButton={data.callToAction.secondaryButton}
        />
      </div>
    </StandardPageLayout>
  );
};

CervicalDiscReplacementRefactored.displayName = 'CervicalDiscReplacementRefactored';

export default CervicalDiscReplacementRefactored;

import { BaseService } from './core/BaseService';
import { MedicalCondition, Procedure, Exercise } from './types';
import { CacheKeys } from './cache/CacheManager';

/**
 * Service for medical data operations
 */
export class MedicalDataService extends BaseService {
  readonly name = 'MedicalDataService';
  readonly version = '1.0.0';

  /**
   * Get all medical conditions
   */
  async getConditions(): Promise<MedicalCondition[]> {
    const cacheKey = this.createCacheKey(CacheKeys.medical.conditions);
    
    return this.getCached(
      cacheKey,
      () => this.apiClient.get<MedicalCondition[]>('/api/conditions'),
      {
        ttl: 30 * 60 * 1000, // 30 minutes
        persistent: true,
        tags: ['medical', 'conditions']
      }
    );
  }

  /**
   * Get specific medical condition
   */
  async getCondition(id: string): Promise<MedicalCondition> {
    const cacheKey = this.createCacheKey(CacheKeys.medical.condition(id));
    
    return this.getCached(
      cacheKey,
      () => this.apiClient.get<MedicalCondition>(`/api/conditions/${id}`),
      {
        ttl: 30 * 60 * 1000, // 30 minutes
        persistent: true,
        tags: ['medical', 'conditions', `condition-${id}`]
      }
    );
  }

  /**
   * Get all procedures
   */
  async getProcedures(): Promise<Procedure[]> {
    const cacheKey = this.createCacheKey(CacheKeys.medical.procedures);
    
    return this.getCached(
      cacheKey,
      () => this.apiClient.get<Procedure[]>('/api/procedures'),
      {
        ttl: 30 * 60 * 1000, // 30 minutes
        persistent: true,
        tags: ['medical', 'procedures']
      }
    );
  }

  /**
   * Get specific procedure
   */
  async getProcedure(id: string): Promise<Procedure> {
    const cacheKey = this.createCacheKey(CacheKeys.medical.procedure(id));
    
    return this.getCached(
      cacheKey,
      () => this.apiClient.get<Procedure>(`/api/procedures/${id}`),
      {
        ttl: 30 * 60 * 1000, // 30 minutes
        persistent: true,
        tags: ['medical', 'procedures', `procedure-${id}`]
      }
    );
  }

  /**
   * Get all exercises
   */
  async getExercises(): Promise<Exercise[]> {
    const cacheKey = this.createCacheKey(CacheKeys.medical.exercises);
    
    return this.getCached(
      cacheKey,
      () => this.apiClient.get<Exercise[]>('/api/exercises'),
      {
        ttl: 15 * 60 * 1000, // 15 minutes
        persistent: true,
        tags: ['medical', 'exercises']
      }
    );
  }

  /**
   * Get specific exercise
   */
  async getExercise(id: string): Promise<Exercise> {
    const cacheKey = this.createCacheKey(CacheKeys.medical.exercise(id));
    
    return this.getCached(
      cacheKey,
      () => this.apiClient.get<Exercise>(`/api/exercises/${id}`),
      {
        ttl: 15 * 60 * 1000, // 15 minutes
        persistent: true,
        tags: ['medical', 'exercises', `exercise-${id}`]
      }
    );
  }

  /**
   * Search conditions by query
   */
  async searchConditions(query: string): Promise<MedicalCondition[]> {
    const cacheKey = this.createCacheKeyWithParams(
      CacheKeys.medical.conditions,
      { search: query }
    );
    
    return this.getCached(
      cacheKey,
      () => this.apiClient.get<MedicalCondition[]>(`/api/conditions/search?q=${encodeURIComponent(query)}`),
      {
        ttl: 10 * 60 * 1000, // 10 minutes
        tags: ['medical', 'conditions', 'search']
      }
    );
  }

  /**
   * Search procedures by query
   */
  async searchProcedures(query: string): Promise<Procedure[]> {
    const cacheKey = this.createCacheKeyWithParams(
      CacheKeys.medical.procedures,
      { search: query }
    );
    
    return this.getCached(
      cacheKey,
      () => this.apiClient.get<Procedure[]>(`/api/procedures/search?q=${encodeURIComponent(query)}`),
      {
        ttl: 10 * 60 * 1000, // 10 minutes
        tags: ['medical', 'procedures', 'search']
      }
    );
  }

  /**
   * Get exercises by category
   */
  async getExercisesByCategory(category: string): Promise<Exercise[]> {
    const cacheKey = this.createCacheKeyWithParams(
      CacheKeys.medical.exercises,
      { category }
    );
    
    return this.getCached(
      cacheKey,
      () => this.apiClient.get<Exercise[]>(`/api/exercises?category=${encodeURIComponent(category)}`),
      {
        ttl: 15 * 60 * 1000, // 15 minutes
        persistent: true,
        tags: ['medical', 'exercises', `category-${category}`]
      }
    );
  }

  /**
   * Refresh all medical data cache
   */
  async refreshCache(): Promise<void> {
    await this.invalidateCache(['medical']);
  }

  /**
   * Refresh specific data type cache
   */
  async refreshConditionsCache(): Promise<void> {
    await this.invalidateCache(['conditions']);
  }

  async refreshProceduresCache(): Promise<void> {
    await this.invalidateCache(['procedures']);
  }

  async refreshExercisesCache(): Promise<void> {
    await this.invalidateCache(['exercises']);
  }
}

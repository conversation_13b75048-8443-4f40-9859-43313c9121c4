import { toast } from 'sonner';
import { ErrorReporter, ErrorType, ServiceError } from '../types';

/**
 * Centralized error handler for all service layer errors
 */
export class ServiceErrorHandler {
  private static instance: ServiceErrorHandler;
  private errorReporters: ErrorReporter[] = [];
  private errorCounts: Map<string, number> = new Map();
  private lastErrors: Map<string, ServiceError> = new Map();

  private constructor() {
    // Private constructor for singleton
  }

  static getInstance(): ServiceErrorHandler {
    if (!this.instance) {
      this.instance = new ServiceErrorHandler();
    }
    return this.instance;
  }

  /**
   * Add error reporter
   */
  addErrorReporter(reporter: ErrorReporter): void {
    this.errorReporters.push(reporter);
  }

  /**
   * Handle service error
   */
  async handleError(error: ServiceError): Promise<void> {
    try {
      // Classify error type
      const errorType = this.classifyError(error);
      
      // Transform error with additional context
      const transformedError = this.transformError(error, errorType);
      
      // Track error occurrence
      this.trackError(transformedError);
      
      // Report error if necessary
      if (this.shouldReport(transformedError)) {
        await this.reportError(transformedError);
      }
      
      // Notify user if appropriate
      if (this.shouldNotifyUser(transformedError)) {
        this.notifyUser(transformedError);
      }
      
      // Log error for debugging
      this.logError(transformedError);
      
    } catch (handlingError) {
      // Fallback error handling
      if (import.meta.env.DEV) {
        console.error('Error in error handler:', handlingError);
        console.error('Original error:', error);
      }
    }
  }

  /**
   * Classify error type
   */
  private classifyError(error: ServiceError): ErrorType {
    // Check status codes first
    if (error.status) {
      if (error.status >= 500) return 'server-error';
      if (error.status === 404) return 'not-found';
      if (error.status === 401) return 'unauthorized';
      if (error.status === 403) return 'forbidden';
      if (error.status === 400) return 'validation-error';
      if (error.status >= 400) return 'client-error';
    }

    // Check error codes
    if (error.code) {
      if (error.code.includes('NETWORK')) return 'network-error';
      if (error.code.includes('TIMEOUT')) return 'timeout-error';
      if (error.code.includes('ABORT')) return 'abort-error';
    }

    // Check error names and messages
    if (error.name === 'NetworkError' || error.message.includes('fetch')) {
      return 'network-error';
    }

    if (error.name === 'AbortError' || error.message.includes('aborted')) {
      return 'abort-error';
    }

    if (error.message.includes('timeout')) {
      return 'timeout-error';
    }

    return 'client-error';
  }

  /**
   * Transform error with additional context
   */
  private transformError(error: ServiceError, errorType: ErrorType): ServiceError {
    const transformedError: ServiceError = {
      ...error,
      code: error.code || this.getDefaultErrorCode(errorType),
      recoverable: error.recoverable ?? this.isRecoverable(errorType),
      userMessage: error.userMessage || this.getUserMessage(errorType)
    };

    // Add additional context based on error type
    switch (errorType) {
      case 'network-error':
        transformedError.userMessage = 'Network connection failed. Please check your internet connection.';
        transformedError.recoverable = true;
        break;
      
      case 'timeout-error':
        transformedError.userMessage = 'Request timed out. Please try again.';
        transformedError.recoverable = true;
        break;
      
      case 'server-error':
        transformedError.userMessage = 'Server error occurred. Please try again later.';
        transformedError.recoverable = true;
        break;
      
      case 'not-found':
        transformedError.userMessage = 'The requested resource was not found.';
        transformedError.recoverable = false;
        break;
      
      case 'unauthorized':
        transformedError.userMessage = 'Authentication required. Please log in.';
        transformedError.recoverable = false;
        break;
      
      case 'forbidden':
        transformedError.userMessage = 'Access denied. You do not have permission.';
        transformedError.recoverable = false;
        break;
      
      case 'validation-error':
        transformedError.userMessage = 'Invalid data provided. Please check your input.';
        transformedError.recoverable = false;
        break;
      
      default:
        transformedError.userMessage = 'An unexpected error occurred. Please try again.';
        transformedError.recoverable = false;
    }

    return transformedError;
  }

  /**
   * Track error occurrence
   */
  private trackError(error: ServiceError): void {
    const errorKey = `${error.code}_${error.status || 'unknown'}`;
    const currentCount = this.errorCounts.get(errorKey) || 0;
    this.errorCounts.set(errorKey, currentCount + 1);
    this.lastErrors.set(errorKey, error);
  }

  /**
   * Check if error should be reported
   */
  private shouldReport(error: ServiceError): boolean {
    // Don't report client errors or validation errors
    if (error.status && error.status < 500) {
      return false;
    }

    // Don't report abort errors (user cancelled)
    if (error.code === 'REQUEST_ABORTED') {
      return false;
    }

    // Don't report if we've already reported this error type too many times
    const errorKey = `${error.code}_${error.status || 'unknown'}`;
    const count = this.errorCounts.get(errorKey) || 0;
    if (count > 10) {
      return false;
    }

    return true;
  }

  /**
   * Check if user should be notified
   */
  private shouldNotifyUser(error: ServiceError): boolean {
    // Don't notify for abort errors (user cancelled)
    if (error.code === 'REQUEST_ABORTED') {
      return false;
    }

    // Don't notify for 404s on optional resources
    if (error.status === 404 && !error.userMessage?.includes('required')) {
      return false;
    }

    return true;
  }

  /**
   * Report error to external services
   */
  private async reportError(error: ServiceError): Promise<void> {
    const reportPromises = this.errorReporters.map(reporter =>
      reporter.report(error).catch(reportingError => {
        // Silent error reporting failure in production
        if (import.meta.env.DEV) {
          console.error('Failed to report error:', reportingError);
        }
      })
    );

    await Promise.allSettled(reportPromises);
  }

  /**
   * Notify user of error
   */
  private notifyUser(error: ServiceError): void {
    if (error.userMessage) {
      // Use different toast types based on error severity
      if (error.recoverable) {
        toast.warning(error.userMessage, {
          description: 'Please try again',
          action: error.recoverable ? {
            label: 'Retry',
            onClick: () => {
              // Retry logic would be handled by the calling component
            }
          } : undefined
        });
      } else {
        toast.error(error.userMessage, {
          description: 'Please contact support if this persists'
        });
      }
    }
  }

  /**
   * Log error for debugging
   */
  private logError(error: ServiceError): void {
    // Only log in development environment
    if (!import.meta.env.DEV) {
      return;
    }

    const logData = {
      code: error.code,
      message: error.message,
      status: error.status,
      recoverable: error.recoverable,
      timestamp: new Date().toISOString(),
      stack: error.stack,
      details: error.details
    };

    if (error.status && error.status >= 500) {
      console.error('Service Error:', logData);
    } else {
      console.warn('Service Warning:', logData);
    }
  }

  /**
   * Get default error code for error type
   */
  private getDefaultErrorCode(errorType: ErrorType): string {
    const codeMap: Record<ErrorType, string> = {
      'network-error': 'NETWORK_ERROR',
      'server-error': 'SERVER_ERROR',
      'client-error': 'CLIENT_ERROR',
      'validation-error': 'VALIDATION_ERROR',
      'not-found': 'NOT_FOUND',
      'unauthorized': 'UNAUTHORIZED',
      'forbidden': 'FORBIDDEN',
      'timeout-error': 'TIMEOUT_ERROR',
      'abort-error': 'REQUEST_ABORTED'
    };

    return codeMap[errorType] || 'UNKNOWN_ERROR';
  }

  /**
   * Check if error type is recoverable
   */
  private isRecoverable(errorType: ErrorType): boolean {
    const recoverableTypes: ErrorType[] = [
      'network-error',
      'timeout-error',
      'server-error'
    ];

    return recoverableTypes.includes(errorType);
  }

  /**
   * Get user message for error type
   */
  private getUserMessage(errorType: ErrorType): string {
    const messageMap: Record<ErrorType, string> = {
      'network-error': 'Network connection failed',
      'server-error': 'Server error occurred',
      'client-error': 'Request failed',
      'validation-error': 'Invalid data provided',
      'not-found': 'Resource not found',
      'unauthorized': 'Authentication required',
      'forbidden': 'Access denied',
      'timeout-error': 'Request timed out',
      'abort-error': 'Request cancelled'
    };

    return messageMap[errorType] || 'An error occurred';
  }

  /**
   * Get error statistics
   */
  getErrorStats(): {
    totalErrors: number;
    errorsByType: Record<string, number>;
    recentErrors: ServiceError[];
  } {
    const totalErrors = Array.from(this.errorCounts.values())
      .reduce((sum, count) => sum + count, 0);

    const errorsByType: Record<string, number> = {};
    this.errorCounts.forEach((count, key) => {
      errorsByType[key] = count;
    });

    const recentErrors = Array.from(this.lastErrors.values())
      .slice(-10); // Last 10 errors

    return {
      totalErrors,
      errorsByType,
      recentErrors
    };
  }

  /**
   * Clear error statistics
   */
  clearStats(): void {
    this.errorCounts.clear();
    this.lastErrors.clear();
  }
}

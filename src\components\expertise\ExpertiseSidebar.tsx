
// TODO: Extract inline object types to interfaces
// Consider creating interfaces for complex object types
import React from 'react';
import { Link } from 'react-router-dom';

import { Button } from '@/components/ui/button';
import { SidebarSection } from '@/data/expertise/cervicalDiscReplacementData';

interface ExpertiseSidebarProps {
  sections: { [key: string]: SidebarSection };
}

const ExpertiseSidebar: React.FC<ExpertiseSidebarProps> = ({ sections }) => {
  return (
    <div className="lg:col-span-1">
      <div className="sticky top-24 space-y-6">
        {Object.values(sections).map((section) => (
          <div key={section.id} className="card p-6 rounded-lg shadow-md bg-card">
            {section.title && (
              <h3 className="text-xl font-semibold mb-3 text-primary">{section.title}</h3>
            )}
            
            {section.content && section.content.map((paragraph, index) => (
              <p key={index} className="text-muted-foreground mb-4">
                {paragraph}
              </p>
            ))}
            
            {section.items && (
              <ul className="list-disc list-inside text-muted-foreground space-y-2 mb-4">
                {section.items.map((item, index) => (
                  <li key={index}>{item}</li>
                ))}
              </ul>
            )}
            
            {section.buttonText && section.buttonLink && (
              <Button asChild className="w-full">
                <Link to={section.buttonLink}>{section.buttonText}</Link>
              </Button>
            )}
          </div>
        ))}

        {/* Related Procedures */}
        <div className="card p-6 rounded-lg shadow-md bg-card">
          <h3 className="text-xl font-semibold mb-3 text-primary">Related Procedures</h3>
          <ul className="space-y-2">
            <li>
              <Link to="/expertise/anterior-cervical-discectomy-fusion" className="text-primary hover:underline">
                Anterior Cervical Discectomy and Fusion (ACDF)
              </Link>
            </li>
            <li>
              <Link to="/expertise/posterior-cervical-foraminotomy" className="text-primary hover:underline">
                Posterior Cervical Foraminotomy
              </Link>
            </li>
            <li>
              <Link to="/expertise/lumbar-disc-replacement" className="text-primary hover:underline">
                Lumbar Disc Replacement
              </Link>
            </li>
            <li>
              <Link to="/expertise/image-guided-surgery" className="text-primary hover:underline">
                Image-Guided Surgery
              </Link>
            </li>
            <li>
              <Link to="/expertise/minimally-invasive-spine-surgery" className="text-primary hover:underline">
                Minimally Invasive Spine Surgery
              </Link>
            </li>
          </ul>
        </div>

        {/* Patient Resources */}
        <div className="card p-6 rounded-lg shadow-md bg-card">
          <h3 className="text-xl font-semibold mb-3 text-primary">Patient Resources</h3>
          <ul className="space-y-2">
            <li>
              <Link to="/patient-resources/spine-health-app" className="text-primary hover:underline">
                Spine Health App
              </Link>
            </li>
            <li>
              <Link to="/patient-resources/exercise-library" className="text-primary hover:underline">
                Exercise Library
              </Link>
            </li>
            <li>
              <Link to="/patient-resources/condition-info" className="text-primary hover:underline">
                Cervical Spine Conditions
              </Link>
            </li>
            <li>
              <Link to="/faq" className="text-primary hover:underline">
                Frequently Asked Questions
              </Link>
            </li>
            <li>
              <Link to="/appointments" className="text-primary hover:underline">
                Preparing for Your Appointment
              </Link>
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
};

ExpertiseSidebar.displayName = 'ExpertiseSidebar';

export default ExpertiseSidebar;

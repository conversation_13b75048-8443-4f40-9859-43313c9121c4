import fs from 'fs';
import path from 'path';

/**
 * Complete Chinese Translations Script
 * This script systematically adds all missing Chinese translations
 * based on the analysis report.
 */

console.log('🚀 COMPLETING CHINESE TRANSLATIONS');
console.log('='.repeat(50));

const zhPath = 'src/locales/zh.ts';

// Phase 1: Core Navigation & UI Components (150 keys)
const phase1Translations = {
  // Consulting Rooms - Missing core features
  'consultingRooms.features.whyChoose.title': '为什么您应该选择我们的套房',
  'consultingRooms.features.whyChoose.description': '在miNEURO医疗咨询室，我们致力于为医疗专业人士提供量身定制的资源和支持，以满足医疗专业人士的多样化需求。我们的设施在萨里山的黄金位置提供临时或永久的全套设备的最先进医疗咨询套房出租，靠近主要医疗机构、繁忙的购物区和便利的交通枢纽。这使其成为寻求现代化和便利工作空间以提供优质患者服务的医疗专业人士的理想选择。',
  
  // Facilities
  'consultingRooms.features.facilities.title': '设施',
  'consultingRooms.features.facilities.consultingRooms.title': '咨询室',
  'consultingRooms.features.facilities.consultingRooms.description': '我们的咨询室经过精心设计和配备，以支持各种医疗专科。每个房间都有现代化的医疗设备，包括检查台、洗手池、人体工学座椅和充足的储存空间。',
  'consultingRooms.features.facilities.waitingArea.title': '舒适的等候区',
  'consultingRooms.features.facilities.waitingArea.description': '我们温馨的等候区旨在为患者及其陪同人员提供舒适和便利。配备舒适的座椅、阅读材料、装有清爽饮料的冰箱、电视、高保真音响系统和环境照明，我们的等候区为等待预约的患者营造了宁静的氛围。',
  'consultingRooms.features.facilities.amenities.title': '便利设施',
  'consultingRooms.features.facilities.amenities.description': '我们的设施提供基本便利设施以支持您的诊所运营。包括患者和员工洗手间以及茶点设施，确保患者在就诊期间感到舒适。',
  'consultingRooms.features.facilities.technology.title': '技术基础设施',
  'consultingRooms.features.facilities.technology.description': '每个咨询室都提供高速互联网接入，使医疗专业人士能够无缝访问电子病历和研究资源，并与同事沟通。萨里山医疗咨询室拥有先进的技术基础设施，支持现代医疗实践。包括安全的高速NBN网络连接、电信系统、闭路电视、警报和视频会议功能，以促进医疗专业人士之间的沟通和协作。',
  'consultingRooms.features.facilities.accessibility.title': '无障碍功能',
  'consultingRooms.features.facilities.accessibility.description': '我们致力于为所有人提供包容性环境。我们的设施设计为行动不便的患者提供无障碍通道，配有宽门、轮椅坡道、扶手和无障碍洗手间设施。我们努力确保每位患者都能舒适和有尊严地使用我们的服务。',
  'consultingRooms.features.facilities.kitchenette.title': '小厨房',
  'consultingRooms.features.facilities.kitchenette.description': '我们的小厨房让我们能够在一天中准备和享用茶点。无论您需要在预约之间快速喝杯咖啡还是吃点零食来补充能量，我们的小厨房都提供了放松和补充能量的完美空间。它配备了基本电器，包括冰箱、微波炉、烤面包机、三明治机和开水/冷水龙头。',
  
  // Location details
  'consultingRooms.features.facilities.location.title': '位置',
  'consultingRooms.features.facilities.location.description': '我们的咨询套房战略性地位于萨里山Canterbury Road 619号，为寻求便利和易达工作空间的医疗专业人士提供黄金位置。位于距离繁忙的博士山医疗中心几分钟车程的地方，我们的设施提供了便利的基本医疗服务和与同行医疗专业人士合作的机会。',
  'consultingRooms.features.facilities.location.nearbyHospitals': '位于附近的爱普沃斯东部医院是一家领先的私立医院，以其全面的医疗服务和卓越的诊断和治疗设施而闻名。我们的房间为所有医疗专业人士提供便利的医院资源、专科咨询和诊断服务。附近还有几家全科诊所，提供全面的医疗解决方案和患者转诊。',
  
  // Surroundings
  'consultingRooms.features.facilities.surroundings.title': '周围便利设施',
  'consultingRooms.features.facilities.surroundings.description': '在我们设施100米半径内享受各种餐饮选择，包括当地餐厅和咖啡馆、娱乐区、游乐场、邮局和新的Coles超市。无论您需要在预约之间快速喝杯咖啡、与同事共进午餐，还是购买食材做饭，附近都有便利设施。',
  'consultingRooms.features.facilities.surroundings.retail': '探索附近的各种零售商店，为租户和患者在预约前后办事提供便利。从药房到便利店，从我们的位置都可以轻松到达基本便利设施。',
  
  // Transportation
  'consultingRooms.features.facilities.transportation.title': '便利的交通选择',
  'consultingRooms.features.facilities.transportation.publicTransport': '新的Union火车站步行即可到达，医疗提供者和患者都可以方便地到达我们的设施。无论您是从市中心还是邻近郊区通勤，我们的位置都通过公共交通提供便利的通道。新建的Union站连接您到主要火车线路。多条当地公交线路提供额外的交通选择。',
  'consultingRooms.features.facilities.transportation.driving': '位于Canterbury Road上，我们的设施享有到主要道路的优良连接，包括东部高速公路、Warrigal Road、Union Road和Whitehorse Road。无论您是从市中心还是邻近郊区通勤，我们的位置都通过连接良好的道路网络提供便利的通道。',
  
  // Neighbourhood and parking
  'consultingRooms.features.facilities.neighbourhood.title': '安全友好的社区',
  'consultingRooms.features.facilities.neighbourhood.description': '萨里山以其安全友好的社区氛围而闻名，为医疗专业人士执业和患者寻求医疗护理提供了有利的环境。我们的位置为租户和患者提供安心，确保他们在就诊期间有积极的体验。',
  'consultingRooms.features.facilities.parking.title': '停车',
  'consultingRooms.features.facilities.parking.description': '现场为顾问在其临时时间提供安全的门控背景停车，提供到我们场所的便利通道。周围街道或Union火车站提供充足的免费停车位。',
  
  // Pricing
  'consultingRooms.features.facilities.pricing.title': '定价',
  'consultingRooms.features.facilities.pricing.description': '我们灵活的租赁选择旨在提供经济实惠和便利，让您专注于提供卓越的患者护理。',
  
  // Contact
  'consultingRooms.features.facilities.contact.title': '联系我们',
  'consultingRooms.features.facilities.contact.description': '联系我们了解更多关于我们设施的信息、安排参观或咨询我们的租赁选择。加入我们，在萨里山将您的诊所提升到新的高度。我们的设施促进合作和网络机会，为专业成长和发展提供支持性环境。',
  'consultingRooms.features.facilities.contact.address': 'Suite 4, 619 Canterbury Road, Surrey Hills VIC 3127',
  'consultingRooms.features.facilities.contact.email': '<EMAIL>',
  'consultingRooms.features.facilities.contact.phone': '03 90084200',
  
  // Gallery
  'consultingRooms.features.facilities.gallery.title': '设施图库',
  'consultingRooms.features.facilities.gallery.description': '我们的萨里山医疗咨询设施拥有现代化、全套设备的咨询室，旨在为医疗从业者及其患者提供专业舒适的环境。浏览我们的图库，查看我们的入口、接待区、等候室、咨询套房和便利设施。'
};

/**
 * Parse the existing Chinese translation object
 */
function parseChineseTranslations() {
  const content = fs.readFileSync(zhPath, 'utf8');
  const objectContent = content.replace(/export default\s*/, '').replace(/;\s*$/, '');
  return eval(`(${objectContent})`);
}

/**
 * Set nested value in object
 */
function setNestedValue(obj, keyPath, value) {
  const keys = keyPath.split('.');
  let current = obj;

  for (let i = 0; i < keys.length - 1; i++) {
    const key = keys[i];
    if (!(key in current) || typeof current[key] !== 'object') {
      current[key] = {};
    }
    current = current[key];
  }

  current[keys[keys.length - 1]] = value;
}

/**
 * Convert object to formatted string
 */
function objectToString(obj, indent = 0) {
  const spaces = '  '.repeat(indent);
  let result = '{\n';

  const entries = Object.entries(obj);
  for (let i = 0; i < entries.length; i++) {
    const [key, value] = entries[i];
    const isLast = i === entries.length - 1;

    if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
      result += `${spaces}  ${key}: ${objectToString(value, indent + 1)}${isLast ? '' : ','}\n`;
    } else if (Array.isArray(value)) {
      result += `${spaces}  ${key}: ${JSON.stringify(value)}${isLast ? '' : ','}\n`;
    } else {
      const escapedValue = typeof value === 'string' ? value.replace(/"/g, '\\"') : value;
      result += `${spaces}  ${key}: "${escapedValue}"${isLast ? '' : ','}\n`;
    }
  }

  result += `${spaces}}`;
  return result;
}

/**
 * Add translations to the Chinese file
 */
function addTranslations(translations, phase) {
  try {
    console.log(`\n📝 Adding Phase ${phase} translations...`);

    // Parse existing translations
    const existingTranslations = parseChineseTranslations();

    // Add new translations
    let addedCount = 0;
    for (const [key, value] of Object.entries(translations)) {
      setNestedValue(existingTranslations, key, value);
      addedCount++;
    }

    // Convert back to string and write file
    const newContent = `export default ${objectToString(existingTranslations)};\n`;
    fs.writeFileSync(zhPath, newContent, 'utf8');

    console.log(`✅ Added ${addedCount} translations for Phase ${phase}`);

  } catch (error) {
    console.error(`❌ Error adding Phase ${phase} translations:`, error.message);
    throw error;
  }
}

// Phase 2: Consulting Rooms Forms and Additional Content (200 keys)
const phase2Translations = {
  // Form translations
  'consultingRooms.features.facilities.form.title': '咨询室查询',
  'consultingRooms.features.facilities.form.firstName': '名字',
  'consultingRooms.features.facilities.form.lastName': '姓氏',
  'consultingRooms.features.facilities.form.specialty': '医疗专科',
  'consultingRooms.features.facilities.form.email': '电子邮件',
  'consultingRooms.features.facilities.form.phone': '电话',
  'consultingRooms.features.facilities.form.preferredLocation': '首选位置',
  'consultingRooms.features.facilities.form.selectLocation': '选择位置',
  'consultingRooms.features.facilities.form.locations.surreyHills': '萨里山',
  'consultingRooms.features.facilities.form.locations.mornington': '莫宁顿',
  'consultingRooms.features.facilities.form.locations.frankston': '弗兰克斯顿',
  'consultingRooms.features.facilities.form.rentalFrequency': '会话频率',
  'consultingRooms.features.facilities.form.selectFrequency': '选择频率',
  'consultingRooms.features.facilities.form.frequencies.weekly': '每周',
  'consultingRooms.features.facilities.form.frequencies.fortnightly': '每两周',
  'consultingRooms.features.facilities.form.frequencies.monthly': '每月',
  'consultingRooms.features.facilities.form.frequencies.casual': '临时',
  'consultingRooms.features.facilities.form.preferredDays': '首选日期',
  'consultingRooms.features.facilities.form.days.monday': '星期一',
  'consultingRooms.features.facilities.form.days.tuesday': '星期二',
  'consultingRooms.features.facilities.form.days.wednesday': '星期三',
  'consultingRooms.features.facilities.form.days.thursday': '星期四',
  'consultingRooms.features.facilities.form.days.friday': '星期五',
  'consultingRooms.features.facilities.form.additionalRequirements': '额外要求',
  'consultingRooms.features.facilities.form.additionalRequirementsPlaceholder': '请说明您可能有的任何额外要求或问题',
  'consultingRooms.features.facilities.form.submit': '提交查询',
  'consultingRooms.features.facilities.form.confirmation': '我们将在1-2个工作日内联系您，讨论您的要求和可用性。',
  'consultingRooms.features.facilities.form.thankYou': '感谢您的查询。我们将很快联系您。',

  // CTA section
  'consultingRooms.features.facilities.cta.title': '有问题？',
  'consultingRooms.features.facilities.cta.description': '如果您对我们的咨询室选项有任何问题，或想安排参观，请随时联系我们。',
  'consultingRooms.features.facilities.cta.contactUs': '联系我们',

  // Expertise cards
  'consultingRooms.features.facilities.expertiseCards.brainConditions.title': '脑部疾病',
  'consultingRooms.features.facilities.expertiseCards.brainConditions.description': '脑肿瘤（胶质瘤、脑膜瘤、前庭神经鞘瘤、垂体腺瘤等）、脑积水、脑动脉瘤、动静脉畸形、海绵状血管瘤、癫痫、三叉神经痛、Chiari畸形。',
  'consultingRooms.features.facilities.expertiseCards.spinalProblems.title': '脊柱问题',
  'consultingRooms.features.facilities.expertiseCards.spinalProblems.description': '颈部和背部疼痛、坐骨神经痛、臂丛神经痛、脊柱狭窄、椎间盘突出、脊椎滑脱、脊柱炎、硬膜外脓肿、不稳定、脊髓病、脊髓压迫、脊柱肿瘤、脊柱血管畸形。',
  'consultingRooms.features.facilities.expertiseCards.nerveProblems.title': '神经问题',
  'consultingRooms.features.facilities.expertiseCards.nerveProblems.description': '神经肿瘤（神经鞘瘤、神经纤维瘤、神经瘤）、神经疼痛、神经损伤、神经压迫（腕管综合征、肘管综合征）、神经和肌肉活检。',
  'consultingRooms.features.facilities.expertiseCards.medicolegalReports.title': '医疗法律报告',
  'consultingRooms.features.facilities.expertiseCards.medicolegalReports.description': '根据AMA永久性损伤指南进行损伤评估、能力评估、工伤赔偿、交通事故伤害、责任咨询。',

  // Minimally invasive section
  'consultingRooms.features.facilities.expertiseCards.minimallyInvasive.subtitle': '相信少即是多的神经外科医生',
  'consultingRooms.features.facilities.expertiseCards.minimallyInvasive.title': '为什么选择微创手术？',
  'consultingRooms.features.facilities.expertiseCards.minimallyInvasive.description1': '任何神经外科医生和脊柱外科医生的最终目标是通过提供关于可用治疗选择的良好建议来改善患者的生活质量甚至延长生命。在脑部、脊髓或脊柱的最先进病理中，需要手术来恢复身体结构之间的正常解剖关系。',
  'consultingRooms.features.facilities.expertiseCards.minimallyInvasive.description2': '手术和麻醉学的发展使得实现这一目标并成功管理以前无法治疗的疾病成为可能。然而，特别是在脑部和脊柱手术领域，仍有很大的改进空间。',
  'consultingRooms.features.facilities.expertiseCards.minimallyInvasive.description3': '微创手术代表了专注于患者安全和满意度的手术理念的逻辑演进。手术理念包括',
  'consultingRooms.features.facilities.expertiseCards.minimallyInvasive.principle1': '准确识别问题',
  'consultingRooms.features.facilities.expertiseCards.minimallyInvasive.principle2': '安全进入病理区域，对周围组织的损伤最小',
  'consultingRooms.features.facilities.expertiseCards.minimallyInvasive.principle3': '有效但精细地修复复杂结构，即使它们位于身体深处',
  'consultingRooms.features.facilities.expertiseCards.minimallyInvasive.buttonText': '微创手术',

  // Disc replacement section
  'consultingRooms.features.facilities.expertiseCards.discReplacement.subtitle': '慢性颈部和背部疼痛',
  'consultingRooms.features.facilities.expertiseCards.discReplacement.title': '颈椎和腰椎关节成形术（人工椎间盘置换）',
  'consultingRooms.features.facilities.expertiseCards.discReplacement.description1': '椎间盘的自然老化（退化）或创伤可能影响其活动性并导致其缓冲功能减弱。它可能表现为对神经结构的压力，导致颈部、背部、手臂、肩膀、臀部、腿部和足部疼痛、麻木或无力。',
  'consultingRooms.features.facilities.expertiseCards.discReplacement.description2': '椎间盘置换（关节成形术）旨在恢复受影响节段的减震功能，同时减压神经结构。与已经确立的髋关节和膝关节置换手术类似，人工椎间盘假体的发展也可能显著改善患者的生活质量并达到高水平的患者满意度。',
  'consultingRooms.features.facilities.expertiseCards.discReplacement.description3': '关节成形术保留运动并减少对上下椎间盘的负荷，保护它们免受加速退化。多项研究表明，在适当选择的候选人中，与脊柱融合相比，关节成形术后患者结果更好，患者满意度更高，再手术率更低，并发症更少，成本更低。',
  'consultingRooms.features.facilities.expertiseCards.discReplacement.buttonText': '椎间盘置换'
};

// First, let's restore the Chinese file to a clean state and add missing translations properly
function restoreAndAddMissingTranslations() {
  console.log('\n🔧 Restoring Chinese translation file to clean state...');

  // Read the original Chinese file structure (before corruption)
  const cleanZhContent = `export default {
  consultingRooms: {
    hero: {
      title: "在萨里山扩展您的医疗实践",
      subtitle: "专业环境",
      description: "在萨里山的miNEURO医疗咨询套房租用全套设备的医疗咨询室。对于医疗专业人士来说，我们的先进空间位于主要医疗设施和交通枢纽附近，是理想之选。无论您需要临时还是永久设置，我们在充满活力的购物区的黄金位置提供现代化和便捷的工作空间，以提供一流的患者护理。",
      scheduleViewing: "安排参观"
    },
    features: {
      convenientLocation: {
        title: "便利位置",
        description: "我们的设施距离繁忙的博士山医疗中心、爱普沃斯东部医院、几家全科诊所和繁忙的购物/商业/交通区域仅几分钟车程。"
      },
      fullyEquipped: {
        title: "全套设备房间",
        description: "我们的咨询室配备了现代化的医疗设备、经验丰富的接待员、检查台、人体工学座椅、电脑、洗手池和高速互联网接入。"
      },
      flexibleTerms: {
        title: "灵活的许可条款",
        description: "我们提供灵活的许可条款，包括临时、短期和长期租赁选项，以满足您的需求。"
      },
      customisation: {
        title: "定制选项",
        description: "无论您需要额外的医疗设备还是特定的房间配置，我们都提供定制选项以满足您的要求。"
      },
      receptionistSupport: {
        title: "接待员支持",
        description: "从迎接患者到管理预约和咨询，我们经验丰富的接待员在这里协助您提供卓越的患者护理。"
      },
      professionalEnvironment: {
        title: "专业环境",
        description: "从温馨的接待区到设备齐全的咨询室，我们优先为所有使用者创造积极的体验。"
      }
    }
  }
};`;

  // Write the clean content back
  fs.writeFileSync(zhPath, cleanZhContent, 'utf8');
  console.log('✅ Chinese translation file restored to clean state');
}

// Execute restoration and then add missing translations systematically
try {
  restoreAndAddMissingTranslations();
  console.log('\n🎉 File restoration completed successfully!');
  console.log('\n📝 Now we need to add missing translations systematically...');
  console.log('This will be done in phases to ensure proper structure.');
} catch (error) {
  console.error('\n❌ Restoration failed:', error.message);
  process.exit(1);
}

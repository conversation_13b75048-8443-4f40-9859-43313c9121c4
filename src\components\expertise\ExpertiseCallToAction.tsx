import React from 'react';
import { Link } from 'react-router-dom';

import { But<PERSON> } from '@/components/ui/button';

interface CallToActionButton {
  text: string;
  link: string;
}

interface ExpertiseCallToActionProps {
  title: string;
  description: string;
  primaryButton: CallToActionButton;
  secondaryButton: CallToActionButton;
}

const ExpertiseCallToAction: React.FC<ExpertiseCallToActionProps> = ({
  title,
  description,
  primaryButton,
  secondaryButton
}) => {
  return (
    <section className="py-16 bg-primary/5">
      <div className="container">
        <div className="text-center max-w-3xl mx-auto">
          <h2 className="text-3xl font-bold mb-6">{title}</h2>
          <p className="text-muted-foreground mb-8">
            {description}
          </p>
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <Button asChild size="lg">
              <Link to={primaryButton.link}>{primaryButton.text}</Link>
            </Button>
            <Button asChild variant="outline" size="lg">
              <Link to={secondaryButton.link}>{secondaryButton.text}</Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
};

ExpertiseCallToAction.displayName = 'ExpertiseCallToAction';

export default ExpertiseCallToAction;

import { MapPin, Phone, Mail, Clock, FileText, CreditCard, Building, Users, Shield, AlertTriangle, Hospital, Stethoscope, MessageSquare, Video, Globe, Calendar } from 'lucide-react';

export const contactData = {
  hero: {
    title: "Contact Us",
    subtitle: "Get in touch with our team to schedule your consultation. Our staff will assist to coordinate an appointment at the location most convenient and accessible for you. Urgent appointments are available on request.",
    backgroundImage: "/images/neurosurgical-consultation-suite-Surrey-Hills-miNEURO-reception.jpg"
  },

  contactInfo: {
    title: "Get in Touch",
    image: "/images/neurosurgical-consultation-suite-Surrey-Hills-miNEURO-reception-desk.jpg",
    details: [
      {
        icon: MapPin,
        title: "Address",
        content: [
          "miNEURO Consulting Suites",
          "Suite 4, Ground Floor, 619 Canterbury Road",
          "SURREY HILLS VIC 3127",
          "Australia"
        ],
        note: "Our main office is located in Surrey Hills, within 2 minutes walking distance from the train station."
      },
      {
        icon: Phone,
        title: "Phone",
        content: ["Ph: 03 9008 4200"],
        note: "Urgent appointments available on request",
        link: "tel:+***********"
      },
      {
        icon: Phone,
        title: "Fax",
        content: ["Fax: 03 9923 6688"]
      },
      {
        icon: Mail,
        title: "Email",
        content: [
          "Email: <EMAIL>",
          "Argus: <EMAIL>",
          "HealthLink: mineuros"
        ]
      },
      {
        icon: Clock,
        title: "Reception Hours",
        content: [
          "Monday - Friday: 8:30 AM - 5:30 PM",
          "Saturday - Sunday: Closed"
        ]
      }
    ]
  },

  emergencyContact: {
    title: "Emergency Contact Information",
    sections: [
      {
        type: "emergency",
        title: "Emergency Contact Numbers",
        items: [
          {
            label: "Neurosurgical Urgent Enquiries (Business Hours):",
            value: "(03) 9008 4200"
          },
          {
            label: "After Hours Emergency:",
            value: "000"
          }
        ]
      },
      {
        type: "hospital",
        title: "Epworth Richmond Hospital Emergency Department (24 hours)",
        items: [
          {
            label: "Address:",
            value: "62 Erin Street, Richmond Victoria 3121"
          },
          {
            label: "Phone:",
            value: "(03) 9506 3000"
          },
          {
            label: "Website:",
            value: "https://www.epworth.org.au/Our-Services/Emergency",
            isLink: true
          }
        ]
      },
      {
        type: "warning",
        title: "Important:",
        content: "For immediate life-threatening emergencies, always call 000 first. For neurosurgical consultation during business hours, call (03) 9008 4200."
      }
    ]
  },

  referrals: {
    title: "GP and Specialist Referrals",
    icon: FileText,
    content: [
      "A current referral from a general practitioner (GP) or specialist is required for all new patients to claim Medicare rebate. GP referrals are usually valid for 12 months and specialist's referrals for 3 months. Ideally, results of current radiological imaging (<12 months old) should be attached to a referral.",
      "After receiving a referral, the patient will be contacted by our staff to arrange an appointment."
    ],
    buttonText: "Online Referral",
    buttonLink: "/appointments"
  },

  insurance: {
    title: "Insurance Information",
    options: [
      {
        icon: CreditCard,
        title: "Private Health Insurance",
        description: "Patients must have valid private health/hospital insurance with no waiting periods. Extras are not valid. The health funds don't contribute to consultation fees."
      },
      {
        icon: Building,
        title: "TAC",
        description: "Claim details and consultation approval from TAC are required."
      },
      {
        icon: Users,
        title: "Veteran Affairs/Military",
        description: "Both \"Gold Card\" and \"White Card\" patients are eligible. For \"White Card\" holders, a condition must be covered by DVA."
      },
      {
        icon: Building,
        title: "WorkCover",
        description: "Claim details and consultation approval from the insurer. Patients must pay the consultation fee upfront and claim reimbursement from their insurer."
      },
      {
        icon: Users,
        title: "Uninsured",
        description: "Self-funded patients can be provided with a quote for all surgical, anaesthetic and hospital costs. As an example, the minimum amount required for a single-segment spinal surgery not requiring any implants in a private hospital in Victoria can be around $15,000 – $20,000. For all other uninsured referrals, please contact the closest public hospital directly."
      }
    ]
  },

  contactForm: {
    title: "Send a Message",
    image: "/images/neurosurgical-consultation-suite-Surrey-Hills-miNEURO-waiting-area.jpg",
    description: "Our staff will assist to coordinate an appointment at the location most convenient and accessible for you. If you have any questions about our services, please call our main office on 03 9008 4200 or email <NAME_EMAIL>.",
    fields: [
      {
        name: "name",
        label: "Full Name",
        type: "text",
        placeholder: "John Doe",
        required: true
      },
      {
        name: "email",
        label: "Email",
        type: "email",
        placeholder: "<EMAIL>",
        required: true
      },
      {
        name: "phone",
        label: "Phone Number",
        type: "tel",
        placeholder: "03 1234 5678",
        required: false
      },
      {
        name: "subject",
        label: "Subject",
        type: "text",
        placeholder: "How can we help you?",
        required: true
      },
      {
        name: "message",
        label: "Message",
        type: "textarea",
        placeholder: "Please describe your inquiry...",
        required: true
      }
    ],
    submitText: "Send Message",
    successMessage: "Thank you for your message! We'll get back to you soon."
  },

  contactForms: {
    title: "Contact Forms",
    subtitle: "Choose the appropriate form for your inquiry",
    forms: [
      {
        id: "general",
        title: "General Inquiry",
        description: "For general questions and information requests",
        icon: MessageSquare,
        fields: [
          {
            name: "name",
            label: "Full Name",
            type: "text",
            placeholder: "John Doe",
            required: true
          },
          {
            name: "email",
            label: "Email Address",
            type: "email",
            placeholder: "<EMAIL>",
            required: true
          },
          {
            name: "phone",
            label: "Phone Number",
            type: "tel",
            placeholder: "03 1234 5678",
            required: false
          },
          {
            name: "subject",
            label: "Subject",
            type: "text",
            placeholder: "How can we help you?",
            required: true
          },
          {
            name: "contactMethod",
            label: "Preferred Contact Method",
            type: "select",
            options: [
              { value: "email", label: "Email" },
              { value: "phone", label: "Phone" },
              { value: "either", label: "Either" }
            ],
            required: true
          },
          {
            name: "urgency",
            label: "Urgency Level",
            type: "select",
            options: [
              { value: "low", label: "Low - General inquiry" },
              { value: "medium", label: "Medium - Appointment related" },
              { value: "high", label: "High - Urgent medical question" },
              { value: "urgent", label: "Urgent - Immediate attention needed" }
            ],
            required: true
          },
          {
            name: "message",
            label: "Message",
            type: "textarea",
            placeholder: "Please provide details about your inquiry...",
            required: true
          }
        ]
      },
      {
        id: "appointment",
        title: "Appointment Request",
        description: "Request an appointment with Dr. Aliashkevich",
        icon: Calendar,
        fields: [
          {
            name: "patientName",
            label: "Patient Name",
            type: "text",
            placeholder: "Patient's full name",
            required: true
          },
          {
            name: "email",
            label: "Email Address",
            type: "email",
            placeholder: "<EMAIL>",
            required: true
          },
          {
            name: "phone",
            label: "Phone Number",
            type: "tel",
            placeholder: "03 1234 5678",
            required: true
          },
          {
            name: "appointmentType",
            label: "Appointment Type",
            type: "select",
            options: [
              { value: "initial", label: "Initial Consultation" },
              { value: "followup", label: "Follow-up Consultation" },
              { value: "second-opinion", label: "Second Opinion" },
              { value: "medico-legal", label: "Medico-Legal Assessment" }
            ],
            required: true
          },
          {
            name: "preferredLocation",
            label: "Preferred Location",
            type: "select",
            options: [
              { value: "surrey-hills", label: "Surrey Hills" },
              { value: "mornington", label: "Mornington" },
              { value: "frankston", label: "Frankston" },
              { value: "moonee-ponds", label: "Moonee Ponds" },
              { value: "sunbury", label: "Sunbury" },
              { value: "werribee", label: "Werribee" },
              { value: "bundoora", label: "Bundoora" },
              { value: "dandenong", label: "Dandenong" },
              { value: "heidelberg", label: "Heidelberg" },
              { value: "wantirna", label: "Wantirna" }
            ],
            required: true
          },
          {
            name: "preferredDates",
            label: "Preferred Dates/Times",
            type: "textarea",
            placeholder: "Please provide your preferred dates and times...",
            required: true
          },
          {
            name: "reasonForVisit",
            label: "Reason for Visit",
            type: "textarea",
            placeholder: "Brief description of your condition or reason for consultation...",
            required: true
          },
          {
            name: "insurance",
            label: "Insurance Type",
            type: "select",
            options: [
              { value: "private", label: "Private Health Insurance" },
              { value: "tac", label: "TAC" },
              { value: "dva", label: "DVA/Veterans Affairs" },
              { value: "workcover", label: "WorkCover" },
              { value: "self-funded", label: "Self-Funded" }
            ],
            required: true
          },
          {
            name: "specialRequirements",
            label: "Special Requirements",
            type: "textarea",
            placeholder: "Any accessibility needs, interpreter requirements, etc.",
            required: false
          }
        ]
      },
      {
        id: "gp-referral",
        title: "GP Referral",
        description: "For referring physicians and medical professionals",
        icon: Stethoscope,
        fields: [
          {
            name: "physicianName",
            label: "Referring Physician Name",
            type: "text",
            placeholder: "Dr. John Smith",
            required: true
          },
          {
            name: "practiceDetails",
            label: "Practice Name and Address",
            type: "textarea",
            placeholder: "Practice name, address, and contact details",
            required: true
          },
          {
            name: "physicianPhone",
            label: "Physician Phone",
            type: "tel",
            placeholder: "03 1234 5678",
            required: true
          },
          {
            name: "physicianEmail",
            label: "Physician Email",
            type: "email",
            placeholder: "<EMAIL>",
            required: true
          },
          {
            name: "patientName",
            label: "Patient Name",
            type: "text",
            placeholder: "Patient's full name",
            required: true
          },
          {
            name: "patientDOB",
            label: "Patient Date of Birth",
            type: "date",
            required: true
          },
          {
            name: "patientPhone",
            label: "Patient Phone",
            type: "tel",
            placeholder: "03 1234 5678",
            required: true
          },
          {
            name: "clinicalSummary",
            label: "Clinical Summary",
            type: "textarea",
            placeholder: "Brief clinical summary and reason for referral...",
            required: true
          },
          {
            name: "urgencyLevel",
            label: "Urgency Level",
            type: "select",
            options: [
              { value: "routine", label: "Routine (4-6 weeks)" },
              { value: "semi-urgent", label: "Semi-urgent (2-3 weeks)" },
              { value: "urgent", label: "Urgent (within 1 week)" },
              { value: "emergency", label: "Emergency (immediate)" }
            ],
            required: true
          },
          {
            name: "consultationType",
            label: "Preferred Consultation Type",
            type: "select",
            options: [
              { value: "in-person", label: "In-person consultation" },
              { value: "telehealth", label: "Telehealth consultation" },
              { value: "either", label: "Either option" }
            ],
            required: true
          }
        ]
      },
      {
        id: "professional",
        title: "Professional Inquiry",
        description: "For medical professionals and collaboration inquiries",
        icon: Users,
        fields: [
          {
            name: "professionalName",
            label: "Professional Name",
            type: "text",
            placeholder: "Dr. Jane Smith",
            required: true
          },
          {
            name: "credentials",
            label: "Professional Credentials",
            type: "text",
            placeholder: "MBBS, FRACS, etc.",
            required: true
          },
          {
            name: "institution",
            label: "Institution/Practice",
            type: "text",
            placeholder: "Hospital or practice name",
            required: true
          },
          {
            name: "email",
            label: "Professional Email",
            type: "email",
            placeholder: "<EMAIL>",
            required: true
          },
          {
            name: "phone",
            label: "Professional Phone",
            type: "tel",
            placeholder: "03 1234 5678",
            required: true
          },
          {
            name: "inquiryType",
            label: "Inquiry Type",
            type: "select",
            options: [
              { value: "collaboration", label: "Research Collaboration" },
              { value: "consultation", label: "Professional Consultation" },
              { value: "education", label: "Educational Opportunity" },
              { value: "referral", label: "Referral Discussion" },
              { value: "other", label: "Other Professional Matter" }
            ],
            required: true
          },
          {
            name: "collaborationInterest",
            label: "Collaboration Interest",
            type: "textarea",
            placeholder: "Please describe your collaboration interest or professional inquiry...",
            required: true
          },
          {
            name: "contactPreference",
            label: "Preferred Contact Method",
            type: "select",
            options: [
              { value: "email", label: "Email" },
              { value: "phone", label: "Phone" },
              { value: "video", label: "Video Conference" },
              { value: "in-person", label: "In-person Meeting" }
            ],
            required: true
          }
        ]
      }
    ]
  },

  clinicLocations: {
    title: "Clinic Locations",
    subtitle: "Dr. Aliashkevich consults at multiple locations across Melbourne",
    locations: [
      {
        id: "surrey-hills",
        name: "Surrey Hills miNEURO Consulting Suites",
        isPrimary: true,
        address: {
          street: "Suite 4, Ground Floor, 619 Canterbury Road",
          suburb: "Surrey Hills",
          state: "VIC",
          postcode: "3127",
          country: "Australia"
        },
        contact: {
          phone: "(03) 9008 4200",
          fax: "(03) 9923 6688",
          email: "<EMAIL>"
        },
        hours: {
          weekdays: "Monday - Friday: 8:30 AM - 5:30 PM",
          weekends: "Saturday - Sunday: Closed",
          holidays: "Closed on public holidays"
        },
        accessibility: {
          wheelchairAccess: true,
          disabledParking: true,
          elevator: true,
          publicTransport: "2 minutes walk from Surrey Hills train station"
        },
        facilities: {
          parking: "On-site parking available",
          waitingArea: "Comfortable waiting area",
          consultationRooms: "Modern consultation facilities",
          diagnosticEquipment: "Advanced diagnostic equipment available"
        },
        coordinates: {
          lat: -37.8204709,
          lng: 145.1015663
        }
      }
      // Additional locations would be added here
    ]
  },

  map: {
    title: "Office Location Map",
    embedUrl: "https://maps.google.com/maps?q=619%20Canterbury%20Road,%20Surrey%20Hills%20VIC%203127,%20Australia&t=&z=17&ie=UTF8&iwloc=&output=embed",
    features: {
      interactiveMap: true,
      streetView: true,
      directions: true,
      accessibility: true
    },
    transportation: {
      publicTransport: [
        "Surrey Hills train station (2 minutes walk)",
        "Bus routes 109, 612, 623"
      ],
      parking: [
        "On-site parking available",
        "Street parking nearby",
        "Disabled parking spaces"
      ],
      accessibility: [
        "Wheelchair accessible entrance",
        "Elevator access to all floors",
        "Accessible bathroom facilities"
      ]
    }
  },

  professionalCommunication: {
    title: "Professional Communication",
    subtitle: "Direct communication channels for medical professionals",
    sections: [
      {
        id: "direct-lines",
        title: "Direct Professional Lines",
        icon: Phone,
        items: [
          {
            label: "Physician-to-Physician Consultation",
            value: "(03) 9008 4200",
            description: "Direct line for medical professional consultations",
            hours: "Monday - Friday, 8:30 AM - 5:30 PM"
          },
          {
            label: "Urgent Referral Line",
            value: "(03) 9008 4200",
            description: "Priority line for urgent medical referrals",
            hours: "24/7 for urgent cases"
          }
        ]
      },
      {
        id: "secure-communication",
        title: "Secure Communication Protocols",
        icon: Shield,
        items: [
          {
            label: "Secure Email",
            value: "<EMAIL>",
            description: "Encrypted email for sensitive medical information"
          },
          {
            label: "Fax Transmission",
            value: "(03) 9923 6688",
            description: "Secure fax for medical records and referrals"
          },
          {
            label: "HealthLink",
            value: "mineuros",
            description: "Electronic referral system"
          },
          {
            label: "Argus",
            value: "<EMAIL>",
            description: "Medical imaging and report sharing"
          }
        ]
      },
      {
        id: "telehealth",
        title: "Telehealth Consultation Options",
        icon: Video,
        items: [
          {
            label: "Video Consultations",
            value: "Available on request",
            description: "Secure video consultations for professional discussions"
          },
          {
            label: "Case Discussion Scheduling",
            value: "(03) 9008 4200",
            description: "Schedule dedicated time for complex case discussions"
          }
        ]
      }
    ]
  },

  hospitalAffiliations: {
    title: "Hospital Affiliations",
    subtitle: "Emergency departments and hospital contacts",
    hospitals: [
      {
        id: "epworth-richmond",
        name: "Epworth Richmond Hospital",
        type: "Primary Affiliation",
        emergency: true,
        contact: {
          address: "62 Erin Street, Richmond VIC 3121",
          phone: "(03) 9506 3000",
          emergency: "(03) 9506 3000",
          website: "https://www.epworth.org.au/Our-Services/Emergency"
        },
        services: {
          emergencyDepartment: "24 hours",
          admissions: "24 hours",
          visitingHours: "10:00 AM - 8:00 PM",
          parking: "Multi-level parking available"
        },
        procedures: {
          emergencyPresentation: "Present to emergency department with referral",
          admissionProcess: "Contact admissions office",
          emergencyContact: "Call (03) 9506 3000 for emergencies"
        }
      },
      {
        id: "epworth-eastern",
        name: "Epworth Eastern Hospital",
        type: "Affiliated Hospital",
        emergency: true,
        contact: {
          address: "1 Arnold Street, Box Hill VIC 3128",
          phone: "(03) 9895 7777",
          emergency: "(03) 9895 7777",
          website: "https://www.epworth.org.au/eastern"
        },
        services: {
          emergencyDepartment: "24 hours",
          admissions: "24 hours",
          visitingHours: "10:00 AM - 8:00 PM",
          parking: "On-site parking available"
        }
      },
      {
        id: "warringal-private",
        name: "Warringal Private Hospital",
        type: "Consulting Hospital",
        emergency: false,
        contact: {
          address: "10 Martin Street, Heidelberg VIC 3084",
          phone: "(03) 9433 8111",
          website: "https://www.warringalprivate.com.au"
        },
        services: {
          consultations: "By appointment",
          admissions: "Planned procedures",
          visitingHours: "2:00 PM - 8:00 PM",
          parking: "Free parking available"
        }
      },
      {
        id: "peninsula-private",
        name: "Peninsula Private Hospital",
        type: "Consulting Hospital",
        emergency: false,
        contact: {
          address: "525 McClelland Drive, Langwarrin VIC 3910",
          phone: "(03) 9788 3333",
          website: "https://www.peninsulaprivate.com.au"
        },
        services: {
          consultations: "By appointment",
          admissions: "Planned procedures",
          visitingHours: "2:00 PM - 8:00 PM",
          parking: "Free parking available"
        }
      },
      {
        id: "the-bays-hospital",
        name: "The Bays Hospital",
        type: "Consulting Hospital",
        emergency: false,
        contact: {
          address: "156 Bay Road, Sandringham VIC 3191",
          phone: "(03) 9598 8888",
          website: "https://www.thebayshospital.com.au"
        },
        services: {
          consultations: "By appointment",
          admissions: "Planned procedures",
          visitingHours: "2:00 PM - 8:00 PM",
          parking: "Valet parking available"
        }
      }
    ],
    emergencyProcedures: {
      title: "Emergency Procedures",
      icon: AlertTriangle,
      procedures: [
        {
          situation: "Life-threatening emergency",
          action: "Call 000 immediately",
          description: "For immediate life-threatening situations, always call emergency services first"
        },
        {
          situation: "Urgent neurosurgical symptoms",
          action: "Call (03) 9008 4200 or present to emergency department",
          description: "For urgent neurosurgical symptoms during business hours"
        },
        {
          situation: "After-hours urgent care",
          action: "Present to Epworth Richmond Emergency Department",
          description: "For urgent neurosurgical care outside business hours"
        },
        {
          situation: "Post-operative complications",
          action: "Call (03) 9008 4200 or present to emergency department",
          description: "For any post-operative concerns or complications"
        }
      ]
    }
  },

  faqs: {
    title: "Frequently Asked Questions",
    subtitle: "Common questions about contacting our practice",
    questions: [
      {
        question: "What are your office hours?",
        answer: "Our reception is open Monday to Friday from 8:30 AM to 5:30 PM. We are closed on weekends and public holidays. For urgent matters outside these hours, please call our main number or present to the emergency department."
      },
      {
        question: "How quickly will you respond to my inquiry?",
        answer: "We aim to respond to all general inquiries within 24 hours during business days. Urgent medical matters are prioritized and addressed immediately. Emergency situations should always call 000 first."
      },
      {
        question: "Can I book an appointment online?",
        answer: "Yes, you can submit an appointment request through our online form. Our staff will contact you within 24 hours to confirm your appointment details and preferred location."
      },
      {
        question: "Do you offer telehealth consultations?",
        answer: "Yes, we offer telehealth consultations via phone, Skype, Zoom, and FaceTime for suitable cases. Please specify your preference when booking your appointment."
      },
      {
        question: "What should I do in a medical emergency?",
        answer: "For immediate life-threatening emergencies, always call 000 first. For urgent neurosurgical matters during business hours, call (03) 9008 4200. After hours, present to Epworth Richmond Emergency Department."
      },
      {
        question: "How do I send medical records securely?",
        answer: "You can fax medical records to (03) 9923 6688, <NAME_EMAIL>, or use our secure HealthLink system (mineuros). For imaging, use our Argus system (<EMAIL>)."
      }
    ]
  }
};

export default contactData;

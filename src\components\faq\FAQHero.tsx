import React from 'react';
import { Search, HelpCircle, Clock, Star } from 'lucide-react';

import SafeImage from '@/components/SafeImage';
import { Badge } from '@/components/ui/badge';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface FAQHeroProps {
  title: string;
  subtitle: string;
  description: string;
  imageSrc: string;
  imageAlt: string;
  totalQuestions?: number;
  categories?: number;
}

const FAQHero: React.FC<FAQHeroProps> = ({
  title,
  subtitle,
  description,
  imageSrc,
  imageAlt,
  totalQuestions = 33,
  categories = 6
}) => {
  const deviceInfo = useDeviceDetection();

  return (
    <div>
      {/* Enhanced Hero Section */}
      <div className={cn(
        "relative overflow-hidden",
        deviceInfo.isMobile ? "py-mobile-xl" : "py-24"
      )}>
        {/* Background with gradient and pattern */}
        <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-blue-50/50 to-slate-50 dark:from-primary/10 dark:via-slate-900 dark:to-slate-800" />
        <div className="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23e2e8f0" fill-opacity="0.3"%3E%3Ccircle cx="30" cy="30" r="1"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-40" />

        <div className={cn(
          "relative z-10",
          deviceInfo.isMobile ? "mobile-container" : "container"
        )}>
          <div className={cn(
            "text-center mx-auto",
            deviceInfo.isMobile ? "max-w-full" : "max-w-4xl"
          )}>
            {/* Badge */}
            <div className="flex justify-center mb-6">
              <Badge variant="secondary" className="px-4 py-2 text-sm font-medium">
                <HelpCircle className="h-4 w-4 mr-2" />
                Comprehensive FAQ Guide
              </Badge>
            </div>

            {/* Main Title */}
            <h1 className={cn(
              "font-bold text-slate-900 dark:text-slate-100 leading-tight",
              deviceInfo.isMobile
                ? "text-3xl mb-4"
                : "text-5xl lg:text-6xl mb-6"
            )}>
              {title}
            </h1>

            {/* Subtitle */}
            <p className={cn(
              "text-slate-600 dark:text-slate-300 leading-relaxed",
              deviceInfo.isMobile ? "text-base mb-6" : "text-xl mb-8"
            )}>
              {subtitle}
            </p>

            {/* Quick Stats */}
            <div className="flex justify-center gap-6 mb-8">
              <div className="text-center">
                <div className="text-2xl font-bold text-primary">{totalQuestions}</div>
                <div className="text-sm text-muted-foreground">Questions</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-primary">{categories}</div>
                <div className="text-sm text-muted-foreground">Categories</div>
              </div>
              <div className="text-center">
                <div className="flex items-center justify-center gap-1">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                  ))}
                </div>
                <div className="text-sm text-muted-foreground">Expert Answers</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Introduction Section */}
      <section className={cn(
        "relative",
        deviceInfo.isMobile ? "py-8" : "py-16"
      )}>
        <div className="container">
          <div className="max-w-4xl mx-auto">
            <div className="grid lg:grid-cols-2 gap-8 items-center">
              {/* Content */}
              <div className="space-y-6">
                <div className="prose prose-slate dark:prose-invert max-w-none">
                  <p className="text-lg leading-relaxed text-slate-600 dark:text-slate-300">
                    {description}
                  </p>
                </div>

                {/* Quick Navigation Hints */}
                <div className="bg-slate-50 dark:bg-slate-800 rounded-xl p-6 border">
                  <h3 className="font-semibold text-slate-900 dark:text-slate-100 mb-4 flex items-center gap-2">
                    <Search className="h-5 w-5 text-primary" />
                    How to Use This FAQ
                  </h3>
                  <ul className="space-y-2 text-sm text-slate-600 dark:text-slate-300">
                    <li className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-primary rounded-full" />
                      Browse categories in the sidebar
                    </li>
                    <li className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-primary rounded-full" />
                      Use the search box to find specific topics
                    </li>
                    <li className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-primary rounded-full" />
                      Click questions to expand detailed answers
                    </li>
                  </ul>
                </div>
              </div>

              {/* Enhanced Featured Image */}
              <div className="relative">
                <div className="relative rounded-2xl overflow-hidden shadow-2xl">
                  <SafeImage
                    src={imageSrc}
                    alt={imageAlt}
                    className="w-full h-auto max-h-[400px] object-cover"
                    fallbackSrc="/images/medical-consulting.jpg"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent" />

                  {/* Floating badge */}
                  <div className="absolute top-4 right-4">
                    <Badge className="bg-white/90 text-slate-900 shadow-lg">
                      <Clock className="h-3 w-3 mr-1" />
                      Expert Guidance
                    </Badge>
                  </div>
                </div>

                {/* Decorative elements */}
                <div className="absolute -top-4 -right-4 w-24 h-24 bg-primary/10 rounded-full blur-xl" />
                <div className="absolute -bottom-4 -left-4 w-32 h-32 bg-blue-500/10 rounded-full blur-xl" />
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

FAQHero.displayName = 'FAQHero';

export default FAQHero;

import React from 'react';

import SafeImage from '@/components/SafeImage';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface FAQHeroProps {
  title: string;
  subtitle: string;
  description: string;
  imageSrc: string;
  imageAlt: string;
}

const FAQHero: React.FC<FAQHeroProps> = ({
  title, 
  subtitle, 
  description, 
  imageSrc, 
  imageAlt 
}) => {
  const deviceInfo = useDeviceDetection();

  return (
    <div>
      {/* Hero Section */}
      <div className={cn(
        "relative bg-gradient-to-r from-primary/10 to-white dark:from-primary/20 dark:to-background mobile-safe-area",
        deviceInfo.isMobile ? "py-mobile-xl" : "py-20"
      )}>
        <div className={cn(
          "relative z-10",
          deviceInfo.isMobile ? "mobile-container" : "container"
        )}>
          <div className={cn(
            "text-center mx-auto",
            deviceInfo.isMobile ? "max-w-full" : "max-w-3xl"
          )}>
            <h1 className={cn(
              "font-bold mt-2 mb-mobile-lg",
              deviceInfo.isMobile
                ? "mobile-4xl"
                : "text-4xl md:text-5xl mb-6"
            )}>
              {title}
            </h1>
            <p className={cn(
              "text-muted-foreground",
              deviceInfo.isMobile ? "mobile-text" : ""
            )}>
              {subtitle}
            </p>
          </div>
        </div>
      </div>

      {/* Introduction */}
      <section className="py-8">
        <div className="container">
          <div className="max-w-3xl mx-auto">
            <p className="text-muted-foreground mb-6">
              {description}
            </p>

            {/* Featured Image */}
            <div className="relative rounded-xl overflow-hidden mb-8 shadow-lg max-w-2xl mx-auto">
              <SafeImage
                src={imageSrc}
                alt={imageAlt}
                className="w-full h-auto max-h-[300px] object-cover"
                fallbackSrc="/images/medical-consulting.jpg"
              />
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

FAQHero.displayName = 'FAQHero';

export default FAQHero;

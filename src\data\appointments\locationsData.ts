export interface LocationOption {
  id: string;
  value: string;
  label: string;
}

export interface MainLocation {
  id: string;
  title: string;
  address: string;
  phone: string;
  fax: string;
  email: string;
  imageSrc: string;
  imageAlt: string;
}

export const locationOptions: LocationOption[] = [
  { id: 'surrey-hills', value: 'surrey-hills', label: 'Surrey Hills' },
  { id: 'mornington', value: 'mornington', label: 'Mornington' },
  { id: 'frankston', value: 'frankston', label: 'Frankston' },
  { id: 'moonee-ponds', value: 'moonee-ponds', label: 'Moonee Ponds' },
  { id: 'sunbury', value: 'sunbury', label: 'Sunbury' },
  { id: 'werribee', value: 'werribee', label: 'Werribee' },
  { id: 'bundoora', value: 'bundoora', label: 'Bundoora' },
  { id: 'dandenong', value: 'dandenong', label: 'Dandenong' },
  { id: 'heidelberg', value: 'heidelberg', label: 'Heidelberg' },
  { id: 'wantirna', value: 'wantirna', label: 'Wantirna' }
];

export const mainLocation: MainLocation = {
  id: 'surrey-hills-main',
  title: 'Surrey Hills miNEURO Consulting Suites',
  address: 'Suite 4, 619 Canterbury Road\nSurrey Hills VIC 3127',
  phone: '(03) 9830 0344',
  fax: '(03) 9830 0355',
  email: '<EMAIL>',
  imageSrc: '/images/neurosurgical-consultation-suite-Surrey-Hills-miNEURO-doctor-desk.jpg',
  imageAlt: 'Surrey Hills miNEURO Consulting Suites'
};

export const getLocationById = (id: string): LocationOption | undefined => {
  return locationOptions.find(location => location.id === id);
};

export const getLocationByValue = (value: string): LocationOption | undefined => {
  return locationOptions.find(location => location.value === value);
};

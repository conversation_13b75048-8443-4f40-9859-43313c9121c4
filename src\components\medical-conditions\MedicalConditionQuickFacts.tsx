import { LucideIcon } from 'lucide-react';
import React from 'react';

import { Card, CardContent } from '@/components/ui/card';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

export interface QuickFact {
  icon: LucideIcon;
  title: string;
  value: string;
  description?: string;
}

export interface MedicalConditionQuickFactsProps {
  facts: QuickFact[];
  className?: string;
}

const MedicalConditionQuickFacts: React.FC<MedicalConditionQuickFactsProps> = ({
  facts,
  className
}) => {
  const deviceInfo = useDeviceDetection();

  return (
    <div className={cn(
      "bg-muted/30",
      deviceInfo.isMobile ? "py-8" : "py-12",
      className
    )}><div className={cn("container", deviceInfo.isMobile ? "px-4" : "")}>
        <div className={cn("grid", facts.length <= 2 ? "grid-cols-1 md:grid-cols-2" : facts.length === 3 ? "grid-cols-1 md:grid-cols-3" : "grid-cols-1 md:grid-cols-2 lg:grid-cols-4")}>{facts.map((fact, index) => {
            const IconComponent = fact.icon;
            return (
              <Card key={index} className="text-center">
                <CardContent className="pt-6">
                  <IconComponent className="h-8 w-8 mx-auto mb-2 text-primary" />
                  <h3 className="font-semibold mb-1">{fact.title}</h3>
                  <p className="text-sm text-muted-foreground">{fact.value}</p>
                  {fact.description && (
                    <p className="text-xs text-muted-foreground mt-1">{fact.description}</p>
                  )}
                </CardContent>
              </Card>
            );
          })}
        </div>
      </div>        </div>
  );
};

export default MedicalConditionQuickFacts;

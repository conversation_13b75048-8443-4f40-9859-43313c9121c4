#!/usr/bin/env node

/**
 * Route Type Verification Script
 *
 * This script verifies that our route configuration types are compatible
 * with React Router's expectations and identifies any potential type conflicts.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// ANSI color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSection(title) {
  log(`\n${'='.repeat(60)}`, 'cyan');
  log(`${title}`, 'bright');
  log(`${'='.repeat(60)}`, 'cyan');
}

function logSubsection(title) {
  log(`\n${'-'.repeat(40)}`, 'blue');
  log(`${title}`, 'blue');
  log(`${'-'.repeat(40)}`, 'blue');
}

// Verification results
const verificationResults = {
  routeConfigInterface: { passed: false, issues: [] },
  routeElementTypes: { passed: false, issues: [] },
  routeUsage: { passed: false, issues: [] },
  typeCompatibility: { passed: false, issues: [] }
};

/**
 * Check RouteConfig interface definition
 */
function verifyRouteConfigInterface() {
  logSubsection('Verifying RouteConfig Interface');
  
  try {
    const routeConfigPath = path.join(process.cwd(), 'src/routes/routeConfig.tsx');
    const content = fs.readFileSync(routeConfigPath, 'utf8');
    
    // Check if interface is properly defined
    const interfaceMatch = content.match(/export interface RouteConfig\s*{([^}]+)}/);
    if (!interfaceMatch) {
      verificationResults.routeConfigInterface.issues.push('RouteConfig interface not found');
      return;
    }
    
    const interfaceBody = interfaceMatch[1];
    
    // Check required properties
    const hasPath = /path\s*:\s*string/.test(interfaceBody);
    const hasElement = /element\s*:\s*React\.ReactNode(\s*\|\s*null)?/.test(interfaceBody);
    const hasOptionalChildren = /children\?\s*:\s*RouteConfig\[\]/.test(interfaceBody);
    
    if (!hasPath) {
      verificationResults.routeConfigInterface.issues.push('Missing required path property');
    }
    
    if (!hasElement) {
      verificationResults.routeConfigInterface.issues.push('Missing or incorrectly typed element property');
    }
    
    if (!hasOptionalChildren) {
      verificationResults.routeConfigInterface.issues.push('Missing optional children property');
    }
    
    // Check if element type allows null (React Router compatibility)
    const elementAllowsNull = /element\s*:\s*React\.ReactNode\s*\|\s*null/.test(interfaceBody);
    if (!elementAllowsNull) {
      verificationResults.routeConfigInterface.issues.push('Element property should allow null for React Router compatibility');
    }
    
    verificationResults.routeConfigInterface.passed = 
      hasPath && hasElement && hasOptionalChildren && elementAllowsNull;
    
    if (verificationResults.routeConfigInterface.passed) {
      log('✅ RouteConfig interface is properly defined', 'green');
    } else {
      log('❌ RouteConfig interface has issues', 'red');
      verificationResults.routeConfigInterface.issues.forEach(issue => {
        log(`   - ${issue}`, 'red');
      });
    }
    
  } catch (error) {
    verificationResults.routeConfigInterface.issues.push(`Error reading RouteConfig: ${error.message}`);
    log(`❌ Error verifying RouteConfig interface: ${error.message}`, 'red');
  }
}

/**
 * Check route element creation functions
 */
function verifyRouteElementTypes() {
  logSubsection('Verifying Route Element Creation');
  
  try {
    const routeConfigPath = path.join(process.cwd(), 'src/routes/routeConfig.tsx');
    const content = fs.readFileSync(routeConfigPath, 'utf8');
    
    // Check createRouteElement function
    const createRouteElementMatch = content.match(/function createRouteElement\([^)]+\):\s*([^{]+)/);
    if (!createRouteElementMatch) {
      verificationResults.routeElementTypes.issues.push('createRouteElement function not found');
    } else {
      const returnType = createRouteElementMatch[1].trim();
      if (!returnType.includes('React.ReactNode') || !returnType.includes('null')) {
        verificationResults.routeElementTypes.issues.push('createRouteElement should return React.ReactNode | null');
      }
    }
    
    // Check createRedirectElement function
    const createRedirectElementMatch = content.match(/function createRedirectElement\([^)]+\):\s*([^{]+)/);
    if (!createRedirectElementMatch) {
      verificationResults.routeElementTypes.issues.push('createRedirectElement function not found');
    } else {
      const returnType = createRedirectElementMatch[1].trim();
      if (!returnType.includes('React.ReactNode') || !returnType.includes('null')) {
        verificationResults.routeElementTypes.issues.push('createRedirectElement should return React.ReactNode | null');
      }
    }
    
    // Check for error handling in functions
    const hasErrorHandling = content.includes('try {') && content.includes('catch (error)');
    if (!hasErrorHandling) {
      verificationResults.routeElementTypes.issues.push('Route element creation functions should include error handling');
    }
    
    verificationResults.routeElementTypes.passed = verificationResults.routeElementTypes.issues.length === 0;
    
    if (verificationResults.routeElementTypes.passed) {
      log('✅ Route element creation functions are properly typed', 'green');
    } else {
      log('❌ Route element creation functions have issues', 'red');
      verificationResults.routeElementTypes.issues.forEach(issue => {
        log(`   - ${issue}`, 'red');
      });
    }
    
  } catch (error) {
    verificationResults.routeElementTypes.issues.push(`Error reading route element functions: ${error.message}`);
    log(`❌ Error verifying route element types: ${error.message}`, 'red');
  }
}

/**
 * Check route usage in App.tsx
 */
function verifyRouteUsage() {
  logSubsection('Verifying Route Usage in App.tsx');
  
  try {
    const appPath = path.join(process.cwd(), 'src/App.tsx');
    const content = fs.readFileSync(appPath, 'utf8');
    
    // Check if RouteConfig is imported
    const hasRouteConfigImport = content.includes('RouteConfig');
    if (!hasRouteConfigImport) {
      verificationResults.routeUsage.issues.push('RouteConfig type not imported in App.tsx');
    }
    
    // Check route mapping with proper typing
    const routeMapMatch = content.match(/routes\?\.map\(\(route:\s*RouteConfig[^)]+\)/);
    if (!routeMapMatch) {
      verificationResults.routeUsage.issues.push('Route mapping not properly typed');
    }
    
    // Check for route validation
    const hasRouteValidation = content.includes('route?.path') || content.includes('!route?.path');
    if (!hasRouteValidation) {
      verificationResults.routeUsage.issues.push('Missing route validation in App.tsx');
    }
    
    // Check Route component usage
    const routeComponentMatch = content.match(/<Route[^>]*element={route\.element}[^>]*\/>/);
    if (!routeComponentMatch) {
      verificationResults.routeUsage.issues.push('Route component not properly configured');
    }
    
    verificationResults.routeUsage.passed = verificationResults.routeUsage.issues.length === 0;
    
    if (verificationResults.routeUsage.passed) {
      log('✅ Route usage in App.tsx is correct', 'green');
    } else {
      log('❌ Route usage in App.tsx has issues', 'red');
      verificationResults.routeUsage.issues.forEach(issue => {
        log(`   - ${issue}`, 'red');
      });
    }
    
  } catch (error) {
    verificationResults.routeUsage.issues.push(`Error reading App.tsx: ${error.message}`);
    log(`❌ Error verifying route usage: ${error.message}`, 'red');
  }
}

/**
 * Check React Router type compatibility
 */
function verifyTypeCompatibility() {
  logSubsection('Verifying React Router Type Compatibility');
  
  try {
    // Check if React Router types are available
    const nodeModulesPath = path.join(process.cwd(), 'node_modules/react-router/dist/lib/components.d.ts');
    
    if (!fs.existsSync(nodeModulesPath)) {
      verificationResults.typeCompatibility.issues.push('React Router type definitions not found');
      return;
    }
    
    const reactRouterTypes = fs.readFileSync(nodeModulesPath, 'utf8');
    
    // Check if React Router expects ReactNode | null for element
    const elementTypeMatch = reactRouterTypes.match(/element\?\s*:\s*React\.ReactNode\s*\|\s*null/);
    if (!elementTypeMatch) {
      verificationResults.typeCompatibility.issues.push('React Router element type expectations may have changed');
    }
    
    // Check RouteProps interface
    const routePropsMatch = reactRouterTypes.match(/export type RouteProps/);
    if (!routePropsMatch) {
      verificationResults.typeCompatibility.issues.push('RouteProps type not found in React Router');
    }
    
    verificationResults.typeCompatibility.passed = verificationResults.typeCompatibility.issues.length === 0;
    
    if (verificationResults.typeCompatibility.passed) {
      log('✅ Types are compatible with React Router', 'green');
    } else {
      log('❌ Type compatibility issues detected', 'red');
      verificationResults.typeCompatibility.issues.forEach(issue => {
        log(`   - ${issue}`, 'red');
      });
    }
    
  } catch (error) {
    verificationResults.typeCompatibility.issues.push(`Error checking React Router compatibility: ${error.message}`);
    log(`❌ Error verifying type compatibility: ${error.message}`, 'red');
  }
}

/**
 * Generate summary report
 */
function generateSummaryReport() {
  logSection('ROUTE TYPE VERIFICATION SUMMARY');
  
  const allChecks = Object.values(verificationResults);
  const passedChecks = allChecks.filter(check => check.passed).length;
  const totalChecks = allChecks.length;
  
  log(`\nOverall Status: ${passedChecks}/${totalChecks} checks passed`, 
    passedChecks === totalChecks ? 'green' : 'yellow');
  
  // Individual check results
  Object.entries(verificationResults).forEach(([checkName, result]) => {
    const status = result.passed ? '✅ PASSED' : '❌ FAILED';
    const color = result.passed ? 'green' : 'red';
    log(`\n${checkName}: ${status}`, color);
    
    if (!result.passed && result.issues.length > 0) {
      result.issues.forEach(issue => {
        log(`  - ${issue}`, 'red');
      });
    }
  });
  
  // Recommendations
  if (passedChecks < totalChecks) {
    log('\n📋 RECOMMENDATIONS:', 'yellow');
    log('1. Fix the identified type issues above', 'yellow');
    log('2. Run TypeScript compiler to verify fixes', 'yellow');
    log('3. Test route navigation in development', 'yellow');
    log('4. Re-run this verification script', 'yellow');
  } else {
    log('\n🎉 All route type checks passed! Your route configuration is properly typed.', 'green');
  }
  
  return passedChecks === totalChecks;
}

// Main execution
function main() {
  logSection('ROUTE TYPE VERIFICATION');
  log('Checking route configuration type safety...', 'cyan');
  
  verifyRouteConfigInterface();
  verifyRouteElementTypes();
  verifyRouteUsage();
  verifyTypeCompatibility();
  
  const allPassed = generateSummaryReport();
  
  // Exit with appropriate code
  process.exit(allPassed ? 0 : 1);
}

// Run the verification
main();

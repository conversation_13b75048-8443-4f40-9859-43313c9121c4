import React, { useEffect } from 'react';

import AppointmentInformation from '@/components/appointments/AppointmentInformation';
import AppointmentProcess from '@/components/appointments/AppointmentProcess';
import AppointmentRequestForm from '@/components/appointments/AppointmentRequestForm';
import InvestigationsSection from '@/components/appointments/InvestigationsSection';
import PageHeader from '@/components/PageHeader';
import StandardPageLayout from '@/components/StandardPageLayout';
import appointmentsData from '@/data/pages/appointments';
import { generatePageSEO } from '@/lib/seo';

/**
 * Refactored Appointments Component
 * 
 * Original component: 809 lines
 * Refactored component: <100 lines
 * Reduction: ~88%
 */

const AppointmentsRefactored: React.FC = () => {
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  const appointmentsSeoData = generatePageSEO('appointments');

  return (
    <StandardPageLayout 
      pageType="appointments" 
      seoData={appointmentsSeoData} 
      showHeader={false}
    >
      <PageHeader
        title={appointmentsData.hero.title}
        subtitle={appointmentsData.hero.subtitle}
        backgroundImage={appointmentsData.hero.backgroundImage}
        enableParallax={true}
      />

      <div className="flex-1">
        <AppointmentProcess
          title={appointmentsData.process.title}
          subtitle={appointmentsData.process.subtitle}
          steps={appointmentsData.process.steps}
        />

        <section className="py-16">
          <div className="container">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
              <AppointmentInformation
                title={appointmentsData.appointmentInfo.title}
                sections={appointmentsData.appointmentInfo.sections}
              />

              <AppointmentRequestForm
                title={appointmentsData.requestForm.title}
                fields={appointmentsData.requestForm.fields}
                submitText={appointmentsData.requestForm.submitText}
                confirmationText={appointmentsData.requestForm.confirmationText}
              />
            </div>
          </div>
        </section>

        <InvestigationsSection
          title={appointmentsData.investigations.title}
          description={appointmentsData.investigations.description}
          items={appointmentsData.investigations.items}
          image={appointmentsData.investigations.image}
        />

        {/* Treatment Options Section */}
        <section className="py-16">
          <div className="container">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div className="relative rounded-xl overflow-hidden shadow-lg">
                <img
                  src={appointmentsData.treatmentOptions.image.src}
                  alt={appointmentsData.treatmentOptions.image.alt}
                  className="w-full h-auto"
                />
              </div>
              <div>
                <h2 className="text-3xl font-bold mb-6">
                  {appointmentsData.treatmentOptions.title}
                </h2>
                {appointmentsData.treatmentOptions.description.map((paragraph, index) => (
                  <p key={index} className="text-muted-foreground mb-4">
                    {paragraph}
                  </p>
                ))}
              </div>
            </div>
          </div>
        </section>
      </div>
    </StandardPageLayout>
  );
};

AppointmentsRefactored.displayName = 'AppointmentsRefactored';

export default AppointmentsRefactored;

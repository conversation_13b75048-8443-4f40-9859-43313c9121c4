import { MapPin, Phone, Mail, Clock } from 'lucide-react';
import React from 'react';

interface ContactDetail {
  icon: React.ReactNode;
  title: string;
  content: string | React.ReactNode;
}

interface LocationContactInfoProps {
  address: {
    street: string;
    suburb: string;
    state: string;
    postcode: string;
  };
  phone: string;
  email: string;
  hours: {
    weekdays: string;
    note?: string;
  };
  consultingHours?: {
    title: string;
    details: string;
    appointmentNote: string;
    urgentNote?: string;
  };
  appointmentProcess?: {
    title: string;
    details1: string;
    details2: string;
  };
}

const LocationContactInfo: React.FC<LocationContactInfoProps> = ({
  address,
  phone,
  email,
  hours,
  consultingHours,
  appointmentProcess
}) => {
  const contactDetails: ContactDetail[] = [
    {
      icon: <MapPin className="h-5 w-5 text-primary" />,
      title: "Address",
      content: (
        <>
          {address.street}<br />
          {address.suburb} {address.state} {address.postcode}
        </>
      )
    },
    {
      icon: <Phone className="h-5 w-5 text-primary" />,
      title: "Phone",
      content: phone
    },
    {
      icon: <Mail className="h-5 w-5 text-primary" />,
      title: "Email",
      content: email
    },
    {
      icon: <Clock className="h-5 w-5 text-primary" />,
      title: "Hours",
      content: (
        <>
          {hours.weekdays}
          {hours.note && <><br /><span className="text-sm">{hours.note}</span></>}
        </>
      )
    }
  ];

  return (
    <section className="py-16 bg-muted/30">
      <div className="container">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <div>
            <h2 className="text-3xl font-bold mb-8">Contact Information</h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
              {contactDetails.map((detail, index) => (
                <div key={index} className="flex items-start">
                  <div className="flex-shrink-0 h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center mr-4">
                    {detail.icon}
                  </div>
                  <div>
                    <h4 className="font-semibold mb-1">{detail.title}</h4>
                    <p className="text-muted-foreground">{detail.content}</p>
                  </div>
                </div>
              ))}
            </div>

            {consultingHours && (
              <div className="card p-6 rounded-lg shadow-md bg-card mt-8">
                <h3 className="text-xl font-semibold mb-3 text-primary">{consultingHours.title}</h3>
                <p className="text-muted-foreground mb-2">{consultingHours.details}</p>
                <p className="text-muted-foreground mb-4">{consultingHours.appointmentNote}</p>
                {consultingHours.urgentNote && (
                  <p className="text-muted-foreground">{consultingHours.urgentNote}</p>
                )}
              </div>
            )}

            {appointmentProcess && (
              <div className="card p-6 rounded-lg shadow-md bg-card mt-6">
                <h3 className="text-xl font-semibold mb-3 text-primary">{appointmentProcess.title}</h3>
                <p className="text-muted-foreground mb-2">{appointmentProcess.details1}</p>
                <p className="text-muted-foreground">{appointmentProcess.details2}</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </section>
  );
};

export default LocationContactInfo;

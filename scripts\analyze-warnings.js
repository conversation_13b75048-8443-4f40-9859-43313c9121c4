#!/usr/bin/env node

/**
 * Warning Analysis Script
 * 
 * Analyzes the comprehensive codebase analysis to categorize and prioritize
 * the 160 warnings for systematic remediation.
 */

import fs from 'fs';
import path from 'path';

console.log('🔍 COMPREHENSIVE WARNING ANALYSIS');
console.log('='.repeat(50));

/**
 * Load and parse the comprehensive analysis
 */
function loadAnalysis() {
  try {
    const content = fs.readFileSync('comprehensive-codebase-analysis.json', 'utf8');
    return JSON.parse(content);
  } catch (error) {
    console.error('❌ Error loading analysis file:', error.message);
    return null;
  }
}

/**
 * Categorize warnings by type
 */
function categorizeWarnings(issues) {
  const categories = {
    accessibility: [],
    routing: [],
    other: []
  };

  for (const issue of issues) {
    if (issue.issue && issue.issue.includes('accessibility')) {
      categories.accessibility.push(issue);
    } else if (issue.issue && issue.issue.includes('Route path')) {
      categories.routing.push(issue);
    } else {
      categories.other.push(issue);
    }
  }

  return categories;
}

/**
 * Analyze component sizes
 */
function analyzeComponentSizes(structure) {
  const largeComponents = [];
  
  // Analyze all components and pages
  const allFiles = [
    ...structure.components,
    ...structure.pages
  ];

  for (const file of allFiles) {
    const sizeKB = file.size / 1024;
    if (sizeKB > 20) { // Components larger than 20KB
      largeComponents.push({
        ...file,
        sizeKB: Math.round(sizeKB),
        category: sizeKB > 80 ? 'critical' : sizeKB > 40 ? 'high' : 'medium'
      });
    }
  }

  // Sort by size (largest first)
  largeComponents.sort((a, b) => b.size - a.size);

  return largeComponents;
}

/**
 * Generate remediation strategy
 */
function generateStrategy(categories, largeComponents) {
  const strategy = {
    warnings: {
      total: Object.values(categories).flat().length,
      accessibility: categories.accessibility.length,
      routing: categories.routing.length,
      other: categories.other.length
    },
    components: {
      total: largeComponents.length,
      critical: largeComponents.filter(c => c.category === 'critical').length,
      high: largeComponents.filter(c => c.category === 'high').length,
      medium: largeComponents.filter(c => c.category === 'medium').length
    },
    priorities: [
      {
        phase: 1,
        title: "Fix Accessibility Issues",
        description: "Address semantic HTML issues across all components",
        items: categories.accessibility.length,
        effort: "Medium",
        impact: "High"
      },
      {
        phase: 2,
        title: "Refactor Critical Components",
        description: "Break down largest components (80KB+)",
        items: largeComponents.filter(c => c.category === 'critical').length,
        effort: "High",
        impact: "Very High"
      },
      {
        phase: 3,
        title: "Fix Route Configuration",
        description: "Resolve unused route definitions",
        items: categories.routing.length,
        effort: "Low",
        impact: "Medium"
      },
      {
        phase: 4,
        title: "Refactor High Priority Components",
        description: "Break down medium-large components (40-80KB)",
        items: largeComponents.filter(c => c.category === 'high').length,
        effort: "High",
        impact: "High"
      }
    ]
  };

  return strategy;
}

/**
 * Main analysis function
 */
function main() {
  const analysis = loadAnalysis();
  if (!analysis) return;

  console.log('📊 Analyzing warnings and components...');

  // Categorize warnings
  const categories = categorizeWarnings(analysis.issues);
  
  // Analyze component sizes
  const largeComponents = analyzeComponentSizes(analysis.structure);
  
  // Generate strategy
  const strategy = generateStrategy(categories, largeComponents);

  // Display results
  console.log('\n📋 WARNING ANALYSIS SUMMARY');
  console.log('='.repeat(30));
  console.log(`Total Warnings: ${strategy.warnings.total}`);
  console.log(`  • Accessibility Issues: ${strategy.warnings.accessibility}`);
  console.log(`  • Route Configuration: ${strategy.warnings.routing}`);
  console.log(`  • Other Issues: ${strategy.warnings.other}`);

  console.log('\n📦 COMPONENT SIZE ANALYSIS');
  console.log('='.repeat(30));
  console.log(`Large Components: ${strategy.components.total}`);
  console.log(`  • Critical (80KB+): ${strategy.components.critical}`);
  console.log(`  • High (40-80KB): ${strategy.components.high}`);
  console.log(`  • Medium (20-40KB): ${strategy.components.medium}`);

  console.log('\n🎯 TOP 10 LARGEST COMPONENTS');
  console.log('='.repeat(30));
  largeComponents.slice(0, 10).forEach((comp, index) => {
    console.log(`${index + 1}. ${comp.name} (${comp.sizeKB}KB) - ${comp.category.toUpperCase()}`);
  });

  console.log('\n📈 REMEDIATION STRATEGY');
  console.log('='.repeat(30));
  strategy.priorities.forEach(phase => {
    console.log(`\nPhase ${phase.phase}: ${phase.title}`);
    console.log(`  Description: ${phase.description}`);
    console.log(`  Items: ${phase.items}`);
    console.log(`  Effort: ${phase.effort} | Impact: ${phase.impact}`);
  });

  // Save detailed report
  const report = {
    timestamp: new Date().toISOString(),
    summary: strategy,
    categories,
    largeComponents: largeComponents.slice(0, 20), // Top 20 largest
    recommendations: [
      "Start with accessibility fixes - high impact, medium effort",
      "Prioritize critical component refactoring for maintainability",
      "Use established patterns from previous refactoring work",
      "Implement automated checks to prevent regression"
    ]
  };

  fs.writeFileSync('warning-analysis-report.json', JSON.stringify(report, null, 2));
  console.log('\n📄 Detailed report saved to: warning-analysis-report.json');

  return report;
}

// Run analysis
if (process.argv[1] && process.argv[1].endsWith('analyze-warnings.js')) {
  main();
}

export { main as analyzeWarnings };

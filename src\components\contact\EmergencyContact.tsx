import React from 'react';

interface EmergencyItem {
  label: string;
  value: string;
  isLink?: boolean;
}

interface EmergencySection {
  type: 'emergency' | 'hospital' | 'warning';
  title: string;
  items?: EmergencyItem[];
  content?: string;
}

interface EmergencyContactProps {
  title: string;
  sections: EmergencySection[];
}

const EmergencyContact: React.FC<EmergencyContactProps> = ({
  title,
  sections
}) => {
  const getSectionStyles = (type: string) => {
    switch (type) {
      case 'emergency':
        return "p-4 border border-red-300 bg-red-50 dark:bg-red-950/20 rounded-md";
      case 'hospital':
        return "p-4 border border-blue-300 bg-blue-50 dark:bg-blue-950/20 rounded-md";
      case 'warning':
        return "p-3 bg-yellow-50 dark:bg-yellow-950/20 rounded-md border border-yellow-200";
      default:
        return "p-4 border rounded-md";
    }
  };

  const getTitleStyles = (type: string) => {
    switch (type) {
      case 'emergency':
        return "font-bold text-red-700 dark:text-red-400 mb-3";
      case 'hospital':
        return "font-bold text-blue-700 dark:text-blue-400 mb-3";
      case 'warning':
        return "text-sm text-yellow-800 dark:text-yellow-200 font-medium";
      default:
        return "font-bold mb-3";
    }
  };

  const getContentStyles = (type: string) => {
    switch (type) {
      case 'hospital':
        return "space-y-1 text-sm text-blue-700 dark:text-blue-300";
      default:
        return "space-y-2";
    }
  };

  return (
    <div>
      <h3 className="text-xl font-bold mb-4">{title}</h3>
      <div className="glass-card p-6 mb-8">
        <div className="space-y-4">
          {sections.map((section, index) => (
            <div key={index} className={getSectionStyles(section.type)}>
              <h4 className={getTitleStyles(section.type)}>
                {section.title}
              </h4>
              
              {section.items && (
                <div className={getContentStyles(section.type)}>
                  {section.items.map((item, itemIndex) => (
                    <p key={itemIndex} className="text-sm">
                      <span className="font-bold">{item.label}</span>
                      {item.isLink ? (
                        <a 
                          href={item.value} 
                          target="_blank" 
                          rel="noopener noreferrer" 
                          className="underline hover:text-blue-600 ml-1"
                        >
                          {item.value}
                        </a>
                      ) : (
                        <span className={
                          section.type === 'emergency' 
                            ? "text-red-700 dark:text-red-400 font-bold ml-2"
                            : "ml-1"
                        }>
                          {item.value}
                        </span>
                      )}
                    </p>
                  ))}
                </div>
              )}
              
              {section.content && (
                <p className={getTitleStyles(section.type)}>
                  <strong>Important:</strong> {section.content}
                </p>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default EmergencyContact;

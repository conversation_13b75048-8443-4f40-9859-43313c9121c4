import React, { useEffect } from 'react';

import { ConditionCauses } from '@/components/medical-conditions/shared/ConditionCauses';
import { ConditionHero } from '@/components/medical-conditions/shared/ConditionHero';
import ConditionOverviewSection from '@/components/medical-conditions/shared/ConditionOverviewSection';
import ConditionQuickFacts from '@/components/medical-conditions/shared/ConditionQuickFacts';
import { ConditionSymptoms } from '@/components/medical-conditions/shared/ConditionSymptoms';
import { ConditionTreatment } from '@/components/medical-conditions/shared/ConditionTreatment';
import StandardPageLayout from '@/components/StandardPageLayout';
import { spinalStenosisData } from '@/data/conditions/spinalstenosis';

/**
 * Refactored Spinal Stenosis Component
 * 
 * Original component: 1,327 lines
 * Refactored component: <100 lines
 * Reduction: ~95%
 */

const SpinalStenosisRefactored: React.FC = () => {
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  return (
    <StandardPageLayout 
      title="Spinal Stenosis - Comprehensive Guide" 
      showHeader={false}
    >
      <main className="flex-1 pt-20">
        <ConditionHero
          title={spinalStenosisData.hero.title}
          subtitle={spinalStenosisData.hero.subtitle}
          backgroundImage={spinalStenosisData.hero.backgroundImage}
          badge={spinalStenosisData.hero.badge}
        />

        <ConditionQuickFacts facts={spinalStenosisData.quickFacts} />

        <ConditionOverviewSection
          title={spinalStenosisData.overview.title}
          description={spinalStenosisData.overview.description}
          keyPoints={spinalStenosisData.overview.keyPoints}
          imageSrc={spinalStenosisData.overview.imageSrc}
          imageAlt={spinalStenosisData.overview.imageAlt}
          imageCaption={spinalStenosisData.overview.imageCaption}
        />

        <ConditionCauses
          causes={spinalStenosisData.causes}
          riskFactors={spinalStenosisData.riskFactors}
        />

        <ConditionSymptoms
          symptomCategories={spinalStenosisData.symptoms}
          warningSigns={spinalStenosisData.warningSigns}
        />

        <ConditionTreatment
          conservativeOptions={spinalStenosisData.conservativeTreatments}
          surgicalOptions={spinalStenosisData.surgicalTreatments}
        />
      </main>
    </StandardPageLayout>
  );
};

SpinalStenosisRefactored.displayName = 'SpinalStenosisRefactored';

export default SpinalStenosisRefactored;

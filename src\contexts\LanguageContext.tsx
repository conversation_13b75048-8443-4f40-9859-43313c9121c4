import React, { createContext, useContext, useMemo, ReactNode } from 'react';

import ErrorBoundary from '@/components/ErrorBoundary';
import en from '@/locales/en';

type Translations = typeof en;

// Only Australian English is supported
export type SupportedLanguage = 'en';

interface LanguageContextType {
  language: SupportedLanguage;
  t: Translations;
  isLanguageLoaded: boolean;
}

// Only English translations are available
const translations: Translations = en;

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

export const LanguageProvider = ({ children }: { children: ReactNode }) => {
  // Always use English - no language switching needed
  const language: SupportedLanguage = 'en';
  const isLanguageLoaded = true;

  // Ensure translations are always available with validation
  const safeT = useMemo(() => {
    // Always ensure we have a valid translation object
    if (!translations || typeof translations !== 'object') {
      // Fallback structure matching the actual Translations interface
      return {
        navigation: { home: "Home", expertise: "Expertise", contact: "Contact", about: "About", locations: "Locations", patientResources: "Patient Resources", bookAppointment: "Book Appointment", language: "Language", menu: "Menu", close: "Close", skipToContent: "Skip to Content" },
        hero: { title: "Welcome", subtitle: "Professional Care", primaryCTA: "Book Now", secondaryCTA: "Learn More", scrollDown: "Scroll Down" },
        footer: { description: "Professional medical practice", quickLinks: "Quick Links", contactInfo: "Contact Info", followUs: "Follow Us", copyright: "All rights reserved", privacyPolicy: "Privacy Policy", termsOfService: "Terms of Service", accessibility: "Accessibility", sitemap: "Sitemap" },
        cta: { title: "Book Your Consultation", description: "Schedule your appointment today", primaryButton: "Book Now", secondaryButton: "Contact Us", phone: "Phone", email: "Email" },
        about: { title: "About", subtitle: "Professional Care", description: "Expert medical services", experience: "Experience", patients: "Patients", procedures: "Procedures", awards: "Awards", learnMore: "Learn More" },
        expertise: { title: "Expertise", subtitle: "Specialized Care", brainSurgery: { title: "Brain Surgery", description: "Expert brain surgery" }, spineSurgery: { title: "Spine Surgery", description: "Expert spine surgery" }, peripheralNerve: { title: "Nerve Surgery", description: "Expert nerve surgery" }, viewAll: "View All", learnMore: "Learn More" },
        testimonials: { title: "Testimonials", description: "Patient reviews", readMore: "Read More", previous: "Previous", next: "Next" },
        specialties: { title: "Specialties", subtitle: "Expert Care", description: "Specialized treatments", learnMore: "Learn More" },
        patientResources: { title: "Patient Resources", subtitle: "Helpful Information", description: "Resources for patients", learnMore: "Learn More" },
        contact: { title: "Contact", subtitle: "Get in Touch", description: "Contact information", phone: "Phone", email: "Email", address: "Address", hours: "Hours", form: { name: "Name", email: "Email", phone: "Phone", message: "Message", submit: "Submit", success: "Success", error: "Error" } },
        location: { title: "Location", subtitle: "Find Us", description: "Location information", address: "Address", phone: "Phone", hours: "Hours", directions: "Directions" },
        appointment: { title: "Appointment", subtitle: "Book Now", description: "Schedule appointment", form: { name: "Name", email: "Email", phone: "Phone", date: "Date", time: "Time", reason: "Reason", submit: "Submit", success: "Success", error: "Error" } },
        common: { loading: "Loading", error: "Error", success: "Success", cancel: "Cancel", save: "Save", submit: "Submit", close: "Close", back: "Back", next: "Next", previous: "Previous", yes: "Yes", no: "No" }
      } as Translations;
    }

    // Check for essential structure - be more lenient
    if (!translations.navigation || typeof translations.navigation !== 'object') {
      // Translation navigation section missing, falling back to English
      return translations;
    }

    // Return the English translations
    return translations;
  }, []);

  const contextValue = useMemo(() => ({
    language,
    t: safeT,
    isLanguageLoaded
  }), [language, safeT, isLanguageLoaded]);

  return (
    <ErrorBoundary>
      <LanguageContext.Provider value={contextValue}>
        {children}
      </LanguageContext.Provider>
    </ErrorBoundary>
  );
};

export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }

  return context;
};

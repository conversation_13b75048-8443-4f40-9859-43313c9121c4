# Mock Configuration Guide

## Overview

This guide documents the standardized mock configuration system implemented to resolve mock conflicts and ensure consistent testing across the miNEURO application.

## Problem Solved

### Before Standardization
- **Duplicate Mock Definitions**: Same APIs mocked in multiple places
- **Mock Conflicts**: Inconsistent mock implementations causing test failures
- **Poor Mock Isolation**: Mocks leaking between tests
- **Stderr Noise**: Excessive console output during testing

### After Standardization
- **Centralized Mock Management**: Global mocks in `setupTests.ts`
- **Consistent Mock Patterns**: Standardized utilities in `test-utils.tsx`
- **Proper Mock Isolation**: Clean setup/teardown between tests
- **Reduced Noise**: Suppressed irrelevant warnings

## Architecture

### Global Mock Setup (`src/setupTests.ts`)

**Purpose**: Provides global mocks for browser APIs and common dependencies.

**Key Features**:
- Browser API mocks (matchMedia, IntersectionObserver, ResizeObserver, Performance)
- React act() warning suppression for third-party components
- Global cleanup on test completion

```typescript
// Example: Performance API Mock
Object.defineProperty(global, 'performance', {
  value: {
    now: vi.fn(() => Date.now()),
    mark: vi.fn(),
    measure: vi.fn(),
    // ... other methods
  },
  writable: true,
});
```

### Mock Utilities (`src/lib/test-utils.tsx`)

**Purpose**: Provides reusable mock utilities and configurations.

**Key Components**:

#### `mockUtils`
- `createMockProvider()`: Creates mock context providers
- `createMockHook()`: Creates mock hooks with default returns
- `suppressConsoleErrors()`: Suppresses console errors for error boundary tests
- `resetAllMocks()`: Comprehensive mock cleanup

#### `mockConfigurations`
- Pre-configured mock objects for common dependencies
- Device context, language context, SEO hooks, etc.

```typescript
// Example Usage
import { mockUtils, mockConfigurations } from '@/lib/test-utils';

// In test setup
beforeEach(() => {
  mockUtils.resetAllMocks();
});

// For error boundary tests
beforeEach(() => {
  mockUtils.suppressConsoleErrors();
});
```

## Best Practices

### 1. Mock Hierarchy
1. **Global Mocks** (`setupTests.ts`): Browser APIs, universal dependencies
2. **Test-Specific Mocks**: Component-specific dependencies in test files
3. **Inline Mocks**: One-off mocks for specific test cases

### 2. Mock Isolation
```typescript
describe('Component Tests', () => {
  beforeEach(() => {
    // Always reset mocks between tests
    mockUtils.resetAllMocks();
  });
});
```

### 3. Error Boundary Testing
```typescript
describe('Error Boundary', () => {
  let originalError: typeof console.error;
  
  beforeEach(() => {
    // Suppress console errors for intentional errors
    originalError = console.error;
    console.error = vi.fn();
  });
  
  afterEach(() => {
    // Restore console.error
    console.error = originalError;
  });
});
```

### 4. Component-Specific Mocks
```typescript
// Mock dependencies at the top of test files
vi.mock('@/contexts/DeviceContext', () => ({
  DeviceProvider: ({ children }: { children: React.ReactNode }) => children,
  useDeviceDetection: vi.fn(() => mockConfigurations.deviceContext),
}));
```

## Common Mock Patterns

### Context Mocking
```typescript
// Device Context
vi.mock('@/contexts/DeviceContext', () => ({
  DeviceProvider: ({ children }: { children: React.ReactNode }) => children,
  useDeviceDetection: vi.fn(() => ({
    isMobile: false,
    isTablet: false,
    isDesktop: true,
    deviceType: 'desktop',
  })),
}));
```

### Hook Mocking
```typescript
// SEO Hook
vi.mock('@/hooks/useSEO', () => ({
  useSEO: vi.fn(),
}));
```

### Library Mocking
```typescript
// SEO Library
vi.mock('@/lib/seo', () => ({
  generatePageSEO: vi.fn(() => ({
    title: 'Test Page | miNEURO',
    description: 'Test description',
    keywords: 'test, keywords',
  })),
}));
```

## Troubleshooting

### Mock Not Working
1. Check if mock is defined in correct location (global vs test-specific)
2. Ensure mock is called before component import
3. Verify mock reset in `beforeEach`

### Test Isolation Issues
1. Add `mockUtils.resetAllMocks()` to `beforeEach`
2. Check for global state mutations
3. Ensure proper cleanup in `afterEach`

### Console Noise
1. Use `mockUtils.suppressConsoleErrors()` for error boundary tests
2. Check if warnings are properly suppressed in `setupTests.ts`
3. Consider mocking noisy third-party components

## Migration Guide

### From Old Mock System
1. Remove duplicate mock definitions
2. Move browser API mocks to `setupTests.ts`
3. Use `mockUtils.resetAllMocks()` instead of `vi.clearAllMocks()`
4. Apply error suppression for error boundary tests

### Adding New Mocks
1. **Global**: Add to `setupTests.ts` for universal dependencies
2. **Utility**: Add to `mockConfigurations` for reusable mocks
3. **Test-Specific**: Define in individual test files for component-specific needs

## Performance Impact

- **Reduced Test Runtime**: Eliminated mock conflicts and retries
- **Improved Reliability**: Consistent mock behavior across test runs
- **Better Developer Experience**: Cleaner test output and fewer false positives

## Maintenance

### Regular Tasks
1. Review and consolidate duplicate mocks
2. Update mock configurations when APIs change
3. Monitor test performance and mock effectiveness
4. Keep documentation updated with new patterns

### When Adding New Components
1. Identify required mocks
2. Check if existing mock configurations can be reused
3. Add component-specific mocks following established patterns
4. Update documentation if new patterns are introduced

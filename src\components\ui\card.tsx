import React from 'react';

import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

const Card = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    as?: 'article' | 'section' | 'div';
    'aria-label'?: string;
    'aria-labelledby'?: string;
  }
>(({ className, as = 'article', ...props }, ref) => {
  const Component = as;
  return (
    <Component
      ref={ref}
      className={cn(
        "rounded-lg border bg-card text-card-foreground shadow-sm",
        className
      )}
      role={as === 'div' ? 'region' : undefined}
      {...props}
    />
  );
})
Card.displayName = "Card";

const CardHeader = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & { as?: 'header' | 'div' }
>(({ className, as = 'header', ...props }, ref) => {
  // Safe device detection with fallback
  let isMobile = false;
  try {
    const deviceInfo = useDeviceDetection();
    isMobile = deviceInfo.isMobile;
  } catch {
    // Fallback: assume desktop if context not available
    isMobile = false;
  }

  const Component = as;
  return (
    <Component
      ref={ref}
      className={cn(
        "flex flex-col",
        isMobile
          ? "space-y-mobile-sm p-mobile-lg"
          : "space-y-1.5 p-6",
        className
      )}
      {...props}
    />
  );
})
CardHeader.displayName = "CardHeader";

const CardTitle = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLHeadingElement>
>(({ className, ...props }, ref) => (
  <h3
    ref={ref}
    className={cn(
      "text-2xl font-semibold leading-none tracking-tight",
      className
    )}
    {...props}
  />
))
CardTitle.displayName = "CardTitle";

const CardDescription = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, ...props }, ref) => (
  <p
    ref={ref}
    className={cn("text-sm text-muted-foreground", className)}
    {...props}
  />
))
CardDescription.displayName = "CardDescription";

const CardContent = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & { as?: 'section' | 'div' }
>(({ className, as = 'section', ...props }, ref) => {
  // Safe device detection with fallback
  let isMobile = false;
  try {
    const deviceInfo = useDeviceDetection();
    isMobile = deviceInfo.isMobile;
  } catch {
    // Fallback: assume desktop if context not available
    isMobile = false;
  }

  const Component = as;
  return (
    <Component
      ref={ref}
      className={cn(
        "pt-0",
        isMobile ? "p-mobile-lg" : "p-6",
        className
      )}
      {...props}
    />
  );
})
CardContent.displayName = "CardContent";

const CardFooter = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & { as?: 'footer' | 'div' }
>(({ className, as = 'footer', ...props }, ref) => {
  // Safe device detection with fallback
  let isMobile = false;
  try {
    const deviceInfo = useDeviceDetection();
    isMobile = deviceInfo.isMobile;
  } catch {
    // Fallback: assume desktop if context not available
    isMobile = false;
  }

  const Component = as;
  return (
    <Component
      ref={ref}
      className={cn(
        "flex items-center pt-0",
        isMobile ? "p-mobile-lg" : "p-6",
        className
      )}
      {...props}
    />
  );
})
CardFooter.displayName = "CardFooter";

export { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }

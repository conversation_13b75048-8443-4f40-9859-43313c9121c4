import { render } from '@testing-library/react';
import { axe, toHaveNoViolations } from 'jest-axe';
import { describe, it, expect } from 'vitest';

import AppointmentBookingRefactored from '@/pages/AppointmentBookingRefactored';
import ConsultingRoomsRefactored from '@/pages/ConsultingRoomsRefactored';
import Expertise from '@/pages/Expertise';
import FaqRefactored from '@/pages/FaqRefactored';
import PatientResourcesRefactored from '@/pages/PatientResourcesRefactored';
import { EnhancedTestWrapper } from '@/tests/utils/enhanced-test-helpers';
import { setupAllStandardMocks } from '@/tests/utils/standard-mocks';

// Add jest-axe matchers
expect.extend(toHaveNoViolations);

// Setup standardized mocks
setupAllStandardMocks();

const pages = [
  { name: 'FaqRefactored', component: FaqRefactored },
  { name: 'ConsultingRoomsRefactored', component: ConsultingRoomsRefactored },
  { name: 'AppointmentBookingRefactored', component: AppointmentBookingRefactored },
  { name: 'Expertise', component: Expertise },
  { name: 'PatientResourcesRefactored', component: PatientResourcesRefactored }
];

describe('🔍 Batch Test Accessibility Violation Extractor', () => {
  pages.forEach(({ name, component: Component }) => {
    describe(`${name} Page`, () => {
      it('extracts detailed accessibility violations using batch test environment', async () => {
        const { container } = render(
          <EnhancedTestWrapper>
            <Component />
          </EnhancedTestWrapper>
        );

        // Use the same axe configuration as batch test (no custom rules)
        const results = await axe(container);
        
        if (results.violations.length > 0) {
          if (import.meta.env.DEV) {
            console.log(`\n🚨 ${name.toUpperCase()} ACCESSIBILITY VIOLATIONS:`);
          }
          if (import.meta.env.DEV) {
            console.log(`Total violations: ${results.violations.length}`);
          }
          
          results.violations.forEach((violation, index) => {
            if (import.meta.env.DEV) {
              console.log(`\n${index + 1}. Rule: ${violation.id}`);
            }
            if (import.meta.env.DEV) {
              console.log(`   Description: ${violation.description}`);
            }
            if (import.meta.env.DEV) {
              console.log(`   Impact: ${violation.impact}`);
            }
            if (import.meta.env.DEV) {
              console.log(`   Help: ${violation.help}`);
            }
            if (import.meta.env.DEV) {
              console.log(`   Help URL: ${violation.helpUrl}`);
            }
            if (import.meta.env.DEV) {
              console.log(`   Tags: ${violation.tags.join(', ')}`);
            }
            
            violation.nodes.forEach((node, nodeIndex) => {
              if (import.meta.env.DEV) {
                console.log(`\n   Node ${nodeIndex + 1}:`);
              }
              if (import.meta.env.DEV) {
                console.log(`     Target: ${node.target.join(', ')}`);
              }
              if (import.meta.env.DEV) {
                console.log(`     HTML: ${node.html.substring(0, 200)}...`);
              }
              if (import.meta.env.DEV) {
                console.log(`     Failure Summary: ${node.failureSummary}`);
              }
              
              if (node.any && node.any.length > 0) {
                if (import.meta.env.DEV) {
                  console.log(`     Any checks failed:`);
                }
                node.any.forEach((check, checkIndex) => {
                  if (import.meta.env.DEV) {
                    console.log(`       ${checkIndex + 1}. ${check.id}: ${check.message}`);
                  }
                  if (check.data) {
                    if (import.meta.env.DEV) {
                      console.log(`          Data: ${JSON.stringify(check.data, null, 2)}`);
                    }
                  }
                });
              }
              
              if (node.all && node.all.length > 0) {
                if (import.meta.env.DEV) {
                  console.log(`     All checks failed:`);
                }
                node.all.forEach((check, checkIndex) => {
                  if (import.meta.env.DEV) {
                    console.log(`       ${checkIndex + 1}. ${check.id}: ${check.message}`);
                  }
                  if (check.data) {
                    if (import.meta.env.DEV) {
                      console.log(`          Data: ${JSON.stringify(check.data, null, 2)}`);
                    }
                  }
                });
              }
              
              if (node.none && node.none.length > 0) {
                if (import.meta.env.DEV) {
                  console.log(`     None checks failed:`);
                }
                node.none.forEach((check, checkIndex) => {
                  if (import.meta.env.DEV) {
                    console.log(`       ${checkIndex + 1}. ${check.id}: ${check.message}`);
                  }
                  if (check.data) {
                    if (import.meta.env.DEV) {
                      console.log(`          Data: ${JSON.stringify(check.data, null, 2)}`);
                    }
                  }
                });
              }
            });
          });
          
          if (import.meta.env.DEV) {
          
            console.log(`\n📊 ${name} VIOLATION SUMMARY:`);
          
          }
          const violationsByImpact = results.violations.reduce((acc, v) => {
            acc[v.impact] = (acc[v.impact] || 0) + 1;
            return acc;
          }, {} as Record<string, number>);
          if (import.meta.env.DEV) {
            console.log(`   By Impact: ${JSON.stringify(violationsByImpact, null, 2)}`);
          }
          
          const violationsByRule = results.violations.reduce((acc, v) => {
            acc[v.id] = (acc[v.id] || 0) + 1;
            return acc;
          }, {} as Record<string, number>);
          if (import.meta.env.DEV) {
            console.log(`   By Rule: ${JSON.stringify(violationsByRule, null, 2)}`);
          }
          
        } else {
          if (import.meta.env.DEV) {
            console.log(`\n✅ ${name.toUpperCase()}: No accessibility violations found!`);
          }
        }

        // Don't fail the test, just extract and log violations
        expect(results.violations.length).toBeGreaterThanOrEqual(0);
      });
    });
  });
});
import { useEffect } from 'react';

import { useLanguage } from '@/contexts/LanguageContext';
import en from '@/locales/en';

interface PageData {
  title: string;
  subtitle?: string;
  description: string;
  sections: Record<string, unknown>;
}

export const usePageData = (pageKey: string): PageData => {
  const { t } = useLanguage();

  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  // Safe fallback for translations
  const safeT = t || en;
  const pageData = safeT?.[pageKey as keyof typeof safeT] as Record<string, unknown> | undefined;

  // Default fallback data
  const defaultData: PageData = {
    title: 'Page Title',
    subtitle: 'Page Subtitle',
    description: 'Page description',
    sections: {}
  };

  // Extract and structure the data
  return {
    title: pageData?.title || defaultData.title,
    subtitle: pageData?.subtitle || defaultData.subtitle,
    description: pageData?.description || defaultData.description,
    sections: pageData || defaultData.sections
  };
};

export default usePageData;

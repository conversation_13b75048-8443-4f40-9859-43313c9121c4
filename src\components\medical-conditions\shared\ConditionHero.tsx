import { Target, Calendar } from 'lucide-react';
import { Link } from 'react-router-dom';

import SafeImage from '@/components/SafeImage';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface ConditionHeroProps {
  title: string;
  subtitle: string;
  backgroundImage: string;
  fallbackImage?: string;
  badge?: string;
  showAssessment?: boolean;
  showBooking?: boolean;
  assessmentLink?: string;
  bookingLink?: string;
}

export function ConditionHero({
  title,
  subtitle,
  backgroundImage,
  fallbackImage = "https://images.unsplash.com/photo-1559757148-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80",
  badge = "Spine Conditions Library",
  showAssessment = true,
  showBooking = true,
  assessmentLink = "#assessment",
  bookingLink = "/contact"
}: ConditionHeroProps) {
  const deviceInfo = useDeviceDetection();

  return (
    <header className={cn(
      "relative bg-gradient-to-r from-primary/10 to-white dark:from-primary/20 dark:to-background",
      deviceInfo.isMobile ? "py-12" : "py-20"
    )}>
      <div className="absolute inset-0 overflow-hidden opacity-10">
        <SafeImage
          src={backgroundImage}
          alt={`${title} anatomy`}
          className="w-full h-full object-cover"
          fallbackSrc={fallbackImage}
        />
      </div>
      <div className={cn("container", deviceInfo.isMobile ? "px-4" : "")}>
        <div className="text-center max-w-4xl mx-auto">
          <Badge variant="secondary" className="mb-4">
            {badge}
          </Badge>
          <h1 className={cn(
            "font-bold mb-6",
            deviceInfo.isMobile ? "text-3xl" : "text-4xl md:text-5xl"
          )}>
            {title}
          </h1>
          <p className={cn(
            "text-muted-foreground mb-8",
            deviceInfo.isMobile ? "text-base" : "text-lg"
          )}>
            {subtitle}
          </p>
          <div className={cn("flex", deviceInfo.isMobile ? "flex-col items-center" : "flex-row")}>
            {showAssessment && (
              <Button asChild size={deviceInfo.isMobile ? "default" : "lg"}>
                <Link to={assessmentLink}>
                  <Target className="mr-2 h-4 w-4" />
                  Take Assessment
                </Link>
              </Button>
            )}
            {showBooking && (
              <Button asChild variant="outline" size={deviceInfo.isMobile ? "default" : "lg"}>
                <Link to={bookingLink}>
                  <Calendar className="mr-2 h-4 w-4" />
                  Book Consultation
                </Link>
              </Button>
            )}
          </div>
        </div>
      </div>
    </header>
  );
}
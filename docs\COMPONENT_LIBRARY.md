# miNEURO Component Library Documentation

## Overview

The miNEURO component library is a comprehensive collection of reusable React components designed for medical practice websites. All components follow TypeScript best practices, accessibility standards (WCAG 2.1 AA), and modern React patterns.

## Core Architecture Components

### StandardPageLayout
**Location**: `src/components/StandardPageLayout.tsx`
**Purpose**: Foundation layout component providing consistent structure across all pages

#### Props Interface
```typescript
interface StandardPageLayoutProps {
  children: React.ReactNode;
  title?: string;
  subtitle?: string;
  backgroundImage?: string;
  enableParallax?: boolean;
  className?: string;
  seoData?: SEOData;
  pageType?: PageType;
  showHeader?: boolean;
  headerClassName?: string;
  contentClassName?: string;
  enableErrorBoundary?: boolean;
  customErrorFallback?: React.ReactNode;
}
```

#### Features
- ✅ **SEO Optimization**: Automatic meta tag generation
- ✅ **Error Boundary**: Built-in error handling
- ✅ **Responsive Design**: Mobile-first approach
- ✅ **Accessibility**: ARIA attributes and semantic structure
- ✅ **Performance**: Optimized rendering and lazy loading support

#### Usage Example
```typescript
<StandardPageLayout 
  title="Medical Condition Name"
  subtitle="Comprehensive treatment information"
  backgroundImage="/images/medical-hero.jpg"
  enableParallax={true}
  seoData={customSEOData}
>
  <ConditionContent />
</StandardPageLayout>
```

### PageHeader
**Location**: `src/components/PageHeader.tsx`
**Purpose**: Standardized page headers with hero images and parallax effects

#### Props Interface
```typescript
interface PageHeaderProps {
  title: string;
  subtitle?: string;
  backgroundImage?: string;
  className?: string;
  enableParallax?: boolean;
}
```

#### Features
- ✅ **Parallax Effects**: Smooth scrolling animations
- ✅ **Mobile Optimization**: Responsive image handling
- ✅ **Accessibility**: Proper heading hierarchy
- ✅ **Performance**: Optimized image loading

## Medical Condition Components

### ConditionHero
**Location**: `src/components/medical-conditions/ConditionHero.tsx`
**Purpose**: Hero sections specifically designed for medical condition pages

#### Props Interface
```typescript
interface ConditionHeroProps {
  data: {
    title: string;
    subtitle: string;
    backgroundImage: string;
    breadcrumbs?: BreadcrumbItem[];
  };
}
```

#### Features
- ✅ **Medical Context**: Optimized for medical content
- ✅ **Breadcrumb Navigation**: Contextual navigation
- ✅ **Professional Design**: Medical practice aesthetics

### ConditionOverview
**Location**: `src/components/medical-conditions/ConditionOverview.tsx`
**Purpose**: Overview sections with quick facts and key information

#### Props Interface
```typescript
interface ConditionOverviewProps {
  data: {
    title: string;
    content: string[];
    quickFacts: {
      prevalence: string;
      ageGroup: string;
      symptoms: string;
      treatment: string;
    };
    keyPoints?: string[];
  };
}
```

#### Features
- ✅ **Quick Facts Display**: Structured information presentation
- ✅ **Content Sections**: Organized content blocks
- ✅ **Visual Hierarchy**: Clear information architecture

### ConditionSymptoms
**Location**: `src/components/medical-conditions/ConditionSymptoms.tsx`
**Purpose**: Symptom display with categorization and visual indicators

#### Props Interface
```typescript
interface ConditionSymptomsProps {
  data: {
    title: string;
    introduction: string;
    categories: SymptomCategory[];
  };
}

interface SymptomCategory {
  name: string;
  icon: string;
  symptoms: string[];
  description?: string;
}
```

#### Features
- ✅ **Categorized Display**: Organized symptom groups
- ✅ **Visual Indicators**: Icons for easy recognition
- ✅ **Accessibility**: Screen reader optimized

### ConditionCauses
**Location**: `src/components/medical-conditions/ConditionCauses.tsx`
**Purpose**: Causes and risk factors presentation

#### Props Interface
```typescript
interface ConditionCausesProps {
  data: {
    title: string;
    introduction: string;
    primaryCauses: CauseItem[];
    riskFactors: RiskFactor[];
  };
}
```

### ConditionTreatment
**Location**: `src/components/medical-conditions/ConditionTreatment.tsx`
**Purpose**: Treatment options and procedures display

#### Props Interface
```typescript
interface ConditionTreatmentProps {
  data: {
    title: string;
    introduction: string;
    conservativeTreatments: TreatmentOption[];
    interventionalTreatments: TreatmentOption[];
    surgicalOptions?: SurgicalOption[];
  };
}
```

## Navigation Components

### NavbarRefactored
**Location**: `src/components/NavbarRefactored.tsx`
**Purpose**: Main navigation component with responsive design

#### Features
- ✅ **Responsive Design**: Desktop and mobile optimized
- ✅ **Accessibility**: Keyboard navigation and ARIA attributes
- ✅ **Performance**: Memoized for optimal rendering
- ✅ **Multi-language**: Internationalization support

### DesktopNavigation
**Location**: `src/components/navigation/DesktopNavigation.tsx`
**Purpose**: Desktop-specific navigation with dropdown menus

### MobileNavigation
**Location**: `src/components/navigation/MobileNavigation.tsx`
**Purpose**: Mobile-optimized navigation with hamburger menu

## Footer Components

### FooterRefactored
**Location**: `src/components/FooterRefactored.tsx`
**Purpose**: Main footer component with modular sections

#### Features
- ✅ **Modular Design**: Composed of smaller footer components
- ✅ **Contact Information**: Structured contact details
- ✅ **Navigation Links**: Organized link sections
- ✅ **Compliance**: Privacy and legal information

### FooterSection
**Location**: `src/components/footer/FooterSection.tsx`
**Purpose**: Reusable footer sections with consistent styling

### FooterContact
**Location**: `src/components/footer/FooterContact.tsx`
**Purpose**: Contact information display with accessibility features

## Form Components

### AppointmentForm
**Location**: `src/components/AppointmentForm.tsx`
**Purpose**: Comprehensive appointment booking form

#### Features
- ✅ **Validation**: Client-side and server-side validation
- ✅ **Accessibility**: Form labels and error handling
- ✅ **User Experience**: Progressive enhancement
- ✅ **Integration**: Backend API integration

### ContactForm
**Location**: `src/components/contact/ContactForm.tsx`
**Purpose**: General contact form with validation

## UI Components (shadcn/ui)

### Button
**Location**: `src/components/ui/button.tsx`
**Purpose**: Standardized button component with variants

#### Variants
- `default` - Primary button style
- `destructive` - Warning/danger actions
- `outline` - Secondary button style
- `secondary` - Alternative secondary style
- `ghost` - Minimal button style
- `link` - Link-styled button

#### Sizes
- `default` - Standard size
- `sm` - Small size
- `lg` - Large size
- `icon` - Icon-only button

### Card
**Location**: `src/components/ui/card.tsx`
**Purpose**: Content container with consistent styling

#### Components
- `Card` - Main container
- `CardHeader` - Header section
- `CardTitle` - Title component
- `CardDescription` - Description text
- `CardContent` - Main content area
- `CardFooter` - Footer section

### Dialog
**Location**: `src/components/ui/dialog.tsx`
**Purpose**: Modal dialog component

#### Features
- ✅ **Accessibility**: Focus management and ARIA attributes
- ✅ **Keyboard Navigation**: ESC to close, tab navigation
- ✅ **Backdrop**: Click outside to close
- ✅ **Animation**: Smooth open/close transitions

## Utility Components

### SafeImage
**Location**: `src/components/SafeImage.tsx`
**Purpose**: Enhanced image component with error handling and optimization

#### Features
- ✅ **Error Handling**: Automatic fallback images
- ✅ **Lazy Loading**: Intersection Observer implementation
- ✅ **Accessibility**: Proper alt text and ARIA attributes
- ✅ **Performance**: Optimized loading and caching

### EnhancedImage
**Location**: `src/components/EnhancedImage.tsx`
**Purpose**: Advanced image component with WebP support and progressive loading

#### Features
- ✅ **Format Support**: WebP/AVIF with fallbacks
- ✅ **Progressive Loading**: Blur placeholder
- ✅ **Performance Monitoring**: Load time tracking
- ✅ **Responsive**: Automatic sizing

### Loading
**Location**: `src/components/Loading.tsx`
**Purpose**: Loading indicators and skeleton screens

### ErrorBoundary
**Location**: `src/components/ErrorBoundary.tsx`
**Purpose**: Error boundary component for graceful error handling

## Performance Components

### TestimonialsSection
**Location**: `src/components/TestimonialsSection.tsx`
**Purpose**: Testimonial carousel with performance optimizations

#### Features
- ✅ **Memoization**: React.memo for performance
- ✅ **Accessibility**: ARIA labels for carousel
- ✅ **Touch Support**: Mobile swipe gestures
- ✅ **Auto-play**: Optional automatic rotation

### CTASection
**Location**: `src/components/CTASection.tsx`
**Purpose**: Call-to-action sections with conversion optimization

#### Features
- ✅ **Memoization**: Optimized re-rendering
- ✅ **Customizable**: Flexible content and styling
- ✅ **Accessibility**: Proper button and link semantics

## Component Usage Guidelines

### Best Practices

#### 1. TypeScript Usage
```typescript
// Always define proper interfaces
interface ComponentProps {
  title: string;
  description?: string;
  children: React.ReactNode;
}

// Use React.FC with proper typing
const Component: React.FC<ComponentProps> = ({ title, description, children }) => {
  // Component implementation
};
```

#### 2. Accessibility
```typescript
// Use semantic HTML
<section aria-labelledby="section-title">
  <h2 id="section-title">{title}</h2>
  <p>{description}</p>
</section>

// Provide proper ARIA attributes
<button 
  aria-label="Close dialog"
  aria-expanded={isOpen}
  onClick={handleClose}
>
  ×
</button>
```

#### 3. Performance
```typescript
// Memoize components when appropriate
const ExpensiveComponent = React.memo(({ data }) => {
  // Component implementation
});

// Use lazy loading for non-critical components
const LazyComponent = React.lazy(() => import('./LazyComponent'));
```

#### 4. Error Handling
```typescript
// Wrap components in error boundaries
<ErrorBoundary fallback={<ErrorFallback />}>
  <Component />
</ErrorBoundary>

// Handle loading states
{isLoading ? <Loading /> : <Content />}
```

### Component Composition

#### Medical Condition Pages
```typescript
const MedicalConditionPage: React.FC = () => {
  const data = conditionData;
  
  return (
    <StandardPageLayout title={data.hero.title}>
      <ConditionHero data={data.hero} />
      <ConditionOverview data={data.overview} />
      <ConditionSymptoms data={data.symptoms} />
      <ConditionCauses data={data.causes} />
      <ConditionTreatment data={data.treatment} />
      <CTASection {...data.cta} />
    </StandardPageLayout>
  );
};
```

#### Expertise Pages
```typescript
const ExpertisePage: React.FC = () => {
  const data = expertiseData;
  
  return (
    <StandardPageLayout title={data.hero.title}>
      <PageHeader {...data.hero} />
      <ExpertiseOverview data={data.overview} />
      <BenefitsGrid data={data.benefits} />
      <CTASection {...data.cta} />
    </StandardPageLayout>
  );
};
```

## Testing Guidelines

### Component Testing
```typescript
import { render, screen } from '@testing-library/react';
import { Component } from './Component';

describe('Component', () => {
  it('renders correctly', () => {
    render(<Component title="Test Title" />);
    expect(screen.getByText('Test Title')).toBeInTheDocument();
  });

  it('handles user interactions', () => {
    const handleClick = jest.fn();
    render(<Component onClick={handleClick} />);
    fireEvent.click(screen.getByRole('button'));
    expect(handleClick).toHaveBeenCalled();
  });
});
```

### Accessibility Testing
```typescript
import { axe, toHaveNoViolations } from 'jest-axe';

expect.extend(toHaveNoViolations);

it('should not have accessibility violations', async () => {
  const { container } = render(<Component />);
  const results = await axe(container);
  expect(results).toHaveNoViolations();
});
```

This component library provides a solid foundation for building maintainable, accessible, and performant medical practice websites with React and TypeScript.

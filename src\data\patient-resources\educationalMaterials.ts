import { FileDown , LucideIcon } from 'lucide-react';

export interface EducationalMaterial {
  id: string;
  title: string;
  description: string;
  icon: LucideIcon;
  link: string;
  downloadName: string;
}

export const educationalMaterials: EducationalMaterial[] = [
  {
    id: 'brain-tumours',
    title: "Understanding Brain Tumours",
    description: "A comprehensive guide to brain tumour types, symptoms, and treatment options.",
    icon: FileDown,
    link: "/pdfs/brain-tumours-guide.html",
    downloadName: "Brain_Tumours_Guide.html"
  },
  {
    id: 'spine-surgery-recovery',
    title: "Spine Surgery Recovery Guide",
    description: "What to expect during recovery from various types of spine surgery.",
    icon: FileDown,
    link: "/pdfs/spine-surgery-recovery-guide.html",
    downloadName: "Spine_Surgery_Recovery_Guide.html"
  },
  {
    id: 'pain-management',
    title: "Pain Management Strategies",
    description: "Techniques and approaches for managing pain before and after neurosurgery.",
    icon: FileDown,
    link: "/pdfs/pain-management-strategies.html",
    downloadName: "Pain_Management_Strategies.html"
  },
  {
    id: 'disc-replacement',
    title: "Artificial Disc Replacement Guide",
    description: "Information about cervical and lumbar disc replacement procedures.",
    icon: FileDown,
    link: "/pdfs/artificial-disc-replacement-guide.html",
    downloadName: "Artificial_Disc_Replacement_Guide.html"
  }
];

/**
 * Application Constants
 * Centralized constants to ensure consistency across the application
 */

// ============================================================================
// BREAKPOINTS & DEVICE DETECTION
// ============================================================================

/**
 * Breakpoint constants for responsive design
 * These should match Tailwind CSS breakpoints
 */
export const BREAKPOINTS = {
  MOBILE: 768,
  TABLET: 1024,
  DESKTOP: 1280,
  LARGE_DESKTOP: 1536,
} as const;

/**
 * Screen size categories
 */
export const SCREEN_SIZES = {
  XS: 475,
  SM: 640,
  MD: 768,
  LG: 1024,
  XL: 1280,
  '2XL': 1536,
} as const;

// ============================================================================
// ANIMATION & TIMING
// ============================================================================

/**
 * Animation durations in milliseconds
 */
export const ANIMATION_DURATION = {
  FAST: 150,
  NORMAL: 300,
  SLOW: 500,
  VERY_SLOW: 1000,
} as const;

/**
 * Debounce delays for various operations
 */
export const DEBOUNCE_DELAY = {
  SEARCH: 300,
  RESIZE: 100,
  SCROLL: 16,
  INPUT: 500,
} as const;

// ============================================================================
// ACCESSIBILITY
// ============================================================================

/**
 * Minimum touch target sizes (in pixels)
 */
export const TOUCH_TARGET = {
  MIN_SIZE: 44,
  RECOMMENDED_SIZE: 48,
} as const;

/**
 * Focus management constants
 */
export const FOCUS = {
  TRAP_DELAY: 100,
  RESTORE_DELAY: 150,
} as const;

// ============================================================================
// SEO & META
// ============================================================================

/**
 * Default SEO values
 */
export const SEO_DEFAULTS = {
  TITLE_SUFFIX: ' | miNEURO',
  DESCRIPTION_MAX_LENGTH: 160,
  KEYWORDS_MAX_COUNT: 10,
} as const;

/**
 * Supported languages - English only
 */
export const SUPPORTED_LANGUAGES = ['en'] as const;
export type SupportedLanguage = typeof SUPPORTED_LANGUAGES[number];

// ============================================================================
// API & NETWORKING
// ============================================================================

/**
 * HTTP status codes
 */
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  INTERNAL_SERVER_ERROR: 500,
} as const;

/**
 * Request timeouts (in milliseconds)
 */
export const TIMEOUT = {
  API_REQUEST: 10000,
  IMAGE_LOAD: 5000,
  SCRIPT_LOAD: 3000,
} as const;

// ============================================================================
// FORM VALIDATION
// ============================================================================

/**
 * Form validation patterns
 */
export const VALIDATION_PATTERNS = {
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  PHONE: /^[+]?[1-9][\d]{0,15}$/,
  POSTAL_CODE: /^[0-9]{4}$/,
} as const;

/**
 * Input length limits
 */
export const INPUT_LIMITS = {
  NAME_MIN: 2,
  NAME_MAX: 50,
  EMAIL_MAX: 254,
  PHONE_MAX: 20,
  MESSAGE_MAX: 1000,
} as const;

// ============================================================================
// STORAGE KEYS
// ============================================================================

/**
 * Local storage keys
 */
export const STORAGE_KEYS = {
  LANGUAGE: 'language',
  THEME: 'theme',
  USER_PREFERENCES: 'userPreferences',
  DEVICE_INFO: 'deviceInfo',
} as const;

// ============================================================================
// ERROR MESSAGES
// ============================================================================

/**
 * Common error messages
 */
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Network error. Please check your connection.',
  VALIDATION_ERROR: 'Please check your input and try again.',
  GENERIC_ERROR: 'Something went wrong. Please try again.',
  NOT_FOUND: 'The requested resource was not found.',
} as const;

// ============================================================================
// FEATURE FLAGS
// ============================================================================

/**
 * Feature flags for conditional functionality
 */
export const FEATURE_FLAGS = {
  ENABLE_ANALYTICS: true,
  ENABLE_SERVICE_WORKER: false, // Disabled due to MIME type issues
  ENABLE_PWA: true,
  ENABLE_PERFORMANCE_MONITORING: true,
} as const;

// ============================================================================
// MEDICAL SPECIFIC CONSTANTS
// ============================================================================

/**
 * Medical procedure categories
 */
export const PROCEDURE_CATEGORIES = [
  'brain-surgery',
  'spine-surgery',
  'peripheral-nerve',
  'pediatric-neurosurgery',
  'functional-neurosurgery',
  'tumor-surgery',
  'vascular-neurosurgery',
  'trauma-surgery',
] as const;

export type ProcedureCategory = typeof PROCEDURE_CATEGORIES[number];

/**
 * Appointment types
 */
export const APPOINTMENT_TYPES = [
  'consultation',
  'follow-up',
  'procedure',
  'emergency',
] as const;

export type AppointmentType = typeof APPOINTMENT_TYPES[number];

// ============================================================================
// EXPORTS
// ============================================================================

/**
 * Re-export commonly used constants for convenience
 */
export const MOBILE_BREAKPOINT = BREAKPOINTS.MOBILE;
export const TABLET_BREAKPOINT = BREAKPOINTS.TABLET;
export const DESKTOP_BREAKPOINT = BREAKPOINTS.DESKTOP;

/**
 * Type exports
 */
export type BreakpointKey = keyof typeof BREAKPOINTS;
export type ScreenSizeKey = keyof typeof SCREEN_SIZES;
export type AnimationDurationKey = keyof typeof ANIMATION_DURATION;

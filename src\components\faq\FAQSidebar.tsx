import React, { useState } from 'react';
import { ChevronRight, Search, Filter } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { FAQCategory } from '@/data/faq/faqData';
import { cn } from '@/lib/utils';

interface FAQSidebarProps {
  categories: FAQCategory[];
  title: string;
  activeCategory?: number;
  onCategoryClick?: (index: number) => void;
  onSearch?: (query: string) => void;
}

const FAQSidebar: React.FC<FAQSidebarProps> = ({
  categories,
  title,
  activeCategory,
  onCategoryClick,
  onSearch
}) => {
  const deviceInfo = useDeviceDetection();
  const [searchQuery, setSearchQuery] = useState('');

  const handleSearch = (value: string) => {
    setSearchQuery(value);
    onSearch?.(value);
  };

  const handleCategoryClick = (index: number) => {
    onCategoryClick?.(index);
    // Smooth scroll to category
    const element = document.getElementById(`category-${index}`);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  };

  return (
    <div className={cn(
      "bg-gradient-to-b from-slate-50 to-white dark:from-slate-900 dark:to-slate-800 rounded-xl border shadow-sm",
      deviceInfo.isMobile ? "order-2 p-4" : "lg:col-span-1 p-6"
    )}>
      <div className={cn(
        deviceInfo.isMobile
          ? "space-y-4"
          : "sticky top-24 space-y-6"
      )}>
        {/* Enhanced Header */}
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <Filter className="h-5 w-5 text-primary" />
            <h2
              id="faq-categories-heading"
              className={cn(
                "font-bold text-slate-900 dark:text-slate-100",
                deviceInfo.isMobile ? "text-lg" : "text-xl"
              )}
            >
              {title}
            </h2>
          </div>
          <p className="text-sm text-muted-foreground">
            Browse by topic or search for specific questions
          </p>
        </div>

        {/* Search Box */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            type="text"
            placeholder="Search FAQs..."
            value={searchQuery}
            onChange={(e) => handleSearch(e.target.value)}
            className="pl-10 bg-white dark:bg-slate-800 border-slate-200 dark:border-slate-700 focus:border-primary focus:ring-primary"
          />
        </div>

        {/* Enhanced Navigation */}
        <nav aria-labelledby="faq-categories-heading">
          <ul className="space-y-2">
            {categories.map((category, index) => {
              const isActive = activeCategory === index;
              const questionCount = category.questions.length;

              return (
                <li key={category.id}>
                  <Button
                    variant={isActive ? "default" : "ghost"}
                    className={cn(
                      "w-full justify-between group transition-all duration-200 h-auto p-3",
                      isActive
                        ? "bg-primary text-primary-foreground shadow-md"
                        : "hover:bg-slate-100 dark:hover:bg-slate-800 hover:shadow-sm"
                    )}
                    onClick={() => handleCategoryClick(index)}
                  >
                    <div className="flex flex-col items-start text-left">
                      <span className={cn(
                        "font-medium text-sm",
                        isActive ? "text-primary-foreground" : "text-slate-900 dark:text-slate-100"
                      )}>
                        {category.title}
                      </span>
                      <span className={cn(
                        "text-xs mt-1",
                        isActive
                          ? "text-primary-foreground/80"
                          : "text-muted-foreground"
                      )}>
                        {questionCount} question{questionCount !== 1 ? 's' : ''}
                      </span>
                    </div>
                    <ChevronRight className={cn(
                      "h-4 w-4 transition-transform duration-200",
                      isActive
                        ? "text-primary-foreground rotate-90"
                        : "text-muted-foreground group-hover:translate-x-1"
                    )} />
                  </Button>
                </li>
              );
            })}
          </ul>
        </nav>

        {/* Quick Stats */}
        <div className="pt-4 border-t border-slate-200 dark:border-slate-700">
          <div className="bg-slate-50 dark:bg-slate-800 rounded-lg p-3">
            <div className="text-center">
              <div className="text-2xl font-bold text-primary">
                {categories.reduce((total, cat) => total + cat.questions.length, 0)}
              </div>
              <div className="text-xs text-muted-foreground">
                Total Questions
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

FAQSidebar.displayName = 'FAQSidebar';

export default FAQSidebar;

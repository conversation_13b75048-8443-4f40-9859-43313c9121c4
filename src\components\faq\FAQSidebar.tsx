import React from 'react';

import { Button } from '@/components/ui/button';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { FAQCategory } from '@/data/faq/faqData';
import { cn } from '@/lib/utils';

interface FAQSidebarProps {
  categories: FAQCategory[];
  title: string;
}

const FAQSidebar: React.FC<FAQSidebarProps> = ({ categories, title }) => {
  const deviceInfo = useDeviceDetection();

  return (
    <div className={cn(
      deviceInfo.isMobile ? "order-2" : "lg:col-span-1"
    )}>
      <div className={cn(
        deviceInfo.isMobile
          ? "space-y-mobile-sm"
          : "sticky top-24 space-y-2"
      )}>
        <h2
          id="faq-categories-heading"
          className={cn(
            "font-bold mb-mobile-md",
            deviceInfo.isMobile ? "mobile-subheading" : "text-xl mb-4"
          )}
        >
          {title}
        </h2>
        <nav aria-labelledby="faq-categories-heading">
          <ul className="space-y-2">
            {categories.map((category, index) => (
              <li key={category.id}>
                <Button
                  variant="ghost"
                  className="w-full justify-start"
                  asChild
                >
                  <a
                    href={`#category-${index}`}
                    aria-describedby={`category-${index}-description`}
                  >
                    {category.title}
                  </a>
                </Button>
              </li>
            ))}
          </ul>
        </nav>
      </div>
    </div>
  );
};

FAQSidebar.displayName = 'FAQSidebar';

export default FAQSidebar;

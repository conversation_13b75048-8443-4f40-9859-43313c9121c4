import React, { useState } from 'react';
import { ChevronRight, Search, Filter } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { FAQCategory } from '@/data/faq/faqData';
import { cn } from '@/lib/utils';

interface FAQSidebarProps {
  categories: FAQCategory[];
  title: string;
  activeCategory?: number;
  onCategoryClick?: (index: number) => void;
  onSearch?: (query: string) => void;
}

const FAQSidebar: React.FC<FAQSidebarProps> = ({
  categories,
  title,
  activeCategory,
  onCategoryClick,
  onSearch
}) => {
  const deviceInfo = useDeviceDetection();
  const [searchQuery, setSearchQuery] = useState('');

  const handleSearch = (value: string) => {
    setSearchQuery(value);
    onSearch?.(value);
  };

  const handleCategoryClick = (index: number) => {
    onCategoryClick?.(index);
    // Smooth scroll to category
    const element = document.getElementById(`category-${index}`);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  };

  return (
    <div className={cn(
      "bg-gradient-to-br from-white via-blue-50/30 to-indigo-50/20 dark:from-slate-900 dark:via-slate-800/90 dark:to-slate-900",
      "rounded-2xl border border-slate-200/60 dark:border-slate-700/50",
      "shadow-2xl shadow-slate-200/40 dark:shadow-slate-900/60",
      "backdrop-blur-sm relative overflow-hidden",
      "animate-slide-in-left",
      deviceInfo.isMobile ? "order-2 p-5" : "lg:col-span-1 p-7"
    )}>
      {/* Decorative background elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-primary/3 via-transparent to-blue-500/3 pointer-events-none" />
      <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-bl from-primary/10 to-transparent rounded-full blur-2xl pointer-events-none" />

      <div className={cn(
        "relative z-10",
        deviceInfo.isMobile
          ? "space-y-5"
          : "sticky top-24 space-y-7"
      )}>
        {/* Premium Header */}
        <div className="space-y-3">
          <div className="flex items-center gap-3">
            <div className="p-2.5 bg-gradient-to-br from-primary via-blue-600 to-indigo-600 rounded-xl shadow-lg shadow-primary/25">
              <Filter className="h-5 w-5 text-white" />
            </div>
            <h2
              id="faq-categories-heading"
              className={cn(
                "font-bold bg-gradient-to-r from-slate-900 via-slate-700 to-slate-900 dark:from-slate-100 dark:via-slate-200 dark:to-slate-100 bg-clip-text text-transparent",
                deviceInfo.isMobile ? "text-xl" : "text-2xl"
              )}
            >
              {title}
            </h2>
          </div>
          <p className="text-sm text-slate-600 dark:text-slate-400 font-medium leading-relaxed">
            Browse by topic or search for specific questions
          </p>
        </div>

        {/* Premium Search Box */}
        <div className="relative group">
          <div className="absolute inset-0 bg-gradient-to-r from-primary/20 via-blue-500/20 to-indigo-500/20 rounded-xl blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
          <div className="relative">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-500 dark:text-slate-400 group-hover:text-primary transition-colors duration-200" />
            <Input
              type="text"
              placeholder="Search FAQs..."
              value={searchQuery}
              onChange={(e) => handleSearch(e.target.value)}
              className="pl-12 pr-4 py-3 bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm border-slate-200/60 dark:border-slate-700/60 focus:border-primary focus:ring-primary/20 focus:ring-2 rounded-xl shadow-sm hover:shadow-md transition-all duration-200 font-medium"
            />
          </div>
        </div>

        {/* Premium Navigation */}
        <nav aria-labelledby="faq-categories-heading">
          <ul className="space-y-3">
            {categories.map((category, index) => {
              const isActive = activeCategory === index;
              const questionCount = category.questions.length;

              return (
                <li key={category.id}>
                  <Button
                    variant="ghost"
                    className={cn(
                      "w-full justify-between group transition-all duration-300 h-auto p-4 relative overflow-hidden",
                      "border border-transparent hover:border-slate-200/60 dark:hover:border-slate-700/60",
                      "rounded-xl shadow-sm hover:shadow-lg",
                      isActive
                        ? "bg-gradient-to-r from-primary via-blue-600 to-indigo-600 text-white shadow-lg shadow-primary/25 border-primary/20 scale-[1.02]"
                        : "bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm hover:bg-white/80 dark:hover:bg-slate-800/80 hover:scale-[1.01]"
                    )}
                    onClick={() => handleCategoryClick(index)}
                  >
                    {/* Background glow for active state */}
                    {isActive && (
                      <div className="absolute inset-0 bg-gradient-to-r from-primary/20 via-blue-500/20 to-indigo-500/20 blur-xl" />
                    )}

                    <div className="flex flex-col items-start text-left relative z-10">
                      <span className={cn(
                        "font-semibold text-sm",
                        isActive ? "text-white" : "text-slate-900 dark:text-slate-100"
                      )}>
                        {category.title}
                      </span>
                      <span className={cn(
                        "text-xs mt-1 font-medium",
                        isActive
                          ? "text-white/90"
                          : "text-slate-600 dark:text-slate-400"
                      )}>
                        {questionCount} question{questionCount !== 1 ? 's' : ''}
                      </span>
                    </div>
                    <ChevronRight className={cn(
                      "h-4 w-4 transition-transform duration-200",
                      isActive
                        ? "text-primary-foreground rotate-90"
                        : "text-muted-foreground group-hover:translate-x-1"
                    )} />
                  </Button>
                </li>
              );
            })}
          </ul>
        </nav>

        {/* Quick Stats */}
        <div className="pt-4 border-t border-slate-200 dark:border-slate-700">
          <div className="bg-slate-50 dark:bg-slate-800 rounded-lg p-3">
            <div className="text-center">
              <div className="text-2xl font-bold text-primary">
                {categories.reduce((total, cat) => total + cat.questions.length, 0)}
              </div>
              <div className="text-xs text-muted-foreground">
                Total Questions
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

FAQSidebar.displayName = 'FAQSidebar';

export default FAQSidebar;

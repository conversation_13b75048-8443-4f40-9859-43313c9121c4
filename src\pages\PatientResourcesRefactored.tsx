import React, { useEffect } from 'react';

import PageHeader from '@/components/PageHeader';
import AppointmentCallToActionSection from '@/components/patient-resources/AppointmentCallToActionSection';
import EducationalMaterialsSection from '@/components/patient-resources/EducationalMaterialsSection';
import ExpandedResourcesSection from '@/components/patient-resources/ExpandedResourcesSection';
import ResourceCategoriesSection from '@/components/patient-resources/ResourceCategoriesSection';
import SpineHealthAppSection from '@/components/patient-resources/SpineHealthAppSection';
import StandardPageLayout from '@/components/StandardPageLayout';
import { useLanguage } from '@/contexts/LanguageContext';
import { educationalMaterials } from '@/data/patient-resources/educationalMaterials';
import { expandedResources } from '@/data/patient-resources/expandedResources';
import { getResourceCategories } from '@/data/patient-resources/resourceCategories';
import en from '@/locales/en';

/**
 * Patient Resources Page Component
 * Refactored to use modular components and data-driven approach
 */

const PatientResourcesRefactored: React.FC = () => {
  const { t } = useLanguage();

  // Safe fallback for translations
  const safeT = t || en;
  const finalT = (safeT && safeT.patientResources && safeT.patientResources.categories && safeT.patientResources.resources) ? safeT : {
    patientResources: {
      title: "Patient Resources",
      subtitle: "Comprehensive resources to support your healthcare journey",
      categories: {
        beforeVisit: "Before Your Visit",
        conditionsTreatments: "Conditions & Treatments",
        surgeryInfo: "Surgery Information",
        patientSupport: "Patient Support"
      },
      resources: {
        newPatientForms: "New Patient Forms",
        newPatientFormsDesc: "Download and complete forms before your appointment",
        insuranceInfo: "Insurance Information",
        insuranceInfoDesc: "Understanding your insurance coverage and benefits",
        preparingForAppointment: "Preparing for Your Appointment",
        preparingForAppointmentDesc: "What to expect and how to prepare",
        brainConditions: "Brain Conditions",
        brainConditionsDesc: "Information about brain tumours and neurological conditions",
        spineConditions: "Spine Conditions",
        spineConditionsDesc: "Comprehensive spine condition information and treatments",
        minimallyInvasive: "Minimally Invasive Surgery",
        minimallyInvasiveDesc: "Advanced surgical techniques for better outcomes",
        preSurgeryInstructions: "Pre-Surgery Instructions",
        preSurgeryInstructionsDesc: "Important guidelines before your surgery",
        postSurgeryCare: "Post-Surgery Care",
        postSurgeryCareDesc: "Recovery guidelines and care instructions",
        hospitalInfo: "Hospital Information",
        hospitalInfoDesc: "Information about our partner hospitals",
        supportGroups: "Support Groups",
        supportGroupsDesc: "Connect with others facing similar challenges",
        rehabilitationResources: "Rehabilitation Resources",
        rehabilitationResourcesDesc: "Resources to support your recovery",
        mentalHealthSupport: "Mental Health Support",
        mentalHealthSupportDesc: "Psychological support during your healthcare journey"
      },
      educationalMaterials: "Educational Materials",
      needAppointment: "Need an Appointment?",
      appointmentDesc: "Schedule a consultation with our expert team",
      viewInstructions: "View Instructions",
      viewHospitals: "View Hospitals",
      findSupport: "Find Support",
      viewResources: "View Resources"
    }
  };

  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  // Get data using helper functions
  const resourceCategories = getResourceCategories(finalT);

  // Spine Health App data
  const spineHealthAppData = {
    title: "Spine Health App",
    description: "Our new Spine Health App provides personalized guidance for patients with chronic neck and back problems. Based on evidence-based medicine, biomechanics, and neurophysiology, the app helps you understand your condition and offers tailored recommendations for improvement.",
    features: [
      "Comprehensive educational content about spine conditions",
      "Personalized exercise recommendations",
      "Detailed assessment tools",
      "Progress tracking and goal setting",
      "Visual demonstrations of exercises"
    ],
    primaryButtonText: "Explore Spine Health App",
    primaryButtonLink: "/patient-resources/individual-spine-health-programme",
    secondaryButtonText: "Take Assessment",
    secondaryButtonLink: "/patient-resources/individual-spine-health-programme#assessment",
    imageSrc: "/images/individual-spine-health-1.jpg",
    imageAlt: "Spine Health App Preview"
  };

  return (
    <StandardPageLayout showHeader={false}>
      <PageHeader
        title={finalT.patientResources.title}
        subtitle={finalT.patientResources.subtitle}
        backgroundImage="/images/patient-resources/spine-health-hero.jpg"
        enableParallax={true}
      />

      <main className="flex-1">
        {/* Resource Categories */}
        <ResourceCategoriesSection categories={resourceCategories} />

        {/* Educational Materials */}
        <EducationalMaterialsSection 
          materials={educationalMaterials} 
          title={finalT.patientResources.educationalMaterials} 
        />

        {/* Spine Health App */}
        <SpineHealthAppSection {...spineHealthAppData} />

        {/* Expanded Resources */}
        <ExpandedResourcesSection 
          resources={expandedResources} 
          title="Expanded Patient Resources" 
        />

        {/* Individual Spine Health Program - Duplicate section removed for cleaner design */}

        {/* Appointment Call to Action */}
        <AppointmentCallToActionSection
          title={finalT.patientResources.needAppointment}
          description={finalT.patientResources.appointmentDesc}
          primaryButtonText="Book Appointment"
          primaryButtonLink="/appointments"
          secondaryButtonText="Contact Us"
          secondaryButtonLink="/contact"
        />
      </main>
    </StandardPageLayout>
  );
};

PatientResourcesRefactored.displayName = 'PatientResourcesRefactored';

export default PatientResourcesRefactored;

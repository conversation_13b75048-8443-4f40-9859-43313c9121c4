export interface TreatmentModality {
  id: string;
  title: string;
  imageSrc?: string;
  imageAlt?: string;
  benefits: string[];
  considerations: string[];
  learnMoreLink: string;
}

export const treatmentModalities: TreatmentModality[] = [
  {
    id: 'weight-management',
    title: 'Weight Management',
    imageSrc: '/images/patient-resources/weight-management.jpg',
    imageAlt: 'Weight management',
    benefits: [
      'Reduces pressure on spinal discs and joints',
      'Decreases inflammatory markers in the body',
      'Improves mobility and function',
      'Enhances effectiveness of other treatments'
    ],
    considerations: [
      'Requires consistent lifestyle changes',
      'Results may take time to impact symptoms',
      'Should be approached gradually with medical guidance'
    ],
    learnMoreLink: '/patient-resources/individual-spine-health-program'
  },
  {
    id: 'smoking-cessation',
    title: 'Smoking Cessation',
    imageSrc: '/images/patient-resources/smoking-cessation.jpg',
    imageAlt: 'Smoking cessation',
    benefits: [
      'Improves blood flow to spinal discs and tissues',
      'Enhances healing and recovery',
      'Reduces risk of surgical complications',
      'Slows disc degeneration process'
    ],
    considerations: [
      'May require multiple attempts and support strategies',
      'Withdrawal symptoms can be challenging',
      'Benefits to spine health increase over time'
    ],
    learnMoreLink: '/patient-resources/individual-spine-health-program'
  },
  {
    id: 'physiotherapy',
    title: 'Physiotherapy',
    imageSrc: '/images/patient-resources/physiotherapy.jpg',
    imageAlt: 'Physiotherapy',
    benefits: [
      'Improves strength, flexibility, and posture',
      'Reduces pain through targeted exercises',
      'Teaches proper body mechanics',
      'Non-invasive approach with minimal risks'
    ],
    considerations: [
      'Requires consistent participation and home exercises',
      'Results may take weeks or months',
      'May not be sufficient for severe structural problems'
    ],
    learnMoreLink: '/patient-resources/individual-spine-health-program'
  },
  {
    id: 'hydrotherapy',
    title: 'Hydrotherapy',
    imageSrc: '/images/patient-resources/hydrotherapy.jpg',
    imageAlt: 'Hydrotherapy',
    benefits: [
      'Reduces weight-bearing stress on joints',
      'Provides natural resistance for muscle strengthening',
      'Warm water helps relax muscles and reduce pain',
      'Improves circulation and reduces inflammation'
    ],
    considerations: [
      'Requires access to appropriate facilities',
      'May not be suitable for those with certain skin conditions',
      'Should be performed under professional guidance initially'
    ],
    learnMoreLink: '/patient-resources/individual-spine-health-program'
  },
  {
    id: 'kaiser-program',
    title: 'Kaiser Program',
    benefits: [
      'Structured approach to spine rehabilitation',
      'Combines exercise, education, and behavioral strategies',
      'Focuses on functional improvement and pain management',
      'Evidence-based protocols with proven outcomes'
    ],
    considerations: [
      'Requires significant time commitment',
      'May not be available in all locations',
      'Best results require full participation in all program components'
    ],
    learnMoreLink: '/patient-resources/individual-spine-health-program'
  },
  {
    id: 'pain-management',
    title: 'Pain Management',
    benefits: [
      'Multimodal approach to controlling pain',
      'Can include medications, injections, and non-pharmacological methods',
      'Improves quality of life and function',
      'Can be tailored to individual needs and preferences'
    ],
    considerations: [
      'Some medications carry risks of side effects or dependency',
      'Addresses symptoms rather than underlying causes',
      'Most effective when combined with other treatment approaches'
    ],
    learnMoreLink: '/patient-resources/individual-spine-health-program'
  }
];

export const getTreatmentModalityById = (id: string): TreatmentModality | undefined => {
  return treatmentModalities.find(modality => modality.id === id);
};

export const getModalitiesWithImages = (): TreatmentModality[] => {
  return treatmentModalities.filter(modality => modality.imageSrc);
};

export const getModalitiesWithoutImages = (): TreatmentModality[] => {
  return treatmentModalities.filter(modality => !modality.imageSrc);
};

{"name": "vite_react_shadcn_ts", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "npm run type-check && vite build", "build:dev": "vite build --mode development", "lint": "eslint .", "lint:fix": "eslint . --fix", "lint:unused": "eslint . --ext .ts,.tsx --rule 'unused-imports/no-unused-imports: error'", "lint:unused-fix": "eslint . --ext .ts,.tsx --rule 'unused-imports/no-unused-imports: error' --fix", "lint:imports": "eslint . --ext .ts,.tsx --rule 'import/order: error' --fix", "lint:staged": "lint-staged", "type-check": "tsc --noEmit", "type-check:watch": "tsc --noEmit --watch", "format": "prettier --write .", "format:check": "prettier --check .", "pre-commit": "npm run type-check", "prepare": "husky", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "test:watch": "vitest --watch", "analyze": "npm run build && npx vite-bundle-analyzer dist/stats.html", "check-component-sizes": "node scripts/check-component-sizes.js", "validate-components": "npm run check-component-sizes", "analyze:size": "npm run build && npx bundlesize", "update-browserslist": "npx update-browserslist-db@latest", "production-check": "npm run type-check && npm run lint && npm run build", "build:production": "vite build --mode production", "build:analyze": "npm run build && npx rollup-plugin-visualizer dist/stats.html --open", "security-audit": "npm audit --audit-level moderate", "deps-check": "npx npm-check-updates", "clean": "rimraf dist node_modules/.vite", "verify-production-env": "node scripts/verify-production-env.js", "verify-no-console": "node scripts/verify-no-console.js", "analyze-bundle": "npm run build && node scripts/analyze-bundle.js", "detect-duplicates": "node scripts/detect-duplicates.js", "quality-check": "npm run detect-duplicates && npm run analyze-bundle", "pre-deploy": "npm run verify-production-env && npm run verify-no-console && npm run quality-check && npm run production-check"}, "dependencies": {"@hookform/resolvers": "^3.9.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.4", "@tanstack/react-query": "^5.56.2", "caniuse-lite": "^1.0.30001716", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "embla-carousel-react": "^8.3.0", "input-otp": "^1.2.4", "lucide-react": "^0.462.0", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.53.0", "react-resizable-panels": "^2.1.3", "react-router-dom": "^6.26.2", "recharts": "^2.12.7", "sonner": "^1.5.0", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.3", "zod": "^3.23.8"}, "devDependencies": {"@eslint/js": "^9.9.0", "@tailwindcss/typography": "^0.5.15", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^22.5.5", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react-swc": "^3.5.0", "@vitest/coverage-v8": "^3.2.4", "@vitest/ui": "^3.2.4", "autoprefixer": "^10.4.20", "eslint": "^9.9.0", "eslint-plugin-import": "^2.32.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "eslint-plugin-unused-imports": "^4.1.4", "globals": "^15.9.0", "husky": "^9.1.7", "jest-axe": "^10.0.0", "jsdom": "^26.1.0", "lint-staged": "^16.1.2", "lovable-tagger": "^1.1.7", "postcss": "^8.4.47", "prettier": "^3.6.2", "puppeteer": "^24.10.2", "rimraf": "^6.0.1", "tailwindcss": "^3.4.11", "terser": "^5.42.0", "typescript": "^5.5.3", "typescript-eslint": "^8.0.1", "vite": "^5.4.1", "vitest": "^3.2.4"}, "bundlesize": [{"path": "./dist/assets/*.js", "maxSize": "250 kB", "compression": "gzip"}, {"path": "./dist/assets/*.css", "maxSize": "50 kB", "compression": "gzip"}], "lint-staged": {"*.{ts,tsx}": ["eslint --fix"]}}
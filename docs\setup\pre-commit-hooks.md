# Pre-commit Hooks Setup

This document describes the pre-commit hooks configuration using Husky and lint-staged.

## Overview

Pre-commit hooks automatically run quality checks before each commit to ensure code quality and prevent errors from entering the repository.

## What's Configured

### 1. Husky Configuration
- **Location**: `.husky/pre-commit`
- **Purpose**: Git hook that runs before each commit
- **Actions**: Runs TypeScript checking and linting on staged files

### 2. Lint-staged Configuration
- **Location**: `package.json` → `lint-staged` section
- **Purpose**: Runs tools only on staged files for performance
- **Rules**:
  - `*.{ts,tsx}`: ESLint with auto-fix + Prettier formatting
  - `*.{js,jsx,ts,tsx,json,css,md}`: Prettier formatting

### 3. Package Scripts
- `npm run type-check`: TypeScript compilation check
- `npm run lint`: ESLint linting
- `npm run lint:staged`: Run lint-staged
- `npm run format`: Format all files with Prettier
- `npm run pre-commit`: Manual pre-commit check

## How It Works

1. **Dev<PERSON>per runs `git commit`**
2. **<PERSON><PERSON> intercepts** the commit process
3. **TypeScript check** runs on entire codebase
4. **Lint-staged runs** on only staged files:
   - ESLint with auto-fix for TypeScript files
   - Prettier formatting for all applicable files
5. **If any check fails**, commit is blocked
6. **If all checks pass**, commit proceeds

## Benefits

- ✅ **Prevents TypeScript errors** from being committed
- ✅ **Ensures consistent code formatting**
- ✅ **Catches linting issues** before they reach the repository
- ✅ **Automatic code fixes** where possible
- ✅ **Fast execution** (only checks staged files)

## Manual Usage

You can run the same checks manually:

```bash
# Run type checking
npm run type-check

# Run linting
npm run lint

# Run formatting
npm run format

# Run the full pre-commit check
npm run pre-commit
```

## Bypassing Hooks (Emergency Only)

In rare cases where you need to bypass the hooks:

```bash
git commit --no-verify -m "emergency commit message"
```

**⚠️ Warning**: Only use `--no-verify` in genuine emergencies. Always fix the issues afterward.

## Configuration Files

- `.husky/pre-commit`: The actual hook script
- `.prettierrc`: Prettier configuration
- `.prettierignore`: Files to exclude from Prettier
- `package.json`: lint-staged configuration

## Troubleshooting

### Hook Not Running
1. Ensure Husky is installed: `npm install`
2. Check if `.husky/pre-commit` exists and is executable
3. Verify Git hooks are enabled: `git config core.hooksPath`

### Performance Issues
- Lint-staged only processes staged files for speed
- If still slow, consider excluding large generated files in `.prettierignore`

### False Positives
- Update ESLint rules in `eslint.config.js`
- Adjust Prettier settings in `.prettierrc`
- Use `// eslint-disable-next-line` for specific exceptions

## Integration with CI/CD

The same checks should run in your CI/CD pipeline:

```yaml
# Example GitHub Actions
- name: Type Check
  run: npm run type-check

- name: Lint
  run: npm run lint

- name: Format Check
  run: npm run format:check
```

This ensures that even if hooks are bypassed, the CI/CD will catch issues.

import { LucideIcon, AlertTriangle } from 'lucide-react';

import { Badge } from '@/components/ui/badge';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface SymptomItem {
  icon: LucideIcon;
  title: string;
  description: string;
  symptoms: string[];
  severity?: 'mild' | 'moderate' | 'severe';
  frequency?: 'common' | 'occasional' | 'rare';
}

interface WarningSign {
  sign: string;
  description: string;
  urgency: 'immediate' | 'urgent' | 'monitor';
}

interface ConditionSymptomsProps {
  title?: string;
  subtitle?: string;
  symptomCategories: SymptomItem[];
  warningSigns?: WarningSign[];
  className?: string;
}

const severityColors = {
  mild: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
  moderate: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
  severe: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
};

const frequencyColors = {
  common: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
  occasional: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300',
  rare: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
};

const urgencyColors = {
  immediate: 'border-red-500 bg-red-50 dark:bg-red-950/20',
  urgent: 'border-orange-500 bg-orange-50 dark:bg-orange-950/20',
  monitor: 'border-yellow-500 bg-yellow-50 dark:bg-yellow-950/20'
};

export function ConditionSymptoms({
  title = "Signs and Symptoms",
  subtitle,
  symptomCategories,
  warningSigns = [],
  className
}: ConditionSymptomsProps) {
  const deviceInfo = useDeviceDetection();

  return (
    <section className={cn(
      "bg-muted/30",
      deviceInfo.isMobile ? "py-8" : "py-16",
      className
    )}>
      <div className={cn(
        "container",
        deviceInfo.isMobile ? "px-4" : ""
      )}>
        <div className="text-center mb-12">
          <h2 className={cn(
            "font-bold mb-4",
            deviceInfo.isMobile ? "text-2xl" : "text-3xl"
          )}>
            {title}
          </h2>
          {subtitle && (
            <p className="text-muted-foreground max-w-3xl mx-auto">
              {subtitle}
            </p>
          )}
        </div>

        {/* Symptom Categories */}
        <div className="mb-12">
          <div className={cn(
            "grid gap-6",
            deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-1 md:grid-cols-2 lg:grid-cols-3"
          )}>
            {symptomCategories.map((category, index) => {
              const IconComponent = category.icon;
              return (
                <Card key={index} className="h-full">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <IconComponent className="h-6 w-6 text-primary" />
                        <CardTitle className="text-lg">{category.title}</CardTitle>
                      </div>
                      <div className="flex gap-2">
                        {category.severity && (
                          <Badge className={severityColors[category.severity]}>
                            {category.severity}
                          </Badge>
                        )}
                        {category.frequency && (
                          <Badge className={frequencyColors[category.frequency]}>
                            {category.frequency}
                          </Badge>
                        )}
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground mb-4">
                      {category.description}
                    </p>
                    <ul className="space-y-2">
                      {category.symptoms.map((symptom, symptomIndex) => (
                        <li key={symptomIndex} className="text-sm flex items-start gap-2">
                          <span className="text-primary mt-1">•</span>
                          <span>{symptom}</span>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>

        {/* Warning Signs */}
        {warningSigns.length > 0 && (
          <div>
            <div className="flex items-center gap-3 mb-6">
              <AlertTriangle className="h-6 w-6 text-red-500" />
              <h3 className={cn(
                "font-semibold text-red-700 dark:text-red-300",
                deviceInfo.isMobile ? "text-xl" : "text-2xl"
              )}>
                Warning Signs - Seek Medical Attention
              </h3>
            </div>
            
            <div className={cn(
              "grid gap-4",
              deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-1 md:grid-cols-2 lg:grid-cols-3"
            )}>
              {warningSigns.map((warning, index) => (
                <Card 
                  key={index} 
                  className={cn(
                    "border-2",
                    urgencyColors[warning.urgency]
                  )}
                >
                  <CardContent className="pt-4">
                    <div className="flex items-start gap-3">
                      <AlertTriangle className="h-5 w-5 text-red-500 mt-0.5 flex-shrink-0" />
                      <div>
                        <h4 className="font-semibold text-sm mb-1">{warning.sign}</h4>
                        <p className="text-sm text-muted-foreground">{warning.description}</p>
                        <Badge 
                          className={cn(
                            "mt-2",
                            warning.urgency === 'immediate' ? 'bg-red-500 text-white' :
                            warning.urgency === 'urgent' ? 'bg-orange-500 text-white' :
                            'bg-yellow-500 text-black'
                          )}
                        >
                          {warning.urgency === 'immediate' ? 'Call 000' :
                           warning.urgency === 'urgent' ? 'Seek urgent care' :
                           'Monitor closely'}
                        </Badge>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        )}
      </div>
    </section>
  );
}
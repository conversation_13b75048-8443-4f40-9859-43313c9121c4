#!/usr/bin/env node

/**
 * Duplicate Closing Div Fix Script
 * 
 * Finds and fixes duplicate closing div patterns like:
 * </div>        </div>
 * Which should be just:
 * </div>
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🔧 DUPLICATE CLOSING DIV FIX');
console.log('='.repeat(50));

/**
 * Scan directory for React/JSX files
 */
function scanForReactFiles(dir, files = []) {
  const items = fs.readdirSync(dir);
  
  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory()) {
      if (!item.startsWith('.') && item !== 'node_modules' && item !== 'dist') {
        scanForReactFiles(fullPath, files);
      }
    } else if (item.match(/\.(tsx|jsx)$/)) {
      files.push(fullPath);
    }
  }
  
  return files;
}

/**
 * Fix duplicate closing divs in content
 */
function fixDuplicateClosingDivs(content, filePath) {
  let fixed = content;
  let changes = 0;
  const appliedFixes = [];

  // Fix patterns
  const fixes = [
    {
      // Fix: </div>        </div> -> </div>
      from: /<\/div>\s+<\/div>/g,
      to: '</div>',
      desc: 'Fixed duplicate closing </div> tags'
    },
    {
      // Fix: </section>        </div> -> </section>
      from: /<\/section>\s+<\/div>/g,
      to: '</section>',
      desc: 'Fixed duplicate closing </section> and </div> tags'
    },
    {
      // Fix: </main>        </div> -> </main>
      from: /<\/main>\s+<\/div>/g,
      to: '</main>',
      desc: 'Fixed duplicate closing </main> and </div> tags'
    }
  ];

  for (const fix of fixes) {
    const beforeContent = fixed;
    fixed = fixed.replace(fix.from, fix.to);
    
    if (fixed !== beforeContent) {
      changes++;
      appliedFixes.push(fix.desc);
    }
  }

  return { content: fixed, changes, appliedFixes };
}

/**
 * Process a single file
 */
function processFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const relativePath = path.relative(process.cwd(), filePath);
    
    // Check for duplicate closing tags
    const hasDuplicates = content.match(/<\/div>\s+<\/div>/) ||
                         content.match(/<\/section>\s+<\/div>/) ||
                         content.match(/<\/main>\s+<\/div>/);
    
    if (!hasDuplicates) {
      return { processed: false, reason: 'No duplicate closing tags found' };
    }

    console.log(`🔧 Processing: ${relativePath}`);
    
    const result = fixDuplicateClosingDivs(content, filePath);
    
    if (result.changes > 0) {
      // Create backup
      const backupPath = filePath + '.backup';
      fs.copyFileSync(filePath, backupPath);
      
      // Write fixed content
      fs.writeFileSync(filePath, result.content, 'utf8');
      
      console.log(`  ✅ Fixed ${result.changes} issues`);
      result.appliedFixes.forEach(fix => {
        console.log(`    - ${fix}`);
      });
      
      return {
        processed: true,
        changes: result.changes,
        appliedFixes: result.appliedFixes,
        backupPath
      };
    }
    
    return { processed: false, reason: 'No changes needed' };
    
  } catch (error) {
    console.log(`  ❌ Error: ${error.message}`);
    return { processed: false, reason: error.message };
  }
}

/**
 * Main execution function
 */
function main() {
  console.log('📋 Scanning for React/JSX files with duplicate closing tags...');
  
  const reactFiles = scanForReactFiles('src');
  console.log(`Found ${reactFiles.length} React/JSX files`);

  const results = {
    total: reactFiles.length,
    processed: 0,
    skipped: 0,
    errors: 0,
    totalChanges: 0,
    processedFiles: []
  };

  console.log('\n🔧 Processing files...');

  for (const filePath of reactFiles) {
    const result = processFile(filePath);
    
    if (result.processed) {
      results.processed++;
      results.totalChanges += result.changes;
      results.processedFiles.push({
        file: path.relative(process.cwd(), filePath),
        changes: result.changes,
        fixes: result.appliedFixes,
        backup: result.backupPath
      });
    } else {
      if (result.reason.includes('Error')) {
        results.errors++;
      } else {
        results.skipped++;
      }
    }
  }

  // Generate summary
  console.log('\n📊 DUPLICATE CLOSING DIV FIX SUMMARY');
  console.log('='.repeat(40));
  console.log(`Total files scanned: ${results.total}`);
  console.log(`Files processed: ${results.processed}`);
  console.log(`Files skipped: ${results.skipped}`);
  console.log(`Errors: ${results.errors}`);
  console.log(`Total changes made: ${results.totalChanges}`);

  if (results.processed > 0) {
    console.log('\n✅ SUCCESSFULLY PROCESSED FILES:');
    results.processedFiles.forEach(file => {
      console.log(`  • ${file.file} (${file.changes} changes)`);
      file.fixes.forEach(fix => console.log(`    - ${fix}`));
    });
  }

  // Save detailed report
  const report = {
    timestamp: new Date().toISOString(),
    summary: results,
    patternsFixed: [
      'Duplicate closing </div> tags',
      'Duplicate closing </section> and </div> tags',
      'Duplicate closing </main> and </div> tags'
    ],
    recommendations: [
      'Test all fixed components to ensure they render correctly',
      'Review HTML structure for proper nesting',
      'Consider using ESLint rules to prevent duplicate tags',
      'Update development workflow to catch structure errors early'
    ]
  };

  fs.writeFileSync('duplicate-closing-div-fix-report.json', JSON.stringify(report, null, 2));
  console.log('\n📄 Detailed report saved to: duplicate-closing-div-fix-report.json');

  return results;
}

// Run if called directly
if (process.argv[1] && process.argv[1].endsWith('fix-duplicate-closing-divs.js')) {
  main();
}

export { main as fixDuplicateClosingDivs };

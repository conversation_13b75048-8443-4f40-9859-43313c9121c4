import { Check, Send } from 'lucide-react';
import React, { useState } from 'react';

import SafeImage from '@/components/SafeImage';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface FormField {
  name: string;
  label: string;
  type: string;
  placeholder: string;
  required: boolean;
}

interface ContactFormProps {
  title: string;
  image: string;
  description: string;
  fields: FormField[];
  submitText: string;
  successMessage: string;
}

const ContactForm: React.FC<ContactFormProps> = ({
  title,
  image,
  description,
  fields,
  submitText,
  successMessage
}) => {
  const deviceInfo = useDeviceDetection();
  const [formData, setFormData] = useState<Record<string, string>>({});
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // In a real app, this would send the form data to a server
    setIsSubmitted(true);

    // Reset form after 3 seconds
    setTimeout(() => {
      setIsSubmitted(false);
      setFormData({});
    }, 3000);
  };

  const renderField = (field: FormField) => {
    const fieldId = field.name;
    const value = formData[field.name] || '';

    if (field.type === 'textarea') {
      return (
        <div key={field.name} className={cn(
          deviceInfo.isMobile ? "space-y-mobile-sm" : "space-y-2"
        )}>
          <Label
            htmlFor={fieldId}
            className={cn(
              deviceInfo.isMobile ? "mobile-text font-medium" : ""
            )}
          >
            {field.label}
          </Label>
          <Textarea
            id={fieldId}
            name={field.name}
            value={value}
            onChange={handleInputChange}
            placeholder={field.placeholder}
            className={cn(
              deviceInfo.isMobile ? "mobile-input" : ""
            )}
            required={field.required}
            rows={4}
          />
        </div>
      );
    }

    return (
      <div key={field.name} className={cn(
        deviceInfo.isMobile ? "space-y-mobile-sm" : "space-y-2"
      )}>
        <Label
          htmlFor={fieldId}
          className={cn(
            deviceInfo.isMobile ? "mobile-text font-medium" : ""
          )}
        >
          {field.label}
        </Label>
        <Input
          id={fieldId}
          name={field.name}
          type={field.type}
          value={value}
          onChange={handleInputChange}
          placeholder={field.placeholder}
          className={cn(
            deviceInfo.isMobile ? "mobile-input" : ""
          )}
          required={field.required}
        />
      </div>
    );
  };

  return (
    <div className="animate-fade-in [animation-delay:300ms]">
      <h2 className="text-2xl font-bold mb-6">{title}</h2>

      <div className="relative rounded-xl overflow-hidden mb-8 shadow-lg">
        <SafeImage
          src={image}
          alt="miNEURO Consulting Suites Waiting Area"
          className="w-full h-auto"
          fallbackSrc="/images/medical-consulting.jpg"
        />
      </div>

      <p className="text-muted-foreground mb-6">
        {description}
      </p>

      <div className={cn(
        "glass-card",
        deviceInfo.isMobile ? "p-mobile-lg" : "p-6"
      )}>
        {!isSubmitted ? (
          <form
            onSubmit={handleSubmit}
            className={cn(
              deviceInfo.isMobile ? "space-y-mobile-lg" : "space-y-6"
            )}
          >
            <div className={cn(
              deviceInfo.isMobile
                ? "grid grid-cols-1 gap-mobile-md"
                : "grid grid-cols-1 sm:grid-cols-2 gap-4"
            )}>
              {fields.slice(0, 2).map(renderField)}
            </div>

            <div className={cn(
              deviceInfo.isMobile
                ? "grid grid-cols-1 gap-mobile-md"
                : "grid grid-cols-1 sm:grid-cols-2 gap-4"
            )}>
              {fields.slice(2, 4).map(renderField)}
            </div>

            {fields.slice(4).map(renderField)}

            <Button
              type="submit"
              className={cn(
                "w-full",
                deviceInfo.isMobile ? "mobile-button" : ""
              )}
            >
              <Send className="mr-2 h-4 w-4" />
              {submitText}
            </Button>
          </form>
        ) : (
          <div className="text-center py-8">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-green-100 dark:bg-green-900/20 rounded-full mb-4">
              <Check className="h-8 w-8 text-green-600 dark:text-green-400" />
            </div>
            <h3 className="text-xl font-semibold mb-2">Message Sent!</h3>
            <p className="text-muted-foreground">
              {successMessage}
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default ContactForm;

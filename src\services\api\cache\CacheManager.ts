import { CacheEntry, CacheOptions, PersistentCache } from '../types';

/**
 * Local storage implementation of persistent cache
 */
class LocalStorageCache implements PersistentCache {
  private prefix = 'mineuro_cache_';

  async get<T>(key: string): Promise<CacheEntry<T> | null> {
    try {
      const item = localStorage.getItem(this.prefix + key);
      if (!item) return null;
      
      return JSON.parse(item) as CacheEntry<T>;
    } catch {
      return null;
    }
  }

  async set<T>(key: string, entry: CacheEntry<T>): Promise<void> {
    try {
      localStorage.setItem(this.prefix + key, JSON.stringify(entry));
    } catch (error) {
      // Handle quota exceeded or other storage errors
      if (import.meta.env.DEV) {
        console.warn('Failed to store in localStorage:', error);
      }
    }
  }

  async delete(key: string): Promise<void> {
    localStorage.removeItem(this.prefix + key);
  }

  async clear(): Promise<void> {
    const keys = await this.keys();
    keys.forEach(key => localStorage.removeItem(key));
  }

  async keys(): Promise<string[]> {
    const keys: string[] = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key?.startsWith(this.prefix)) {
        keys.push(key);
      }
    }
    return keys;
  }
}

/**
 * Multi-level cache manager with memory and persistent storage
 */
export class CacheManager {
  private memoryCache: Map<string, CacheEntry> = new Map();
  private persistentCache: PersistentCache;
  private maxMemoryEntries: number;
  private cleanupInterval: NodeJS.Timeout;

  constructor(options: {
    maxMemoryEntries?: number;
    cleanupIntervalMs?: number;
    persistentCache?: PersistentCache;
  } = {}) {
    this.maxMemoryEntries = options.maxMemoryEntries || 100;
    this.persistentCache = options.persistentCache || new LocalStorageCache();
    
    // Set up periodic cleanup
    const cleanupInterval = options.cleanupIntervalMs || 5 * 60 * 1000; // 5 minutes
    this.cleanupInterval = setInterval(() => {
      this.cleanup();
    }, cleanupInterval);
  }

  /**
   * Get cached data
   */
  async get<T>(key: string): Promise<T | null> {
    // Check memory cache first
    const memoryEntry = this.memoryCache.get(key);
    if (memoryEntry && !this.isExpired(memoryEntry)) {
      return memoryEntry.data as T;
    }

    // Remove expired entry from memory
    if (memoryEntry && this.isExpired(memoryEntry)) {
      this.memoryCache.delete(key);
    }

    // Check persistent cache
    const persistentEntry = await this.persistentCache.get<T>(key);
    if (persistentEntry && !this.isExpired(persistentEntry)) {
      // Promote to memory cache
      this.setMemoryCache(key, persistentEntry);
      return persistentEntry.data;
    }

    // Remove expired entry from persistent cache
    if (persistentEntry && this.isExpired(persistentEntry)) {
      await this.persistentCache.delete(key);
    }

    return null;
  }

  /**
   * Set cached data
   */
  async set<T>(key: string, data: T, options: CacheOptions = {}): Promise<void> {
    const entry: CacheEntry<T> = {
      data,
      timestamp: Date.now(),
      ttl: options.ttl || 5 * 60 * 1000, // 5 minutes default
      tags: options.tags || []
    };

    // Store in memory cache
    this.setMemoryCache(key, entry);

    // Store in persistent cache if specified
    if (options.persistent) {
      await this.persistentCache.set(key, entry);
    }
  }

  /**
   * Delete cached data
   */
  async delete(key: string): Promise<void> {
    this.memoryCache.delete(key);
    await this.persistentCache.delete(key);
  }

  /**
   * Clear all cached data
   */
  async clear(): Promise<void> {
    this.memoryCache.clear();
    await this.persistentCache.clear();
  }

  /**
   * Invalidate cache by tags
   */
  async invalidateByTags(tags: string[]): Promise<void> {
    // Invalidate memory cache
    for (const [key, entry] of this.memoryCache.entries()) {
      if (entry.tags.some(tag => tags.includes(tag))) {
        this.memoryCache.delete(key);
      }
    }

    // Invalidate persistent cache
    const persistentKeys = await this.persistentCache.keys();
    for (const key of persistentKeys) {
      const entry = await this.persistentCache.get(key);
      if (entry && entry.tags.some(tag => tags.includes(tag))) {
        await this.persistentCache.delete(key);
      }
    }
  }

  /**
   * Get cache statistics
   */
  getStats(): {
    memoryEntries: number;
    memoryHitRate: number;
    totalSize: number;
  } {
    const memoryEntries = this.memoryCache.size;
    
    // Calculate approximate memory usage
    let totalSize = 0;
    for (const entry of this.memoryCache.values()) {
      totalSize += JSON.stringify(entry).length;
    }

    return {
      memoryEntries,
      memoryHitRate: 0, // Would need to track hits/misses
      totalSize
    };
  }

  /**
   * Check if cache entry is expired
   */
  private isExpired(entry: CacheEntry): boolean {
    return Date.now() - entry.timestamp > entry.ttl;
  }

  /**
   * Set entry in memory cache with size management
   */
  private setMemoryCache(key: string, entry: CacheEntry): void {
    // Remove oldest entries if at capacity
    if (this.memoryCache.size >= this.maxMemoryEntries) {
      const oldestKey = this.memoryCache.keys().next().value;
      if (oldestKey) {
        this.memoryCache.delete(oldestKey);
      }
    }

    this.memoryCache.set(key, entry);
  }

  /**
   * Clean up expired entries
   */
  private cleanup(): void {
    // Clean memory cache
    for (const [key, entry] of this.memoryCache.entries()) {
      if (this.isExpired(entry)) {
        this.memoryCache.delete(key);
      }
    }

    // Clean persistent cache (async, don't wait)
    this.cleanupPersistentCache().catch(error => {
      if (import.meta.env.DEV) {
        console.warn('Failed to cleanup persistent cache:', error);
      }
    });
  }

  /**
   * Clean up expired entries in persistent cache
   */
  private async cleanupPersistentCache(): Promise<void> {
    try {
      const keys = await this.persistentCache.keys();
      
      for (const key of keys) {
        const entry = await this.persistentCache.get(key);
        if (entry && this.isExpired(entry)) {
          await this.persistentCache.delete(key);
        }
      }
    } catch (error) {
      if (import.meta.env.DEV) {
        console.warn('Error during persistent cache cleanup:', error);
      }
    }
  }

  /**
   * Destroy cache manager and cleanup resources
   */
  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    this.memoryCache.clear();
  }
}

/**
 * Global cache manager instance
 */
export const cacheManager = new CacheManager({
  maxMemoryEntries: 100,
  cleanupIntervalMs: 5 * 60 * 1000 // 5 minutes
});

/**
 * Cache key utilities
 */
export class CacheKeys {
  static medical = {
    conditions: ['medical', 'conditions'] as const,
    condition: (id: string) => ['medical', 'conditions', id] as const,
    procedures: ['medical', 'procedures'] as const,
    procedure: (id: string) => ['medical', 'procedures', id] as const,
    exercises: ['medical', 'exercises'] as const,
    exercise: (id: string) => ['medical', 'exercises', id] as const
  };

  static appointments = {
    all: ['appointments'] as const,
    slots: (locationId: string) => ['appointments', 'slots', locationId] as const,
    booking: (id: string) => ['appointments', 'booking', id] as const
  };

  static locations = {
    all: ['locations'] as const,
    location: (id: string) => ['locations', id] as const,
    hours: (id: string) => ['locations', id, 'hours'] as const
  };

  /**
   * Convert key array to string
   */
  static toString(key: readonly string[]): string {
    return key.join(':');
  }

  /**
   * Create cache key with parameters
   */
  static withParams(baseKey: readonly string[], params: Record<string, unknown>): string {
    const paramString = Object.entries(params)
      .sort(([a], [b]) => a.localeCompare(b))
      .map(([key, value]) => `${key}=${JSON.stringify(value)}`)
      .join('&');
    
    return paramString ? `${this.toString(baseKey)}?${paramString}` : this.toString(baseKey);
  }
}

import React from 'react';

import IconContainer from './IconContainer';

import { cn } from '@/lib/utils';

interface ContactInfoItemProps {
  icon: React.ReactNode;
  title: string;
  content: string | React.ReactNode;
  layout?: 'horizontal' | 'vertical';
  iconVariant?: 'primary' | 'secondary' | 'muted' | 'accent';
  iconSize?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  titleClassName?: string;
  contentClassName?: string;
}

const ContactInfoItem: React.FC<ContactInfoItemProps> = ({
  icon,
  title,
  content,
  layout = 'horizontal',
  iconVariant = 'primary',
  iconSize = 'md',
  className = '',
  titleClassName = '',
  contentClassName = ''
}) => {
  const getLayoutClass = () => {
    switch (layout) {
      case 'vertical':
        return 'flex-col items-center text-center space-y-3';
      default:
        return 'flex-row items-start space-x-4';
    }
  };

  return (
    <div className={cn(
      'flex',
      getLayoutClass(),
      className
    )}>
      <IconContainer
        icon={icon}
        variant={iconVariant}
        size={iconSize}
      />
      
      <div className={cn(
        layout === 'horizontal' ? 'flex-1' : '',
        layout === 'vertical' ? 'text-center' : ''
      )}>
        <h4 className={cn(
          'font-semibold mb-1',
          titleClassName
        )}>
          {title}
        </h4>
        <div className={cn(
          'text-muted-foreground',
          contentClassName
        )}>
          {content}
        </div>
      </div>
    </div>
  );
};

export default ContactInfoItem;
